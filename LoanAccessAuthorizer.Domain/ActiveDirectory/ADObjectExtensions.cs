namespace LoanAccessAuthorizer.Domain.ActiveDirectory;

public static class ADObjectExtensions
{
    public static bool IsInDomain(this IADObject obj, ADDomain domain) => obj?.Domain == domain;

    public static bool IsInMiCorpDomain(this IADObject obj) => obj.IsInDomain(ADDomain.MiCorp);

    public static bool IsInQCloudDomain(this IADObject obj) => obj.IsInDomain(ADDomain.QCloud);

    public static bool IsInUnknownDomain(this IADObject obj) => obj.IsInDomain(ADDomain.Unknown);
}
