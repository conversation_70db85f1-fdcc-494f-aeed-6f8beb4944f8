namespace LoanAccessAuthorizer.Domain.ActiveDirectory;

public static class ADUserExtensions
{
    public static bool HasRole(this ADUser user, ADRole role)
    {
        return user?.Roles.Contains(role) ?? false;
    }

    public static bool HasRole(this ADUser user, string roleName, ADDomain domain)
    {
        var role = new ADRole(roleName, domain);
        return user.HasRole(role);
    }

    public static bool Has<PERSON>iCorpRole(this ADUser user, string roleName)
    {
        return user.HasRole(roleName, ADDomain.MiCorp);
    }
}
