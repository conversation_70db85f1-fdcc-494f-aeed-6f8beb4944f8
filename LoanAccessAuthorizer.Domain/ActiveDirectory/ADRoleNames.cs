namespace LoanAccessAuthorizer.Domain.ActiveDirectory;

public static class ADRoleNames
{
    public const string IncomeViewerAdmin = "income-viewer-admin";
    public const string DocViewerRoleBanker = "Doc Viewer Role Banker";
    public const string DocViewerRoleIt = "Doc Viewer Role IT";
    public const string TeamPLSos = "Team PS SOS"; // This is NOT a typo. PS stands for "Product Solutions", which was Product Lab's old name
    public const string IVBanking = "IV-Banking";
    public const string IVClosing = "IV-Closing";
    public const string IVUnderwriting = "IV-Underwriting";
    public const string IVSolutionConsultant = "IV-SolutionConsultant";
    public const string MortgageQualifierAdmins = "Mortgage Qualifier Admins";
    public const string IVVendor = "IV-Vendor";
    public const string MortgageQualifierPilot = "Mortgage Qualifier Pilot";
    public const string RLUnderwritingPilot = "RocketLogicUWPilot";
    public const string RLUnderwritingSupport = "RocketLogicUWSupport";
    public const string RLUnderwritingCondo = "RocketLogicUWCondo";
    public const string PQCondo = "PQ- Condo Team";
    public const string PQCUW = "PQ-CUW";
    public const string PQCUWTasks = "PQ-CUWTaskManager";
    public const string PQTitleCommitment = "PQ-TitleCommitment";
    public const string PQAdmin = "PQ-Admin";
    public const string ITTeamTheLand = "IT Team The Land";
    public const string PQPostClose = "PQ-PostClose";
    public const string ITAppraisalTeam1 = "IT Team Pathfinders";
    public const string ITAppraisalTeam2 = "IT Team Rising Phoenix";
    public const string RLClientCareSpecialist = "RL-ClientCareSpecialist";
    public const string RLFedsSpecialist = "RL-FedsSpecialist";
    public const string RLHotlineClosingSpecialist = "RL-HotlineClosingSpecialist";
    public const string RLRefinanceEscalationSpecialist = "RL-RefinanceEscalationSpecialist";
    public const string RLClientAdvocate = "RL-ClientAdvocate";
    public const string RLResolutionSpecialist = "RL-ResolutionSpecialist";
    public const string RLTitleClearingVestingAnalyst = "RL-TitleClearingVestingAnalyst";
    public const string RLFreshStartConsultant = "RL-FreshStartConsultant";
    public const string RLClosingCareRepresentative = "RL-ClosingCareRepresentative";
    public const string RLClosingUnderwriting = "RL-ClosingUnderwriting";
    public const string RLFrontlineSupportSpecialist = "RL-FrontlineSupportSpecialist";
    public const string RLFolderReceivedAnalyst = "RL-FolderReceivedAnalyst";
    public const string RLPropertyVerificationTeam = "RL-PVT";
    public const string RLPurchaseSpecialist = "RL-PurchaseSpecialist";
    public const string RLTitleCoordinator = "RL-TitleCoordinator";
    public const string RLClosingSpecialist = "RL-ClosingSpecialist";
    public const string RLSelfEmploymentLoanAnalyst = "RL-SelfEmploymentLoanAnalyst";
    public const string RLPurchaseAgreementReviewer = "RL-PurchaseAgreementReviewer";
    public const string RLPassport = "Rocket Logic Passport Access";
    public const string PIQInsuranceSpecialist = "PIQ-InsuranceSpecialist";
    public const string ClosedEndSecondBanker = "ClosedEndSecondBanker";
    public const string SelfEmployedAssistBanker = "SelfEmployedAssistBanker";
    public const string FranchiseELOs = "Franchise ELOs";
    public const string ViewTargetProfit = "View Target Profit";
    public const string ITTeamEinstein = "IT Team Einstein";
    public const string ShortageApproveLv2 = "Shortage Approve - Level 2";
    public const string ShortageApproveLv3 = "Shortage Approve - Level 3";
    public const string ShortageApproveLv4 = "Shortage Approve - Level 4";
    public const string ShortageApproveLv5 = "Shortage Approve - Level 5";
    public const string ShortageApproveCapMkt = "Shortage Approve - CapMkt";
    public const string BigSchwabUsers = "BIG-Schwab Users";
    public const string DocViewerRoleSchwab = "Doc Viewer Role Schwab";
    public const string TeamFinalDocs = "Team Final Docs";
    public const string EmAdmin = "em-admin";
    public const string DocViewerRoleMortgageOperations = "Doc Viewer Role Mortgage Operations";
    public const string DocViewerRoleClientExperience = "Doc Viewer Role Client Experience";
    public const string DocViewerRoleCLosing = "Doc Viewer Role Closing";
    public const string DocViewerRoleQlms = "Doc Viewer Role QLMS";
    public const string DocViewerCapitalMarkets = "Doc Viewer Role Capital Markets";
    public const string LoanStudio = "loan-studio-user";
    public const string LoanStudioAdmin = "loan-studio-admin";
    public const string DocViewerRoleLimitedAccessDocuments =
        "Doc Viewer Role Limited Access Documents";
    public const string DocViewerRoleCCS = "Doc Viewer Role CCS";
    public const string DocViewerRoleAccounting = "Doc Viewer Role Accounting";
    public const string DocViewerRoleLeadership = "Doc Viewer Role Leadership";
    public const string DocViewerRoleFOC = "Doc Viewer Role FOC";
    public const string DocViewerRoleTSI = "Doc Viewer Role TSI";
    public const string DocViewerRoleLegal = "Doc Viewer Role Legal";
    public const string DocViewerRoleServicing = "Doc Viewer Role Servicing";
    public const string DocViewerRolePostToRocket = "Doc Viewer Role Post to Rocket";
    public const string DocViewerRoleClientRelations = "Doc Viewer Role Client Relations";
    public const string DocViewerRoleServicingEarlyResolution =
        "Doc Viewer Role Servicing Early Resolution";
    public const string DocViewerRoleServicingPaymentAndReporting =
        "Doc Viewer Role Servicing Payment and Reporting";
    public const string DocViewerRoleServicingPaymentServices =
        "Doc Viewer Role Servicing Payment Services";
    public const string DocViewerRoleOpsVendorTeam = "Doc Viewer Role Ops Vendor Team";
    public const string DocViewerRoleServicingAdministration =
        "Doc Viewer Role Servicing Administration";
    public const string DocViewerRoleReverseVision = "Doc Viewer Role Reverse Vision";
    public const string DocViewerRoleSPS = "Doc Viewer Role SPS";
    public const string DocViewerRoleServicingEscrowAndSpecialLoans =
        "Doc Viewer Role Servicing Escrow and Special Loans";
    public const string DocViewerRoleClientRelationsLeader =
        "Doc Viewer Role Client Relations Leader";
    public const string DocViewerRoleCapitalMarketsPostClosing =
        "Doc Viewer Role Capital Markets Post Closing";
    public const string DocViewerRoleCapitalMarketsCreditRisk =
        "Doc Viewer Role Capital Markets Credit Risk";
    public const string DocViewerRoleServicingSpecialLoans =
        "Doc Viewer Role Servicing Special Loans";
    public const string DocViewerRoleQualityControl = "Doc Viewer Role Quality Control";
    public const string DocViewerRolePurchaseUnderwriting = "Doc Viewer Role Purchase Underwriting";
    public const string DocViewerRoleUnderwriting = "Doc Viewer Role Underwriting";
    public const string DocViewerRoleRefinanceUnderwriter = "Doc Viewer Role Refinance Underwriter";
    public const string DocViewerRoleCapitalMarketsLossMitigation =
        "Doc Viewer Role Capital Markets Loss Mitigation";
    public const string DocViewerRoleServicingRisk = "Doc Viewer Role Servicing Risk";
    public const string DocViewerRoleCapitalMarketsReadOnly =
        "Doc Viewer Role Capital Markets Read Only";
    public const string DocViewerRoleOpsLeadership = "Doc Viewer Role Ops Leadership";
    public const string DocViewerRoleBusinessDevelopment = "Doc Viewer Role Business Development";
    public const string DocViewerRoleServicingRelationshipManagement =
        "Doc Viewer Role Servicing Relationship Management";
    public const string DocViewerRoleServicingAmazement = "Doc Viewer Role Servicing Amazement";
    public const string DocViewerRoleServicingDefaultOperations =
        "Doc Viewer Role Servicing Default Operations";
    public const string DocViewerRoleServicingDefaultCommunications =
        "Doc Viewer Role Servicing Default Communications";
    public const string NonProdTeamMemberLoanAccessADGroup =
        "Retail Team Member Loan Access - Non Prod";
    public const string ProdTeamMemberLoanAccessADGroup = "Retail Team Member Loan Access";
    public const string AMPProductionAccess = "AMP Production Access";
    public const string AllAMPUsers = "All AMP Users";
    public const string ITAll = "IT ALL";
    public const string RLSupport = "RLSupport";
    public const string RFSuperAdmin = "RocketFindingsSuperAdmin";
    public const string OverrideEligibilityStopper = "Override Eligibility Stopper";
    public const string OverrideEligibilityStopperReadOnly =
        "Override Eligibility Stopper Read Only";
    public const string NonProdOverrideEligibilityStopper = "Override Eligibility Stopper NonProd";
}
