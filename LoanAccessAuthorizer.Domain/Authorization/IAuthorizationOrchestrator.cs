using LoanAccessAuthorizer.Domain.Models.Response;

namespace LoanAccessAuthorizer.Domain.Authorization;

public interface IAuthorizationOrchestrator
{
    Task<AccessResult> IsAuthorized(
        string loanIdentifier,
        string applicationIdentifier,
        string commonId = null,
        string rockHumanId = null
    );

    Task<IReadOnlyDictionary<string, bool>> AreAuthorized(
        string loanIdentifier,
        IEnumerable<string> applicationIdentifiers,
        string commonId = null,
        string rockHumanId = null
    );

    Task<IReadOnlyDictionary<string, AccessResult>> AreAuthorizedAccessResults(
        string loanIdentifier,
        IEnumerable<string> applicationIdentifiers,
        string commonId = null,
        string rockHumanId = null
    );

    Task<AuthorizationResponse> GetAuthorizationResponseForApplications(
        string loanIdentifier,
        IEnumerable<string> applicationIdentifier,
        string commonId,
        string ampUsername
    );

    Task<bool> CanClearDecision(string loanIdentifier, string applicationIdentifier);

    IReadOnlyDictionary<string, ApplicationAuthorizationResponse> CreateNoAccessResponseForAppIds(
        IEnumerable<string> appIds
    );
}
