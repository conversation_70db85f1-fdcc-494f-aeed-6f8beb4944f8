namespace LoanAccessAuthorizer.Domain.LoanData.Models;

public class AmpUnderwritingSystemInfo
{
    public AutomatedUnderwritingSystem? UnderwriterSource { get; set; }
    public decimal CashFromClientAtClosingAUSCalc { get; set; }
    public decimal CashToClientAtClosingAUSCalc { get; set; }
    public IEnumerable<AmpAusFinding> AusFinding { get; set; }
    public decimal RequiredReservesAUSCalc { get; set; }
    public bool? ManualDowngrade { get; set; }

}