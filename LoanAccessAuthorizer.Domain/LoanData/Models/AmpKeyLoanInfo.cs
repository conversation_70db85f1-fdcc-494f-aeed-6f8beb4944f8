﻿namespace LoanAccessAuthorizer.Domain.LoanData.Models;

public class AmpKeyLoanInfo
{
    public string LoanNumber { get; set; }
    public string ProductCode { get; set; }
    public string LoanPurpose { get; set; }
    public string LoanChannel { get; set; }
    public DateTime? InitialContactDate { get; set; }
    public int? CurrentLoanStatus { get; set; }
    public bool? IsQLMSLoan { get; set; }
    public int? RefiPlus { get; set; }
    public int? UnderwriterSource { get; set; }
    public string PiggyBacklnNumber { get; set; }
}
