﻿namespace LoanAccessAuthorizer.Domain.LoanData.Models;

public static class EquityBuyoutLienTypes
{
    private const string DivorceSettlement = "divorce settlement";
    private const string InheritedPropertyBuyout = "inherited property buyout";
    private static string[] equityBuyoutTypes = { DivorceSettlement, InheritedPropertyBuyout };

    public static bool HasEquityBuyoutLien(IEnumerable<AmpLiability> liabilities)
    {
        return liabilities.Any(
            l => equityBuyoutTypes.Contains(l.LienType, StringComparer.CurrentCultureIgnoreCase)
        );
    }
}
