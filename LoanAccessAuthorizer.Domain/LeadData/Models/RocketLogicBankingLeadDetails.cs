using LoanAccessAuthorizer.Domain.LeadData.Models;

namespace LoanAccessAuthorizer.Domain.LoanData.Models;

public class RocketLogicBankingLeadDetails
{
    public string LoanNumber { get; set; }
    public bool IsUnsupportedLeadType { get; set; }
    public InitialLoanOriginationSystem InitialLoanOriginationSystem { get; set; }
    public LeadSourceSystem LeadSourceSystem { get; set; }
    public string AssignedToCommonId { get; set; }
    public LoanPurpose? LoanPurpose { get; set; }
    public ApplicationStatus ApplicationStatus { get; set; }
    public bool IsTeamMemberLoan { get; set; }
    public bool RlbCompleted { get; set; }
    public string LeadSourceCategory { get; set; }
    public bool IsAmeripriseLead { get; set; }
    public bool IsSchwabOverride { get; set; }
    public bool IsRelocation { get; set; }
    public bool IsAssumption { get; set; }
    public string LeadTypeCode { get; set; }
}
