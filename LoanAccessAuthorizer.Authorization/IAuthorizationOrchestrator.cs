﻿using System.Collections.Generic;
using System.Threading.Tasks;
using LoanAccessAuthorizer.Domain.Models.Response;

namespace LoanAccessAuthorizer.Authorization
{
    public interface IAuthorizationOrchestrator
    {
        Task<bool> IsAuthorized(string loanIdentifier, string applicationIdentifier);

        Task<IReadOnlyDictionary<string, bool>> AreAuthorized(string loanIdentifier,
            IEnumerable<string> applicationIdentifiers);
        Task<IReadOnlyDictionary<string, ApplicationAuthorizationResponse>> GetAuthorizationResponseForApplications(
            string loanIdentifier, IEnumerable<string> applicationIdentifier, string commonId, string ampUsername);
    }
}
