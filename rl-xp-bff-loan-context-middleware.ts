import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

// Extend the Request interface to include loanId
declare global {
  namespace Express {
    interface Request {
      loanId?: string;
    }
  }
}

@Injectable()
export class LoanContextMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    // Extract loan ID from various sources and set it on the request
    let loanId: string | null = null;

    // Method 1: From URL parameters
    loanId = req.params?.loanId;

    // Method 2: From query parameters
    if (!loanId) {
      loanId = req.query?.loanId as string;
    }

    // Method 3: From custom header
    if (!loanId) {
      loanId = req.headers['x-loan-id'] as string;
    }

    // Method 4: Extract from path patterns
    if (!loanId) {
      const path = req.path;
      const loanIdMatch = path.match(/\/loans\/([^\/]+)/);
      if (loanIdMatch && loanIdMatch[1]) {
        loanId = loanIdMatch[1];
      }
    }

    // Set the loan ID on the request for downstream services
    if (loanId) {
      req.loanId = loanId;
    }

    next();
  }
}

// Updated LoanContextService to use the middleware-set loan ID
@Injectable()
export class LoanContextService {
  constructor(@Inject(REQUEST) private readonly request: Request) {}

  getCurrentLoanId(): string | null {
    return this.request.loanId || null;
  }
}
