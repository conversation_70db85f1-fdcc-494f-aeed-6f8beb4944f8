using LoanAccessAuthorizer.Domain.Models.Response;
using LoanAccessAuthorizer.Authorization.Rules.Authorization.Model;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using LoanAccessAuthorizer.Domain.ActiveDirectory;

namespace LoanAccessAuthorizer.Authorization.Rules.Authorization;

public interface IAccessVerifier
{
    Task<ApplicationAuthorizationResponse> VerifyAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        string appId,
        AccessDecisionContainer accessContainer
    );
}
