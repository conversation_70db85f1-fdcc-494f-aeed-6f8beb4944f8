using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules;

public class RLBLolaAuthorizer : AbstractLeaderAuthorizer, IRLBLolaAuthorizer
{
    public RLBLolaAuthorizer(PilotCheckFactory pilotCheckFactory,
        ILogger<RLBLolaAuthorizer> logger)
        : base(pilotCheckFactory, ApplicationId.RLBLola, logger)
    {
    }

    public async Task<bool> IsInPilot(ADUser user, AccessDecisionContainer accessContainer)
    {
        var (isInPilot, _) = await IsInPilotWithLoanData(user.CommonId, accessContainer);
        return isInPilot;
    }
}
