
using DecisionServices.Core.Metrics.Measurement;

namespace LoanAccessAuthorizer.Authorization.Rules.Instrumentation;

public class CounterMeasurement<T> : IMeasurement
{
    public string Name { get; }
    public Dictionary<string, object> Fields { get; }
    public Dictionary<string, string> Tags { get; }

    public CounterMeasurement(string name, T delta, Dictionary<string, string> tags)
    {
        Name = name;
        Fields = new Dictionary<string, object> {["count"] = delta};
        Tags = tags;
    }
}