using System.Diagnostics.Metrics;
using DecisionServices.Core.Metrics;

namespace LoanAccessAuthorizer.Authorization.Rules.Instrumentation;

public class AccessDecisionMetrics
{
    public void DecisionInvoked(string appId)
    {
        var cm = new CounterMeasurement<int>("decision-invoked", 1, new Dictionary<string, string>{["app_id"] = appId});
        MetricsWriter.WriteMeasurement(cm);
    }

    public void DecisionChecked(string appId)
    {
        var cm = new CounterMeasurement<int>("decision-check", 1, new Dictionary<string, string>{["app_id"] = appId});
        MetricsWriter.WriteMeasurement(cm);
    }
}