namespace LoanAccessAuthorizer.Authorization.Rules;

public static class LoanStatus
{
    public const int InitialContact = 1;
    public const int Application = 10;
    public const int LoanSetupComplete = 20;
    public const int FolderReceived = 21;
    public const int ApprovedPendingClientConditionsOnProperty = 24;
    /// <summary>
    /// Is this a real AMP Loan Status, or just a hack value for testing?
    /// </summary>
    public const int LoanStatus25 = 25;
    public const int ApprovedWaitingForProperty = 26;
    public const int SubmittedToUnderwriting = 30;
    public const int Suspended = 33;
    public const int ApprovedByRocket = 34;
    public const int ConditionallyApproved = 35;
    public const int ReferredByRocket = 36;
    public const int FinalSignoffPendingAction = 40;
    public const int FinalSignoff = 41;
    public const int HudReviewComplete = 42;
    public const int DocsOutToSettlementAgent = 57;
    public const int Closed = 60;
    public const int DraftHonoredWarehoused = 65;
    public const int FileReadyToShip = 70;
    public const int FundedByInvestor = 95;
    public const int Denied = 100;
    public const int Withdrawn = 110;
    public const int LoanRescinded = 111;
    /// <summary>
    /// Is this a real AMP loan status, or just a hack value for testing?
    /// </summary>
    public const int LoanStatus135 = 135;
    public const int FileClosedForIncompleteness = 140;
    public const int ApplicationApprovedNotAccepted = 150;
    public const int LoggedIntoAccounting = 170;
}
