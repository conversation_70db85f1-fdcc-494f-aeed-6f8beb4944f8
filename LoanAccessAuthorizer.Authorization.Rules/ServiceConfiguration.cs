using System.Reflection;
using DecisionServices.Core.Authentication;
using DecisionServices.Core.HttpPolly;
using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.Configuration;
using DecisionServices.QualificationGroupMapper.Factory;
using DocEntityRelationship.Shared.Configuration;
using DocEntityRelationship.Shared.DataAccess;
using LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers;
using LoanAccessAuthorizer.Authorization.Rules.Authorization;
using LoanAccessAuthorizer.Authorization.Rules.Configuration;
using LoanAccessAuthorizer.Authorization.Rules.Features;
using LoanAccessAuthorizer.Authorization.Rules.Instrumentation;
using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot.PilotConfigProvider;
using LoanAccessAuthorizer.Domain.Authorization;
using LoanAccessAuthorizer.RLTasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace LoanAccessAuthorizer.Authorization.Rules;

public static class ServiceConfiguration
{
    public static void AddAuthorizationV2(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddAuthorizationCommon();
        // NOTE: The application id resolver should always be injected as a singleton
        // so we only build the list of application ids once
        services.AddSingleton<ApplicationIdResolver>();
        services.AddScoped<IAuthorizationOrchestrator, AuthorizationOrchestrator>();
        services.AddSingleton<AccessDecisionMetrics>();
        services.AddScoped<AccessDecisionContainerFactory>();

        services.AddStateProvider();
        services.AddPilotConfigProvider(configuration);
        services.AddFeaturesEnabledConfigProvider(configuration);

        // Register Access Deciders
        var accessDeciderInterface = typeof(IAccessDecider);
        var exclusionDeciderInterface = typeof(IExclusionDecider);
        var clearDecisionInterface = typeof(IClearDecision);
        var accessDeciders = Assembly.GetExecutingAssembly()
            .GetTypes()
            .Where(type => type.IsClass && !type.IsAbstract && accessDeciderInterface.IsAssignableFrom(type)
                           && !exclusionDeciderInterface.IsAssignableFrom(type));

        var exclusionDeciders = Assembly.GetExecutingAssembly()
            .GetTypes()
            .Where(type => !typeof(DeciderToExclusionAdapter).IsAssignableFrom(type))
            .Where(type => type.IsClass && !type.IsAbstract && exclusionDeciderInterface.IsAssignableFrom(type));

        var clearDecisionDeciders = Assembly.GetExecutingAssembly()
            .GetTypes()
            .Where(type => type.IsClass && !type.IsAbstract && clearDecisionInterface.IsAssignableFrom(type));

        foreach (var exclusionDecider in exclusionDeciders)
        {
            services.AddScoped(exclusionDecider);
            services.AddScoped(exclusionDeciderInterface, sp => sp.GetRequiredService(exclusionDecider));
        }

        foreach (var accessDecider in accessDeciders)
        {
            services.AddScoped(accessDecider);
            services.AddScoped(exclusionDeciderInterface, sp =>
                new DeciderToExclusionAdapter((IAccessDecider)sp.GetRequiredService(accessDecider)));
        }

        foreach (var clearDecisionDecider in clearDecisionDeciders)
        {
            services.AddScoped(clearDecisionInterface, sp => sp.GetRequiredService(clearDecisionDecider));
        }

        services.AddScoped<IRocketLogicBankingAuthorizer, RocketLogicBankingAuthorizer>();
        services.AddScoped<IRLBLolaAuthorizer, RLBLolaAuthorizer>();
        services.AddScoped<IAmpAuthorizer, AmpAuthorizer>();
        services.AddScoped<IAppDepositFolderingAuthorizer, AppDepositFolderingAuthorizer>();
        services.AddScoped<IRLTasksClient, RLTasksClient>();
    }

    public static void AddRuleAccessVerifier(this IServiceCollection services, string environmentName)
    {
        services.AddSingleton(c => new EnvironmentDetails(GetHostingEnvironment(environmentName)));

        services.AddScoped<IAccessVerifier, CodedRulesApplicationAccessVerifier>();

        var assemblyTypes = typeof(ServiceConfiguration)
            .Assembly
            .GetTypes()
            .Where(p => p.IsClass && !p.IsAbstract)
            .ToList();

        var applicationVerifierTypes = assemblyTypes.Where(p => typeof(IApplicationVerifier).IsAssignableFrom(p));

        foreach (var applicationVerifierType in applicationVerifierTypes)
        {
            services.AddScoped(typeof(IApplicationVerifier), applicationVerifierType);
        }

        var rluSubAppVerifierTypes = assemblyTypes.Where(p => typeof(IRluSubApplicationVerifier).IsAssignableFrom(p));

        foreach (var rluSubAppVerifierType in rluSubAppVerifierTypes)
        {
            services.AddScoped(typeof(IRluSubApplicationVerifier), rluSubAppVerifierType);
        }
    }

    public static void AddDocEntityReleationshipService(this IServiceCollection services,
        AuthenticatedServiceConfig config)
    {
        services.AddScoped<PolicyInfoLoggingHttpLifecycleHook<IDocEntityRelationshipClient>>();
        services.AddDocumentEntityRelationshipService("loan-access-authorizer", config);
    }

    private static void AddAuthorizationCommon(this IServiceCollection services)
    {
        services.AddQualificationFactory();
    }

    private static EnvironmentDetails.HostingEnvironment GetHostingEnvironment(string environment)
    {
        //Defaulting to prod here as it should be the most restrictive
        if (string.IsNullOrEmpty(environment))
        {
            return EnvironmentDetails.HostingEnvironment.Prod;
        }

        environment = environment.ToLower();

        if (environment.Contains("dev"))
        {
            return EnvironmentDetails.HostingEnvironment.Dev;
        }

        if (environment.Contains("test"))
        {
            return EnvironmentDetails.HostingEnvironment.Test;
        }

        if (environment.Contains("beta"))
        {
            return EnvironmentDetails.HostingEnvironment.Beta;
        }

        if (environment.Contains("train"))
        {
            return EnvironmentDetails.HostingEnvironment.Train;
        }

        return EnvironmentDetails.HostingEnvironment.Prod;
    }

    private static void AddStateProvider(this IServiceCollection services)
    {
        services.AddDefaultStateProviderMiddlewareFactories(ServiceLifetime.Scoped)
            .AddStateContainerFactory<StateContainerConfig>(lifetime: ServiceLifetime.Scoped)
            .AddCacheableStateInvalidator(ServiceLifetime.Scoped);

        // Register State Providers
        var stateProviderInterface = typeof(IStateProvider);
        var stateProviders = Assembly.GetExecutingAssembly()
            .GetTypes()
            .Where(type => type.IsClass && !type.IsAbstract && stateProviderInterface.IsAssignableFrom(type));
        foreach (var stateProvider in stateProviders)
        {
            services.AddStateProvider(stateProvider, ServiceLifetime.Scoped);
        }
    }

    private static void AddPilotConfigProvider(this IServiceCollection services,
        IConfiguration configuration)
    {
        services.AddScoped<PilotCheckFactory>(serviceProvider => (type, appId) =>
        {
            IPilotCheck pilotCheck = type switch
            {
                PilotCheckType.Normal => ActivatorUtilities.CreateInstance<PilotCheck>(serviceProvider, appId),
                PilotCheckType.Leader => ActivatorUtilities.CreateInstance<LeaderPilotCheck>(serviceProvider, appId),
                _ => throw new ArgumentException($"Invalid {nameof(PilotCheckType)} '{type}'", nameof(type))
            };

            return pilotCheck;
        });

        var appIds = ApplicationId.All;

        foreach (var appId in appIds)
        {
            var section = configuration.GetSection(appId);
            services.Configure<Dictionary<string, PilotConfiguration>>(appId, section);
        }

        var parAutomatedReviewConfig = configuration.GetSection("ParAutomatedReviewConfig:Config");
        services.Configure<ParAutomatedReviewConfiguration>(parAutomatedReviewConfig);

        var piConfig = configuration.GetSection("PIOptions:Config");
        services.Configure<PIOptions>(piConfig);

        var mcConfig = configuration.GetSection("MCOptions:Config");
        services.Configure<MCOptions>(mcConfig);

        var iqConfig = configuration.GetSection("IQOptions:Config");
        services.Configure<IQOptions>(iqConfig);

        var ampLoanDetailsProviderConfig = configuration.GetSection("AmpLoanDetailsProviderConfig:Config");
        services.Configure<AmpLoanDetailsProviderConfig>(ampLoanDetailsProviderConfig);

        var rocketLogicAssetsTpoConfigurations = configuration.GetSection("RocketLogicAssetsTPO");
        services.Configure<Dictionary<string, RocketLogicAssetsTpoPopulationConfiguration>>(rocketLogicAssetsTpoConfigurations);

        services.AddSingleton<IPilotConfigProvider>(sp =>
            ActivatorUtilities.CreateInstance<ConfigManagerPilotConfigProvider>(sp, (IEnumerable<string>)appIds));
    }

    private static void AddFeaturesEnabledConfigProvider(this IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<Dictionary<string, bool>>(configuration.GetSection("FeaturesEnabled:FeaturesEnabledConfig"));
        services.AddScoped<IFeatureEnabledConfigProvider, FeatureEnabledConfigProvider>();
    }
}
