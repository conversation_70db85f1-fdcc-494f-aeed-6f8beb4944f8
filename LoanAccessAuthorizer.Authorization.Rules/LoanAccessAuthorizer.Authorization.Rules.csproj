<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ProjectGuid>bf92a7d3-c4e3-47bf-bd59-7e9f17df5031</ProjectGuid>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\LoanAccessAuthorizer.AmpJobRunner\LoanAccessAuthorizer.AmpJobRunner.csproj" />
    <ProjectReference Include="..\LoanAccessAuthorizer.CreditQualifier\LoanAccessAuthorizer.CreditQualifier.csproj" />
    <ProjectReference Include="..\LoanAccessAuthorizer.Exclusions\LoanAccessAuthorizer.Exclusions.csproj" />
    <ProjectReference Include="..\LoanAccessAuthorizer.IncomeQualifier\LoanAccessAuthorizer.IncomeQualifier.csproj" />
    <ProjectReference Include="..\LoanAccessAuthorizer.LicensingService\LoanAccessAuthorizer.LicensingService.csproj" />
    <ProjectReference Include="..\LoanAccessAuthorizer.RocketLogicApi\LoanAccessAuthorizer.RocketLogicApi.csproj" />
    <ProjectReference Include="..\LoanAccessAuthorizer.MODS\LoanAccessAuthorizer.MODS.csproj" />
    <ProjectReference Include="..\LoanAccessAuthorizer.ParOrchestrator\LoanAccessAuthorizer.ParOrchestrator.csproj" />
    <ProjectReference Include="..\LoanAccessAuthorizer.PropertyInsuranceQualifier\LoanAccessAuthorizer.PropertyInsuranceQualifier.csproj" />
    <ProjectReference Include="..\LoanAccessAuthorizer.PropertyQualifier\LoanAccessAuthorizer.PropertyQualifier.csproj" />
    <ProjectReference Include="..\LoanAccessAuthorizer.RLTasks\LoanAccessAuthorizer.RLTasks.csproj" />
    <ProjectReference Include="..\LoanAccessAuthorizer.RocketDocs\LoanAccessAuthorizer.RocketDocs.csproj" />
    <ProjectReference Include="..\LoanAccessAuthorizer.RocketLogicBanking\LoanAccessAuthorizer.RocketLogicBanking.csproj" />
    <ProjectReference Include="..\LoanAccessAuthorizer.RocketLogicUnderwriting\LoanAccessAuthorizer.RocketLogicUnderwriting.csproj" />
    <ProjectReference Include="..\LoanAccessAuthorizer.Roles\LoanAccessAuthorizer.Roles.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Include="V2\AccessDecision\AccessDeciders\Pilot\Configs\**" Link="%(RecursiveDir)%(Filename)%(Extension)" CopyToOutputDirectory="PreserveNewest" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AWSSDK.Core" Version="3.7.300.15" />
    <PackageReference Include="DecisionServices.Core.StateProvider" Version="18.0.0" />
    <PackageReference Include="DecisionServices.QualificationGroupMapper.Factory" Version="1.15.0" />
    <PackageReference Include="DecisionServices.Core.Cache" Version="18.0.0" />
    <PackageReference Include="DocEntityRelationship.Shared" Version="5.19.2" />
    <PackageReference Include="DocEntityRelationship.Shared.Model" Version="5.19.2" />
    <PackageReference Include="Property.Domain" Version="6.10.0" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="V2\AccessDecision\AccessDeciders\Pilot\Configs\AMP\beta\" />
    <Folder Include="V2\AccessDecision\AccessDeciders\Pilot\Configs\AMP\development\" />
    <Folder Include="V2\AccessDecision\AccessDeciders\Pilot\Configs\AMP\prod\" />
  </ItemGroup>

</Project>
