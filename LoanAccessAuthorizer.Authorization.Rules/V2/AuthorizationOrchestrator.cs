using LoanAccessAuthorizer.Authorization.Rules.Authorization.Model;
using LoanAccessAuthorizer.Authorization.Rules.Features;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Exceptions;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using LoanAccessAuthorizer.Domain.Authorization;
using LoanAccessAuthorizer.Domain.Exceptions;
using LoanAccessAuthorizer.Domain.LoanData;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using LoanAccessAuthorizer.Domain.Models;
using LoanAccessAuthorizer.Domain.Models.Response;
using LoanAccessAuthorizer.Domain.VerifierExclusions;
using LoanAccessAuthorizer.Roles;
using Microsoft.Extensions.Logging;
using Serilog.Context;

namespace LoanAccessAuthorizer.Authorization.Rules.V2;

public class AuthorizationOrchestrator : IAuthorizationOrchestrator
{
    private readonly ILoanDataClient _loanData;
    private readonly Authorizer _authorizer;
    private readonly AccessDecisionContainerFactory _accessDecisionContainerFactory;
    private readonly ILogger<AuthorizationOrchestrator> _logger;
    private readonly IUserRetriever _userRetriever;
    private readonly IReadOnlyDictionary<string, IClearDecision> _clearDecisionCheckers;

    private readonly ADRole _teamMemberLoanAccessADGroup;

    private readonly bool _enableRocketLogicAccessCheck;
    private readonly bool _enableTeamMemberClockedInStatus;

    private static readonly IEnumerable<ADRole> _rocketLogicAccessADGroups = new ADRole[]
    {
        (ADRoleNames.IncomeViewerAdmin, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleIt, ADDomain.MiCorp),
        (ADRoleNames.TeamPLSos, ADDomain.MiCorp),
        (ADRoleNames.IVSolutionConsultant, ADDomain.MiCorp),
        (ADRoleNames.IVBanking, ADDomain.MiCorp),
        (ADRoleNames.IVClosing, ADDomain.MiCorp),
        (ADRoleNames.IVUnderwriting, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleMortgageOperations, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleClientExperience, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleCLosing, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleQlms, ADDomain.MiCorp),
        (ADRoleNames.DocViewerCapitalMarkets, ADDomain.MiCorp),
        (ADRoleNames.IVVendor, ADDomain.MiCorp),
        (ADRoleNames.RLClientCareSpecialist, ADDomain.MiCorp),
        (ADRoleNames.RLClientAdvocate, ADDomain.MiCorp),
        (ADRoleNames.RLResolutionSpecialist, ADDomain.MiCorp),
        (ADRoleNames.RLTitleClearingVestingAnalyst, ADDomain.MiCorp),
        (ADRoleNames.RLFreshStartConsultant, ADDomain.MiCorp),
        (ADRoleNames.RLRefinanceEscalationSpecialist, ADDomain.MiCorp),
        (ADRoleNames.RLHotlineClosingSpecialist, ADDomain.MiCorp),
        (ADRoleNames.RLFedsSpecialist, ADDomain.MiCorp),
        (ADRoleNames.RLClosingCareRepresentative, ADDomain.MiCorp),
        (ADRoleNames.RLClosingUnderwriting, ADDomain.MiCorp),
        (ADRoleNames.RLFrontlineSupportSpecialist, ADDomain.MiCorp),
        (ADRoleNames.RLFolderReceivedAnalyst, ADDomain.MiCorp),
        (ADRoleNames.RLPurchaseSpecialist, ADDomain.MiCorp),
        (ADRoleNames.RLTitleCoordinator, ADDomain.MiCorp),
        (ADRoleNames.RLClosingSpecialist, ADDomain.MiCorp),
        (ADRoleNames.RLSelfEmploymentLoanAnalyst, ADDomain.MiCorp),
        (ADRoleNames.RLPurchaseAgreementReviewer, ADDomain.MiCorp),
        (ADRoleNames.PQAdmin, ADDomain.MiCorp),
        (ADRoleNames.PQCondo, ADDomain.MiCorp),
        (ADRoleNames.PQTitleCommitment, ADDomain.MiCorp),
        (ADRoleNames.PQPostClose, ADDomain.MiCorp),
        (ADRoleNames.RLUnderwritingCondo, ADDomain.MiCorp),
        (ADRoleNames.RLUnderwritingSupport, ADDomain.MiCorp),
        (ADRoleNames.RLUnderwritingPilot, ADDomain.MiCorp),
        (ADRoleNames.TeamFinalDocs, ADDomain.MiCorp),
        (ADRoleNames.PIQInsuranceSpecialist, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleBanker, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleLimitedAccessDocuments, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleCCS, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleAccounting, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleLeadership, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleFOC, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleTSI, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleLegal, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleServicing, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRolePostToRocket, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleClientRelations, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleServicingEarlyResolution, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleServicingPaymentAndReporting, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleServicingPaymentServices, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleOpsVendorTeam, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleServicingAdministration, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleReverseVision, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleSPS, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleServicingEscrowAndSpecialLoans, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleClientRelationsLeader, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleCapitalMarketsPostClosing, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleCapitalMarketsCreditRisk, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleServicingSpecialLoans, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleQualityControl, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRolePurchaseUnderwriting, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleUnderwriting, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleRefinanceUnderwriter, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleCapitalMarketsLossMitigation, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleServicingRisk, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleCapitalMarketsReadOnly, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleOpsLeadership, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleBusinessDevelopment, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleServicingRelationshipManagement, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleServicingAmazement, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleServicingDefaultOperations, ADDomain.MiCorp),
        (ADRoleNames.DocViewerRoleServicingDefaultCommunications, ADDomain.MiCorp),
        (ADRoleNames.AMPProductionAccess, ADDomain.MiCorp),
        (ADRoleNames.AllAMPUsers, ADDomain.MiCorp),
    };

    public AuthorizationOrchestrator(
        ILoanDataClient loanData,
        Authorizer authorizer,
        AccessDecisionContainerFactory accessDecisionContainerFactory,
        ILogger<AuthorizationOrchestrator> logger,
        IEnumerable<IClearDecision> clearDecisionCheckers,
        IUserRetriever userRetriever,
        IFeatureEnabledConfigProvider featureEnabledConfigProvider,
        EnvironmentInfo env
    )
    {
        _loanData = loanData;
        _authorizer = authorizer;
        _accessDecisionContainerFactory = accessDecisionContainerFactory;
        _logger = logger;
        _userRetriever = userRetriever;
        _clearDecisionCheckers = clearDecisionCheckers.ToDictionary(c => c.AccessDecisionId);

        var isProduction = env.EnvironmentName == EnvironmentInfo.Prod;
        var isBeta = env.EnvironmentName == EnvironmentInfo.Beta;

        var teamMemberAccessADGroupName = isProduction
            ? ADRoleNames.ProdTeamMemberLoanAccessADGroup
            : ADRoleNames.NonProdTeamMemberLoanAccessADGroup;
        _teamMemberLoanAccessADGroup = (teamMemberAccessADGroupName, ADDomain.MiCorp);

        _enableRocketLogicAccessCheck = featureEnabledConfigProvider.IsFeatureEnabled(
            FeatureName.CheckRocketLogicADGroups
        );
        _enableTeamMemberClockedInStatus = featureEnabledConfigProvider.IsFeatureEnabled(
            FeatureName.TeamMemberClockedInStatus
        );

        if (isBeta)
        {
            _rocketLogicAccessADGroups.Append((ADRoleNames.RLPassport, ADDomain.MiCorp));
        }
    }

    public Task<AccessResult> IsAuthorized(
        string loanIdentifier,
        string applicationIdentifier,
        string commonId = null,
        string rockHumanId = null
    )
    {
        var accessDecisionContainer = CreateAccessDecisionContainer(
            loanIdentifier,
            commonId,
            rockHumanId
        );
        return IsLoanAuthorized(accessDecisionContainer, applicationIdentifier, false);
    }

    public async Task<IReadOnlyDictionary<string, bool>> AreAuthorized(
        string loanIdentifier,
        IEnumerable<string> applicationIdentifiers,
        string commonId = null,
        string rockHumanId = null
    )
    {
        var accessDecisionContainer = CreateAccessDecisionContainer(
            loanIdentifier,
            commonId,
            rockHumanId
        );

        var appIdAndIsAuthorized = await Task.WhenAll(
            applicationIdentifiers.Select(async id =>
                (id, IsAuthorized: await IsLoanAuthorized(accessDecisionContainer, id, false))
            )
        );

        return appIdAndIsAuthorized.ToDictionary(
            item => item.id,
            item => item.IsAuthorized.AccessDecision
        );
    }

    public async Task<IReadOnlyDictionary<string, AccessResult>> AreAuthorizedAccessResults(
        string loanIdentifier,
        IEnumerable<string> applicationIdentifiers,
        string commonId = null,
        string rockHumanId = null
    )
    {
        var accessDecisionContainer = CreateAccessDecisionContainer(
            loanIdentifier,
            commonId,
            rockHumanId
        );
        return (
            await Task.WhenAll(
                applicationIdentifiers.Select(async id =>
                    (id, AccessResult: await IsLoanAuthorized(accessDecisionContainer, id, false))
                )
            )
        ).ToDictionary();
    }

    public async Task<AuthorizationResponse> GetAuthorizationResponseForApplications(
        string loanIdentifier,
        IEnumerable<string> applicationIdentifier,
        string commonId,
        string ampUsername
    )
    {
        var appIds = applicationIdentifier.ToList();
        var accessContainer = CreateAccessDecisionContainer(loanIdentifier, commonId);

        var numericCommonId = int.Parse(commonId);

        var ampLoanDetailsTask = GetAmpLoanDetails(accessContainer);
        var userTask = _userRetriever.GetADUserByCommonId(numericCommonId);

        var ampLoanDetails = await ampLoanDetailsTask;
        var user = await userTask;

        if (user is null)
        {
            _logger.LogWarning("User not found in Active Directory.");
            return new AuthorizationResponse(
                false,
                CreateNoAccessResponseForAppIds(appIds),
                AuthorizationErrorCode.UserNotFoundInActiveDirectory
            );
        }

        // Add additional context for troubleshooting multi-domain (e.g. QCloud vs MiCorp) user issues.
        using var samAccountNameContext = LogContext.PushProperty(
            "SamAcountName",
            user.SamAccountName
        );
        using var userDomainContext = LogContext.PushProperty("UserDomain", user.Domain);

        try
        {
            var noAccessAppIds = Enumerable.Empty<(string id, ApplicationAuthorizationResponse)>();
            var userAccessResult = await CanUserAccessLoan(
                loanIdentifier,
                ampLoanDetails,
                accessContainer,
                ampUsername,
                user
            );
            if (!userAccessResult.IsUserAuthorized)
            {
                // LOLA will interpret a false response to mean that the loan should be worked in RLB.
                // If the user doesn't have access to RLB, this will prevent them from working the loan.
                // Instead, run the LOLA application verifier as normal but return no access for any
                // other applications in the request.
                // We will revisit this logic once RLB expands to all TM bankers.
                if (appIds.Contains(ApplicationId.LOLA))
                {
                    noAccessAppIds = appIds
                        .Where(appId => appId != ApplicationId.LOLA)
                        .Select(appId =>
                            (
                                appId,
                                new ApplicationAuthorizationResponse(
                                    false,
                                    false,
                                    new VerifierExclusion
                                    {
                                        Reason = VerifierExclusionReason.UserNotAuthorized,
                                        Comment = $"User is not authorized to use {appId}",
                                    }
                                )
                            )
                        );
                    appIds = new List<string> { ApplicationId.LOLA };
                }
                else
                {
                    return new AuthorizationResponse(
                        false,
                        CreateNoAccessResponseForAppIds(appIds),
                        userAccessResult.AuthorizationErrorCode
                    );
                }
            }

            var appIdsAndIsAuthorized = await Task.WhenAll(
                appIds.Select(async id =>
                    (id, IsAuthorized: await IsLoanAuthorized(accessContainer, id, true))
                )
            );

            var verificationInput = await GetVerificationDecisionInput(
                accessContainer,
                user,
                loanIdentifier,
                ampLoanDetails
            );

            var appIdAndUserAccess = await Task.WhenAll(
                appIdsAndIsAuthorized.Select(async item =>
                    item.IsAuthorized.AccessDecision
                        ? (
                            item.id,
                            await _authorizer.GetAccessByApplication(
                                item.id,
                                user,
                                verificationInput,
                                accessContainer
                            )
                        )
                        : (
                            item.id,
                            new ApplicationAuthorizationResponse(
                                false,
                                false,
                                new VerifierExclusion
                                {
                                    Reason = VerifierExclusionReason.NotInPilot,
                                    Comment = $"User: {user.CommonId} is not in {item.id} pilot",
                                }
                            )
                        )
                )
            );

            return new AuthorizationResponse(
                true,
                appIdAndUserAccess
                    .Concat(noAccessAppIds)
                    .ToDictionary(item => item.id, item => item.Item2)
            );
        }
        catch (Exception ex)
        {
            // Log the error here so we have the additional context of the SamAccountName and UserDomain attached to
            // the error log record. After this method exits, we'll only have the commonId to troubleshoot with. That
            // won't be impossible to work with, but it will make it more difficult to identify the domain user if it
            // is not in the MiCorp domain. QCloud domin users don't show up in qverify/.
            _logger.LogError(ex, "Unexpected error checking user access.");
            throw;
        }
    }

    private async Task<AmpKeyLoanInfo> GetAmpLoanDetails(AccessDecisionContainer accessContainer)
    {
        try
        {
            return await accessContainer.StateContainer.Get<AmpKeyLoanInfo>();
        }
        catch (Exception ex)
            when (ex is RequestNotFoundException || ex is LoanDoesNotExistInAmpException)
        {
            return null;
        }
    }

    private async Task<UserAccessResult> CanUserAccessLoan(
        string loanIdentifier,
        AmpKeyLoanInfo ampLoanDetails,
        AccessDecisionContainer accessContainer,
        string ampUsername,
        ADUser user
    )
    {
        if (_enableTeamMemberClockedInStatus)
        {
            var isTeamMemberClockedIn = await GetClockedInStatus(accessContainer);

            if (!isTeamMemberClockedIn)
            {
                _logger.LogWarning("User is not clocked in.");
                return new UserAccessResult(false, AuthorizationErrorCode.UserIsClockedOut);
            }
        }

        var isInMiCorpDomain = user.IsInMiCorpDomain();
        if (
            _enableRocketLogicAccessCheck
            && isInMiCorpDomain
            && !user.Roles.Overlaps(_rocketLogicAccessADGroups)
        )
        {
            _logger.LogInformation(
                "Preventing access to {loanIdentifier} since {ampUserName} does not have access to Rocket Logic",
                loanIdentifier,
                ampUsername
            );
            return new UserAccessResult(
                false,
                AuthorizationErrorCode.UserNotAllowedToAccessRocketLogic
            );
        }

        // Defer to Amp for loans that are in Amp, since there are some cases not supported by
        // RLB yet (such as if the lead doesn't indicate it's a TM loan, but the employer on
        // the loan is in the FOC).
        if (ampLoanDetails is not null)
        {
            var ampUserAccess = await _loanData.CanUserAccessLoan(loanIdentifier, ampUsername);
            return new UserAccessResult(
                ampUserAccess,
                ampUserAccess ? null : AuthorizationErrorCode.UserDoesNotHaveAccessToLoanInAmp
            );
        }

        // Only MI Corp domain users can access non-schwab loans.
        // QCloud/Schwab users may not access non-Schwab loans.
        // We can't tell if a loan is a Schwab loan pre-amp. So, check the user.
        if (!isInMiCorpDomain)
        {
            var message =
                "Preventing access to {loanIdentifier} since {ampUsername} is a QCloud/Schawb user, "
                + "and we can't tell whether a pre-amp loan is a Schwab loan.";
            _logger.LogInformation(message, loanIdentifier, ampUsername);
            return new UserAccessResult(false, AuthorizationErrorCode.ExternalUser);
        }

        if (user.HasRole(_teamMemberLoanAccessADGroup))
        {
            return new UserAccessResult(true);
        }

        var rlbLeadDetails =
            await accessContainer.StateContainer.Get<RocketLogicBankingLeadDetails>();
        var isTeamMemberLoan = rlbLeadDetails?.IsTeamMemberLoan ?? false;

        if (isTeamMemberLoan)
        {
            _logger.LogInformation(
                "Preventing access to {loanIdentifier} since the RLB lead indicates it is a Team Member loan and {ampUsername} does not have access to Team Member loans",
                loanIdentifier,
                ampUsername
            );
        }

        return new UserAccessResult(
            !isTeamMemberLoan,
            isTeamMemberLoan ? AuthorizationErrorCode.UserNotAllowedToAccessTeamMemberLoans : null
        );
    }

    private AccessDecisionContainer CreateAccessDecisionContainer(
        string loanIdentifier,
        string commonId = null,
        string rockHumanId = null
    )
    {
        var initialState = new InitialLoanState
        {
            LoanNumber = loanIdentifier,
            CallerId =
                commonId != null ? UserId.FromCommonId(commonId)
                : rockHumanId != null ? UserId.FromRockHumanId(rockHumanId)
                : null,
        };

        return _accessDecisionContainerFactory.CreateAccessDecisionContainerFromInitialState(
            initialState
        );
    }

    private async Task<AccessResult> IsLoanAuthorized(
        AccessDecisionContainer accessDecisionContainer,
        string applicationIdentifier,
        bool defaultIfAuthorizerNotFound
    )
    {
        try
        {
            return await accessDecisionContainer.GetAccessDecision(applicationIdentifier);
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "Error occured getting access for {applicationIdentifier}, defaulting to {defaultIfAuthorizerNotFound}",
                applicationIdentifier,
                defaultIfAuthorizerNotFound
            );
            return new AccessResult(defaultIfAuthorizerNotFound, Enumerable.Empty<string>());
        }
    }

    public IReadOnlyDictionary<
        string,
        ApplicationAuthorizationResponse
    > CreateNoAccessResponseForAppIds(IEnumerable<string> appIds)
    {
        return appIds.ToDictionary(
            id => id,
            id => new ApplicationAuthorizationResponse(false, false)
        );
    }

    private async Task<VerificationDecisionInputData> GetVerificationDecisionInput(
        AccessDecisionContainer accessContainer,
        ADUser user,
        string loanIdentifier,
        AmpKeyLoanInfo ampLoanDetails
    )
    {
        if (ampLoanDetails == null)
        {
            return new VerificationDecisionInputData
            {
                LoanDetails = new AmpKeyLoanInfo { LoanNumber = loanIdentifier },
            };
        }

        var inputData = new VerificationDecisionInputData { LoanDetails = ampLoanDetails };
        await TryAppendRluwTrails(user, inputData, accessContainer);

        return inputData;
    }

    private async Task TryAppendRluwTrails(
        ADUser user,
        VerificationDecisionInputData data,
        AccessDecisionContainer accessContainer
    )
    {
        // first, check loan pilot access
        var rluwCreditLoanAccessTask = accessContainer.GetAccessDecision(
            ApplicationId.RocketLogicUnderwritingCredit
        );
        var rluwTpoLoanAccessTask = accessContainer.GetAccessDecision(
            ApplicationId.RocketLogicUnderwritingTpo
        );
        var rluwSchwabLoanAccessTask = accessContainer.GetAccessDecision(
            ApplicationId.RocketLogicUnderwritingSchwab
        );

        var rluwCreditLoanAccess = (await rluwCreditLoanAccessTask).AccessDecision;
        var rluwTpoLoanAccess = (await rluwTpoLoanAccessTask).AccessDecision;
        var rluwSchwabLoanAccess = (await rluwSchwabLoanAccessTask).AccessDecision;
        var rluwLoanAccess = rluwCreditLoanAccess || rluwTpoLoanAccess || rluwSchwabLoanAccess;

        // next, check user pilot access
        if (rluwLoanAccess)
        {
            var appIds = new List<string>();
            if (rluwCreditLoanAccess)
            {
                appIds.Add(ApplicationId.RocketLogicUnderwritingCredit);
            }
            if (rluwTpoLoanAccess)
            {
                appIds.Add(ApplicationId.RocketLogicUnderwritingTpo);
            }
            if (rluwSchwabLoanAccess)
            {
                appIds.Add(ApplicationId.RocketLogicUnderwritingSchwab);
            }

            var userAuthorization = await _authorizer.GetAccessByApplications(
                appIds,
                user,
                data,
                accessContainer
            );
            var rluwUserAccess = userAuthorization.Values.Any(auth => auth.Read);
            _logger.LogInformation(
                "Read access to Rocket Logic Underwriting is: {readAccess}",
                rluwUserAccess
            );

            if (rluwUserAccess)
            {
                var rluTrailSet =
                    await accessContainer.StateContainer.Get<RocketLogicUnderwritingVisitedTrailSet>();
                data.RocketLogicUnderwritingVisitedTrails = rluTrailSet;
            }
        }
    }

    public Task<bool> CanClearDecision(string loanIdentifier, string applicationIdentifier)
    {
        var accessDecisionContainer = CreateAccessDecisionContainer(loanIdentifier);
        return _clearDecisionCheckers.TryGetValue(applicationIdentifier, out var checker)
            ? checker.CanClearDecision(accessDecisionContainer)
            : Task.FromResult(true);
    }

    private async Task<bool> GetClockedInStatus(AccessDecisionContainer accessContainer)
    {
        try
        {
            return await accessContainer.StateContainer.Get<bool>(
                ExplicitStateProviderIds.IsTeamMemberClockedIn
            );
        }
        catch (Exception ex) when (ex is RequestNotFoundException)
        {
            return true;
        }
    }
}
