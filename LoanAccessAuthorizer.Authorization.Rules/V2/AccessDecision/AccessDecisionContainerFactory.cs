using DecisionServices.Core.StateProvider;
using LoanAccessAuthorizer.Authorization.Rules.Instrumentation;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Exclusions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;

public class AccessDecisionContainerFactory
{
    public StateContainerFactory<StateContainerConfig> StateContainerFactory { get; }
    private readonly IDictionary<string, IExclusionDecider> _idToAccessDecider;
    private readonly IServiceProvider _serviceProvider;
    private readonly AccessDecisionMetrics _metrics;

    public AccessDecisionContainerFactory(
        IEnumerable<IExclusionDecider> accessDeciders,
        StateContainerFactory<StateContainerConfig> stateContainerFactory,
        IServiceProvider serviceProvider,
        AccessDecisionMetrics metrics
    )
    {
        StateContainerFactory = stateContainerFactory;
        _serviceProvider = serviceProvider;
        _idToAccessDecider = accessDeciders.ToDictionary(
            accessDecider => accessDecider.AccessDecisionId,
            accessDecider => accessDecider,
            StringComparer.InvariantCultureIgnoreCase
        );
        _metrics = metrics;
    }

    public virtual AccessDecisionContainer
        CreateAccessDecisionContainerFromInitialState(InitialLoanState initialState)
    {
        var config = new StateContainerConfig
        {
            InitialData = new[] { initialState }
        };
        var stateContainer = StateContainerFactory.CreateStateContainer(config);
        var decisionRepo = _serviceProvider.GetRequiredService<IApplicationAuthorizerDecisionStore>();
        var exclusionsRepo = _serviceProvider.GetRequiredService<IExclusionsRepository>();
        return new AccessDecisionContainer(_idToAccessDecider, stateContainer, decisionRepo, exclusionsRepo, _metrics);
    }
}
