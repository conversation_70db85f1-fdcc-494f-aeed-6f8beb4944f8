using System.Collections.ObjectModel;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;

public class ExclusionDecision : Collection<Exclusion>
{
    public bool ShouldPersist { get; }
    public bool IsExcluded => Count > 0;
    public IEnumerable<string> Features { get; } = Enumerable.Empty<string>();

    public ExclusionDecision() : this(new List<Exclusion>(), Enumerable.Empty<string>()) { }

    public ExclusionDecision(bool persist) : this(new List<Exclusion>(), Enumerable.Empty<string>(), persist) { }

    public ExclusionDecision(bool persist, IEnumerable<string> features) : this(new List<Exclusion>(), features, persist) { }

    public ExclusionDecision(IList<Exclusion> exclusions, bool persist = true)
        : this(exclusions ?? new List<Exclusion>(), Enumerable.Empty<string>(), persist) { }

    public ExclusionDecision(
        IList<Exclusion> exclusions,
        IEnumerable<string> features,
        bool persist = true)
        : base(exclusions ?? new List<Exclusion>())
    {
        ShouldPersist = persist;
        Features = features;
    }

    public void Add(ExclusionReason exclusionReason, Type excludedByType, string condition = null)
    {
        var comment = $"Excluded by {excludedByType.Name}";

        if (!string.IsNullOrWhiteSpace(condition))
        {
            comment += $" ({condition})";
        }
        this.Add(
            new Exclusion
            {
                Reason = exclusionReason,
                Comment = comment,
                ExcludedAt = DateTime.UtcNow
            }
        );
    }

    public void ExtendOrAdd(ExclusionReason reason, Type excludedByType, string condition = null)
    {
        var exclusion = this.FirstOrDefault(e => e.Reason == reason);
        if (exclusion != null)
        {
            exclusion.Comment += $"|({condition})";
            return;
        }
        this.Add(reason, excludedByType, condition);
    }
}
