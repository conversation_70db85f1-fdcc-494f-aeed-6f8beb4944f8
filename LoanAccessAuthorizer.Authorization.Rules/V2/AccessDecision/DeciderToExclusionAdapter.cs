using LoanAccessAuthorizer.Domain.PilotExclusions.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;

public class DeciderToExclusionAdapter : IExclusionDecider
{
    private readonly IAccessDecider _decider;

    public DeciderToExclusionAdapter(IAccessDecider decider)
    {
        _decider = decider;
    }

    public string AccessDecisionId => _decider.AccessDecisionId;

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var exclusionDecision = new ExclusionDecision(false);

        if (!await _decider.GetAccessDecision(container))
        {
            exclusionDecision.Add(
                ExclusionReason.DefaultDeciderExclusion,
                GetType(),
                $"default fallback exclusion from {_decider.GetType().Name}"
            );
        }

        return exclusionDecision;
    }
}
