using System.Collections.Concurrent;
using DecisionServices.Core.StateProvider;
using LoanAccessAuthorizer.Authorization.Rules.Instrumentation;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.Models.Response;
using LoanAccessAuthorizer.Exclusions;
using LoanAccessAuthorizer.Exclusions.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;

public class AccessDecisionContainer
{
    private const int ConcurrencyLevel = 4;
    public StateContainer StateContainer { get; }
    private readonly IDictionary<string, IExclusionDecider> _idToAccessDecider;
    private readonly IExclusionsRepository _exclusionsRepo;
    private readonly AccessDecisionMetrics _metrics;
    private readonly IApplicationAuthorizerDecisionStore _decisionsRepo;
    private readonly ConcurrentDictionary<string, Lazy<Task<AccessResult>>> _idToGetDecisionTask;

    public AccessDecisionContainer(
        IDictionary<string, IExclusionDecider> accessDeciders,
        StateContainer stateContainer,
        IApplicationAuthorizerDecisionStore decisionsRepo,
        IExclusionsRepository exclusionRepo,
        AccessDecisionMetrics metrics
    )
    {
        _decisionsRepo = decisionsRepo;
        _idToAccessDecider = accessDeciders;
        StateContainer = stateContainer;
        _idToGetDecisionTask = new ConcurrentDictionary<string, Lazy<Task<AccessResult>>>(
            ConcurrencyLevel,
            _idToAccessDecider.Count
        );
        _exclusionsRepo = exclusionRepo;
        _metrics = metrics;
    }

    public virtual Task<AccessResult> GetAccessDecision(string id)
    {
        return GetGetDecisionTask(id);
    }

    private Task<AccessResult> GetGetDecisionTask(string id)
    {
        _metrics.DecisionChecked(id);
        return _idToGetDecisionTask
            .GetOrAdd(id, key => new Lazy<Task<AccessResult>>(() => GetAccessDecisionFromDeciderTask(key)))
            .Value;
    }

    private async Task<AccessResult> GetAccessDecisionFromDeciderTask(string id)
    {
        var decider = GetAccessDecider(id);

        var exclusionInfo = await StateContainer.Get<ExclusionInformation>();
        if (exclusionInfo?.HasExclusions(id) ?? false)
        {
            var appFeatures = exclusionInfo.GetApplicationFeatures(id);
            return new AccessResult(!exclusionInfo.IsExcluded(id), appFeatures);
        }

        _metrics.DecisionInvoked(decider.AccessDecisionId);
        var decision = await decider.GetExclusionDecision(this);

        if (decision.ShouldPersist)
        {
            var exclusions = decision
                .GroupBy(exclusion => exclusion.Reason)
                .Select(group =>
                {
                    var exclusion = group.First();
                    return new Exclusions.Models.Exclusion
                    {
                        AppId = decider.AccessDecisionId,
                        Reason = exclusion.Reason,
                        Comment = exclusion.Comment,
                        ExcludedAt = exclusion.ExcludedAt,
                        User = exclusion.User,
                    };
                });
            if (!exclusions.Any())
            {
                exclusions = new[]
                {
                    new Exclusions.Models.Exclusion
                    {
                        AppId = decider.AccessDecisionId,
                        Reason = null,
                        ExcludedAt = DateTime.Now,
                        Features = decision.Features,
                    }
                };
            }

            var initialState = await StateContainer.Get<InitialLoanState>();

            // Store the decision so that the DynamoDB stream can fire one event per decision. There can be multiple
            // exclusions per decision. Events only fired for stored exclusions would result in duplicate processing.
            await _decisionsRepo.StoreDecision(initialState.LoanNumber, id, !decision.IsExcluded);
            await _exclusionsRepo.PutExclusions(initialState.LoanNumber, exclusions);
        }

        return new AccessResult(!decision.IsExcluded, decision.Features);
    }

    private IExclusionDecider GetAccessDecider(string id)
    {
        if (!_idToAccessDecider.TryGetValue(id, out var decider))
        {
            throw new Exception($"No access decider for id {id} found");
        }

        return decider;
    }
}
