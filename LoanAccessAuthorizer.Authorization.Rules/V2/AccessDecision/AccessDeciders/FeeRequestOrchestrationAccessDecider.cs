using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;
public class FeeRequestOrchestrationAccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.FeeRequestOrchestration;

    private readonly IPilotCheck _pilotCheck;

    public FeeRequestOrchestrationAccessDecider(PilotCheckFactory pilotCheckFactory)
    {
        _pilotCheck = pilotCheckFactory(PilotCheckType.Normal, AccessDecisionId);
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var exclusions = new List<Exclusion>();
        var rocketLogicAccessTask = container.GetAccessDecision(ApplicationId.RCLockout);
        var feesOrchestratorEvaluationAccessTask = container.GetAccessDecision(ApplicationId.FeesOrchestratorEvaluation);
        var pilotCheck = await _pilotCheck.IsInPilot(container);
        var feesOrchestratorEvaluationAccess = await feesOrchestratorEvaluationAccessTask;

        var shouldIncludeInPilot = pilotCheck.IsInPilot && (await rocketLogicAccessTask).AccessDecision && feesOrchestratorEvaluationAccess.AccessDecision
                                    && feesOrchestratorEvaluationAccess.Features.Contains(ApplicationId.FeeRequestOrchestration);

        if (!shouldIncludeInPilot)
        {
            exclusions.Add(new Exclusion
            {
                Reason = ExclusionReason.NotInPilot,
                ExcludedAt = DateTime.UtcNow,
            });
        }

        return new ExclusionDecision(exclusions, pilotCheck.Features, persist: true);
    }
}
