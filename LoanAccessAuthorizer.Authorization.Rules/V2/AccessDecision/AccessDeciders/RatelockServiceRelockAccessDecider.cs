using System.Globalization;
using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.Extensions;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class RatelockServiceRelockAccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.RatelockServiceRelock;
    private readonly IPilotCheck _pilotCheck;
    private readonly Type _exclusionDeciderType = typeof(RatelockServiceRelockAccessDecider);

    public RatelockServiceRelockAccessDecider(PilotCheckFactory pilotCheckFactory)
    {
        _pilotCheck = pilotCheckFactory(PilotCheckType.Leader, AccessDecisionId);
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var exclusionDescision = new ExclusionDecision(true);

        var (accessDecision, hasLoanData) = await _pilotCheck.IsInPilot(container);

        if (!accessDecision)
        {
            exclusionDescision.Add(ExclusionReason.NotInPilot, _exclusionDeciderType, "Loan is not part of this pilot.");
            return exclusionDescision;
        }

        var loanStatusesTask = container.StateContainer.Get<AmpLoanStatusCollection>();
        var isRocketLogicBankingTask = container.StateContainer.IsRocketLogicBankingLoan();
        var isInAppDepositFolderingPilot = container.GetAccessDecision(ApplicationId.AppDepositFoldering);

        await Task.WhenAll(loanStatusesTask, isRocketLogicBankingTask, isInAppDepositFolderingPilot);

        if (loanStatusesTask.Result.Any(status => status.LoanStatusId == LoanStatus.FolderReceived.ToString(CultureInfo.InvariantCulture)))
        {
            exclusionDescision.Add(ExclusionReason.UnsupportedLoanStatus, _exclusionDeciderType, "Loan is at or past folder received.");
        }

        if (!isRocketLogicBankingTask.Result.isRocketLogicBankingLoan)
        {
            exclusionDescision.Add(ExclusionReason.NotInPilot, _exclusionDeciderType, "Loan is not a RocketLogicBanking loan.");
        }

        if (!isInAppDepositFolderingPilot.Result)
        {
            exclusionDescision.Add(ExclusionReason.NotInPilot, _exclusionDeciderType, "Loan is not in AppDepositFoldering pilot, therefore, it is not in RatelockServiceRelock pilot.");
        }

        if (exclusionDescision.IsExcluded)
        {
            return exclusionDescision;
        }

        return new ExclusionDecision(false);
    }
}
