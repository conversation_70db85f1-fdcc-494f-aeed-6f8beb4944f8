using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Common.AccessPopulations;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class PropertyInsuranceQualifierAccessDecider : IAccessDecider
{
    private readonly IApplicationAuthorizerDecisionStore _applicationAuthorizerDecisionStore;
    private readonly ILogger _logger;
    private readonly IAccessPopulation _accessPopulation;

    public string AccessDecisionId => "PropertyInsuranceQualifier";

    private readonly ISet<string> AllowedPropertyTypes = new HashSet<string>(StringComparer.CurrentCultureIgnoreCase)
    {
        "Single Family",
        "PUD"
    };

    public PropertyInsuranceQualifierAccessDecider(IApplicationAuthorizerDecisionStore applicationAuthorizerDecisionStore,
        ILogger<PropertyInsuranceQualifierAccessDecider> logger, AccessPopulationFactory accessPopulationFactory)
    {
        _applicationAuthorizerDecisionStore = applicationAuthorizerDecisionStore;
        _logger = logger;
        _accessPopulation = accessPopulationFactory.GetAccessPopulation(AccessDecisionId);
    }

    public async Task<bool> GetAccessDecision(AccessDecisionContainer accessContainer)
    {
        var initialLoanData = await accessContainer.StateContainer.Get<InitialLoanState>();

        var storedDecision =
            await _applicationAuthorizerDecisionStore.GetDecision(initialLoanData.LoanNumber, AccessDecisionId);

        if (storedDecision.HasValue)
        {
            return storedDecision.Value;
        }

        var tpo = await accessContainer.StateContainer.Get<ThirdPartyOrigination>();

        if (tpo?.IsRocketProTPOLoan == true && tpo?.IsCorrespondentLoan == true)
        {
            return await StoreAndLogDecision(initialLoanData.LoanNumber, false);
        }

        var subjectProperty = await accessContainer.StateContainer.Get<OpenAmpSubjectProperty>();

        if (!AllowedPropertyTypes.Contains(subjectProperty?.PropertyType))
        {
            return await StoreAndLogDecision(initialLoanData.LoanNumber, false);
        }

        var hasAccess = await _accessPopulation.IsInPopulation();

        if (hasAccess)
        {
            _logger.LogInformation("{App} access population granted access to {loan}.", AccessDecisionId, initialLoanData.LoanNumber);
        }

        return await StoreAndLogDecision(initialLoanData.LoanNumber, hasAccess);
    }

    private async Task<bool> StoreAndLogDecision(string loanNumber, bool access)
    {
        await _applicationAuthorizerDecisionStore.StoreDecision(loanNumber, AccessDecisionId, access);
        _logger.LogInformation("{App} Pilot decision ({decision}) persisted", AccessDecisionId, access);

        return access;
    }
}
