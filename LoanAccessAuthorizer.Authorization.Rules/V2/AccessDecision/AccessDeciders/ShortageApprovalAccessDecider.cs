using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class ShortageApprovalAccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.ShortageApproval;
    private readonly IPilotCheck _pilotCheck;
    private readonly Type _exclusionDeciderType = typeof(ShortageApprovalAccessDecider);

    public ShortageApprovalAccessDecider(PilotCheckFactory pilotCheckFactory)
    {
        _pilotCheck = pilotCheckFactory(PilotCheckType.Leader, AccessDecisionId);
    }

    public async Task<ExclusionDecision> GetExclusionDecision(
        AccessDecisionContainer container)
    {
        var exclusionDecision = new ExclusionDecision(false);
        var accessDecisionTask = GetAccessDecision(container);
        var isRcLockoutLoanTask = container.GetAccessDecision(ApplicationId.RCLockout);

        await Task.WhenAll(accessDecisionTask, isRcLockoutLoanTask);
        var decisions = new bool[] { accessDecisionTask.Result, isRcLockoutLoanTask.Result };
        if (decisions.Any(decision => !decision))
        {
            exclusionDecision = new ExclusionDecision(true)
            {
                { ExclusionReason.NotInPilot, _exclusionDeciderType, "Not in pilot" }
            };
        }

        return exclusionDecision;
    }

    private async Task<bool> GetAccessDecision(AccessDecisionContainer accessContainer)
    {
        var (accessDecision, _) = await _pilotCheck.IsInPilot(accessContainer);
        return accessDecision;
    }
}
