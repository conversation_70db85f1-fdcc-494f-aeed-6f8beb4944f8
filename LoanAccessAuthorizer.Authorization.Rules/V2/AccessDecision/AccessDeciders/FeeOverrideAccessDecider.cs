using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.Extensions;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class FeeOverrideAccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.FeeOverride;
    private readonly IPilotCheck _pilotCheck;

    public FeeOverrideAccessDecider(PilotCheckFactory pilotCheckFactory)
    {
        _pilotCheck = pilotCheckFactory(PilotCheckType.Leader, AccessDecisionId);
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var pilotCheckTask = _pilotCheck.IsInPilot(container);
        var isRlbLoanTask = container.StateContainer.IsRocketLogicBankingLoan();
        var parPilotTask = container.GetAccessDecision(ApplicationId.ParAutomatedReview);

        var (isInPilot, hasLoanData) = await pilotCheckTask;
        var isRlbLoan = (await isRlbLoanTask).isRocketLogicBankingLoan;
        var isInParPilot = await parPilotTask;

        var exclusionDecision = new ExclusionDecision(persist: hasLoanData);
        if (!isInPilot || !isRlbLoan || !isInParPilot)
        {
            exclusionDecision.Add(ExclusionReason.NotInPilot, GetType());
        }

        return exclusionDecision;
    }
}
