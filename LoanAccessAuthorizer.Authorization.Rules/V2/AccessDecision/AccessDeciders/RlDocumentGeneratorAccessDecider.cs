using LoanAccessAuthorizer.Domain.PilotExclusions.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class RlDocumentGeneratorAccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.RlDocumentGenerator;

    public Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        return Task.FromResult(new ExclusionDecision(true)
            {
                { ExclusionReason.NotInPilot, GetType() }
            });
    }
}
