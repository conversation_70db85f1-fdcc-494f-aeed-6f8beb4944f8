using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot.Utils;
using LoanAccessAuthorizer.Domain;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.LoanData;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class RLBTaskBasedDecider : LeaderPilotAccessDecider
{
    public override string AccessDecisionId => ApplicationId.RLBTaskBased;

    public RLBTaskBasedDecider(IApplicationAuthorizerDecisionStore decisionStore,
        PilotCheckFactory pilotCheckFactory,
        ILogger<RLBTaskBasedDecider> logger)
        : base(decisionStore, pilotCheckFactory, logger)
    {
    }

    protected override async Task<(bool isInPilot, bool shouldPersist)> DetermineAccessDecision(AccessDecisionContainer accessContainer)
    {
        return (true, true);
    }

}
