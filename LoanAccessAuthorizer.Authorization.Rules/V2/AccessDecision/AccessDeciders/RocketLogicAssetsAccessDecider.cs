using LoanAccessAuthorizer.Authorization.Rules.Configuration;
using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Common.AccessPopulations;
using LoanAccessAuthorizer.Common.Random;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;
using Microsoft.Extensions.Options;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class RocketLogicAssetsAccessDecider : IExclusionDecider
{
    private const int MinThreshold = 0;
    private const int MaxThreshold = 100;

    private readonly IPilotCheck _pilotCheck;
    private readonly ILookup<
        string,
        (RocketLogicAssetsTpoPopulationConfiguration Config, IAccessPopulation Population)
    > _accessPopulations;

    public string AccessDecisionId => ApplicationId.RocketLogicAssets;

    public RocketLogicAssetsAccessDecider(
        PilotCheckFactory pilotCheckFactory,
        IRandomNumberGenerator randomNumberGenerator,
        IOptionsMonitor<
            Dictionary<string, RocketLogicAssetsTpoPopulationConfiguration>
        > rlAccessPopulationConfigurations
    )
    {
        _pilotCheck = pilotCheckFactory(PilotCheckType.Leader, AccessDecisionId);
        _accessPopulations = rlAccessPopulationConfigurations.CurrentValue.Values.ToLookup(
            rlAccessConfig => rlAccessConfig.LoanPurpose,
            (RocketLogicAssetsTpoPopulationConfiguration, IAccessPopulation) (rlAccessConfig) =>
                (
                    rlAccessConfig,
                    new RandomPercentageBasedAccessPopulation(
                        rlAccessConfig.Percentage,
                        MinThreshold,
                        MaxThreshold,
                        randomNumberGenerator
                    )
                )
        );
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var isTpoLoan = await container.StateContainer.Get<bool>(
            ExplicitStateProviderIds.IsTpoLoan
        );

        return isTpoLoan ? await ProcessTPO(container) : await ProcessRetail(container);
    }

    private async Task<ExclusionDecision> ProcessTPO(AccessDecisionContainer container)
    {
        var rlApiLoanDetails = await container.StateContainer.Get<RocketLogicLoanDetails>();

        // Get all population configs whose name match the loan purpose
        var loanPurpose = rlApiLoanDetails.LoanPurpose?.ToString();
        if (string.IsNullOrWhiteSpace(loanPurpose) || !_accessPopulations.Contains(loanPurpose))
        {
            return new ExclusionDecision(true)
            {
                new()
                {
                    Reason = ExclusionReason.UnsupportedLoanPurpose,
                    ExcludedAt = DateTime.Now,
                }
            };
        }

        // Only use configs where pilot determinate date is after the pilot date
        var pilotDeterminantDate = await container.StateContainer.Get<DateTime?>(
            ExplicitStateProviderIds.ApplicationOrInitialContactDate
        );

        if (pilotDeterminantDate == null)
        {
            return new ExclusionDecision(false)
            {
                new()
                {
                    Reason = ExclusionReason.PilotDeterminantDateNotFound,
                    ExcludedAt = DateTime.Now,
                }
            };
        }

        // Use the accessible population with the latest pilot date prior to the determinant date
        var accessiblePopulationsOrderedByLatest = _accessPopulations[loanPurpose]
            .OrderByDescending(x => x.Config.PilotDate)
            .Where(x => x.Config.PilotDate <= pilotDeterminantDate)
            .ToList();

        if (!accessiblePopulationsOrderedByLatest.Any())
        {
            return new ExclusionDecision(true)
            {
                new()
                {
                    Reason = ExclusionReason.EarlierThanPilotStartDate,
                    ExcludedAt = DateTime.Now,
                }
            };
        }

        foreach (var accessiblePopulation in accessiblePopulationsOrderedByLatest)
        {
            var isInPopulation = await accessiblePopulation.Population.IsInPopulation();

            if (isInPopulation)
            {
                return new ExclusionDecision(true, accessiblePopulation.Config.Features);
            }
        }

        return new ExclusionDecision(true)
        {
            new() { Reason = ExclusionReason.NotInPilot, ExcludedAt = DateTime.Now }
        };
    }

    private async Task<ExclusionDecision> ProcessRetail(AccessDecisionContainer container)
    {
        // Retail loans are based off of leader. Need common id to check this.
        var rlbLeadDetails = await container.StateContainer.Get<RocketLogicBankingLeadDetails>();
        if (rlbLeadDetails?.AssignedToCommonId == null)
        {
            return new ExclusionDecision(false)
            {
                new()
                {
                    Reason = ExclusionReason.LeadHasNoAllocatedCommonId,
                    ExcludedAt = DateTime.Now,
                }
            };
        }

        // Check if common id is under leader in pilot and loan application is past certain date.
        var pcr = await _pilotCheck.IsInPilot(container);
        if (!pcr.IsInPilot)
        {
            return new ExclusionDecision(pcr.HasLoanData)
            {
                new()
                {
                    Reason = ExclusionReason.NotInPilot,
                    ExcludedAt = DateTime.Now,
                    Comment = $"Loan assigned to {rlbLeadDetails.AssignedToCommonId}"
                }
            };
        }

        return new ExclusionDecision(pcr.HasLoanData, pcr.Features);
    }
}
