using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

/// <summary>
/// Access Decider for the Payoff Qualifier
/// </summary>
public class POQAccessDecider : PilotAccessDecider
{
    public override string AccessDecisionId => ApplicationId.POQ;
    
    private readonly ISet<string> _excludedQualificationGroups = new HashSet<string>(StringComparer.CurrentCultureIgnoreCase)
    {
        "FHACreditQualifyingStreamline",
        "FHANonCreditQualifyingStreamline",
        "FHAStreamline",
        "VACreditQualifyingIRRRL",
        "VAIRRRL",
        "VANonCreditQualifyingIRRRL"
    };


    public POQAccessDecider(
        IApplicationAuthorizerDecisionStore decisionStore,
        PilotCheckFactory pilotCheckFactory,
        ILogger<POQAccessDecider> logger
    )
         : base(decisionStore, pilotCheckFactory, logger)
    {
    }

    protected override async Task<(bool isInPilot, bool shouldPersist)> DetermineAccessDecision(AccessDecisionContainer accessContainer)
    {
        // Check for loan type. Allow Refinance loans only. 
        var loanDetails = await accessContainer.StateContainer.Get<AmpLoanDetails>();
        var loanPurpose = loanDetails?.LoanPurpose;
        if (loanPurpose != "Refinance")
        {
            return (false, true);
        }

        var loanStatuses = await accessContainer.StateContainer.Get<AmpLoanStatusCollection>();

        // Check for folder received
        if (!loanStatuses.Any(status => int.Parse(status.LoanStatusId) == LoanStatus.FolderReceived))
        {
            return (false, false);
        }

        // Check for status greater than HudReviewComplete
        if (loanStatuses.Any(status => int.Parse(status.LoanStatusId) > LoanStatus.HudReviewComplete))
        {
            return (false, false);
        }

        var tpo = await accessContainer.StateContainer.Get<ThirdPartyOrigination>();

        if (tpo?.IsCorrespondentLoan == true)
        {
            return (false, true);
        }

        var qualificationGroup = await accessContainer.StateContainer.Get<QualificationGroupSet>();

        if (!qualificationGroup.Any() || qualificationGroup.Intersect(_excludedQualificationGroups).Any())
        {
            return (false, false);
        }

        return await base.DetermineAccessDecision(accessContainer);
    }

}
