﻿using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class CQAlimonyChildSupport : IAccessDecider
{
    private readonly IApplicationAuthorizerDecisionStore _decisionStore;
    private const string ProdStartDate = "2021-03-31T00:00:00";
    private const string LowersStartDate = "2020-11-24T00:00:00";
    private readonly DateTime _startDate;

    public CQAlimonyChildSupport(IApplicationAuthorizerDecisionStore decisionStore,
        EnvironmentInfo env, ILogger<CQAlimonyChildSupport> logger) 
    {
        _decisionStore = decisionStore ?? throw new ArgumentNullException(nameof(decisionStore));

        _startDate =  env.EnvironmentName.Equals("prod", StringComparison.OrdinalIgnoreCase)
            ? DateTime.Parse(ProdStartDate)
            : DateTime.Parse(LowersStartDate);
    }

    public string AccessDecisionId => ApplicationId.CQAlimonyChildSupport;

    public async Task<bool> GetAccessDecision(AccessDecisionContainer accessContainer)
    {
        var initialLoanData = await accessContainer.StateContainer.Get<InitialLoanState>();
        var loanNumber = initialLoanData.LoanNumber;
        var storedDecision = await _decisionStore.GetDecision(loanNumber, AccessDecisionId);
        if (storedDecision.HasValue)
        {
            return storedDecision.Value;
        }     

        var isCqLoan = accessContainer.GetAccessDecision(ApplicationId.Credit);
        var initialContactDate = accessContainer.StateContainer.Get<DateTime?>(ExplicitStateProviderIds.ApplicationOrInitialContactDate);
        await Task.WhenAll(isCqLoan, initialContactDate);
        var decision = isCqLoan.Result && initialContactDate.Result > _startDate;
        await _decisionStore.StoreDecision(initialLoanData.LoanNumber, AccessDecisionId, decision);
        return decision;
    }

}