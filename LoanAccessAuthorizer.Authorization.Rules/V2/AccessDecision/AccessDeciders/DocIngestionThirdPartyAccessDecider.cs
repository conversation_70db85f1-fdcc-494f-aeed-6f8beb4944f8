using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class DocIngestionThirdPartyAccessDecider : LeaderPilotAccessDecider
{
    protected override bool ShouldPersist => true;
    public override string AccessDecisionId => ApplicationId.DocIngestionThirdParty;

    public DocIngestionThirdPartyAccessDecider(
        IApplicationAuthorizerDecisionStore decisionStore,
        PilotCheckFactory pilotCheckFactory,
        ILogger<DocIngestionThirdPartyAccessDecider> logger)
        : base(decisionStore, pilotCheckFactory, logger)
    { }

    protected override Task<(bool isInPilot, bool shouldPersist)> DetermineAccessDecision(
        AccessDecisionContainer container
    ) => Task.FromResult((true, true));
}
