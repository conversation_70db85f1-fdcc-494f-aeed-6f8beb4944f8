using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class DocFraudDetectionAccessDecider : LeaderPilotAccessDecider
{
    public DocFraudDetectionAccessDecider(
        IApplicationAuthorizerDecisionStore decisionStore,
        PilotCheckFactory pilotCheckFactory,
        ILogger<DocFraudDetectionAccessDecider> logger)
        : base(
            decisionStore,
            pilotCheckFactory,
            logger)
    {
    }

    public override string AccessDecisionId => ApplicationId.DocFraudDetection;

    protected override Task<(bool isInPilot, bool shouldPersist)> DetermineAccessDecision(
        AccessDecisionContainer container
    ) => Task.FromResult((true, true));
}
