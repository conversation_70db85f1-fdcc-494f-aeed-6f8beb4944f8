using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class BetterQualityImagesAccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.RocketDocsBetterQualityImages;

    private readonly IPilotCheck _pilotCheck;

    public BetterQualityImagesAccessDecider(
        PilotCheckFactory pilotCheckFactory
    )
    {
        _pilotCheck = pilotCheckFactory(PilotCheckType.Leader, AccessDecisionId);
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var (isInPilot, hasLoanData) = await _pilotCheck.IsInPilot(container);

        var exclusion = new ExclusionDecision(hasLoanData);
        if (!isInPilot)
        {
            exclusion.Add(ExclusionReason.NotInPilot, GetType());
        }

        return exclusion;
    }
}

