using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class AQAccessDecider : IExclusionDecider
{
    private readonly IApplicationAuthorizerDecisionStore _decisionStore;

    public string AccessDecisionId => ApplicationId.Asset;

    public AQAccessDecider(IApplicationAuthorizerDecisionStore decisionStore)
    {
        _decisionStore = decisionStore;
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var getExistingDecisionTask = GetExistingDecision(container);
        var existingDecision = await getExistingDecisionTask;
        var exclusion = new ExclusionDecision(true);

        if (existingDecision == false)
        {
            exclusion.Add(ExclusionReason.NotInPilot, typeof(AQAccessDecider));
        }

        return exclusion;
    }

    private async Task<bool?> GetExistingDecision(AccessDecisionContainer accessContainer)
    {
        var initialLoanState = await accessContainer.StateContainer.Get<InitialLoanState>();

        var existingDecisionTask = _decisionStore.GetDecision(
            initialLoanState.LoanNumber,
            AccessDecisionId
        );

        return await existingDecisionTask;
    }
}
