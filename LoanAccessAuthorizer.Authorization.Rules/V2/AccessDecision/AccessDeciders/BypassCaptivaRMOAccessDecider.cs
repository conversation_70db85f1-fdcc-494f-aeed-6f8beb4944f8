﻿using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Common.AccessPopulations;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class BypassCaptivaRMOAccessDecider : BypassCaptivaPercentageBasedAccessDecider
{
    public override string AccessDecisionId => ApplicationId.BypassCaptivaRMO;

    public BypassCaptivaRMOAccessDecider(IApplicationAuthorizerDecisionStore applicationAuthorizerDecisionStore,
        ILogger<BypassCaptivaRMOAccessDecider> logger, AccessPopulationFactory accessPopulationFactory) : 
        base(applicationAuthorizerDecisionStore, logger, accessPopulationFactory)
    {
    }

    protected override bool IsAboveLoanStatusThreshold(AmpLoanStatusCollection loanStatuses) => false;
    protected override bool IsBelowLoanStatusThreshold(AmpLoanStatusCollection loanStatuses)
    {
        return loanStatuses.All(status => status.LoanStatusId != $"{LoanStatus.FinalSignoff}");
    }
}