namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class RlbBackHalfAmpLockoutAccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.RlbBackHalfAmpLockout;

    public Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var exclusion = new ExclusionDecision(false);
        return Task.FromResult(exclusion);
    }
}
