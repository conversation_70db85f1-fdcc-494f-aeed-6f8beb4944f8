using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.Constants;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.RLUnderwriting;

public class RLUnderwritingCondoAccessDecider : PilotAccessDecider
{
    public override string AccessDecisionId => ApplicationId.RocketLogicUnderwritingCondo;
    private const string FolderReceivedStatusName = "FolderReceived";
    private const string FolderReceivedDate = "FolderRecievedDate";
    private const string InitialContactDate = "AmpInitialContactDate";
    private const string UnknownDate = "UnknownDate";
    private readonly DateTime FolderReceivedCutoverDate = new DateTime(2022, 01, 06);

    private static readonly IEnumerable<string> CondoSubjectPropertyTypes =
        new HashSet<string>(StringComparer.InvariantCultureIgnoreCase)
        {
            "Condominium",
            "Coop",
            "Townhouse",
            "Site Condominium"
        };

    public RLUnderwritingCondoAccessDecider(IApplicationAuthorizerDecisionStore store,
        PilotCheckFactory pilotCheckFactory,
        ILogger<RLUnderwritingCondoAccessDecider> logger)
        : base(store, pilotCheckFactory, logger)
    {
    }

    protected override async Task<(bool isInPilot, bool shouldPersist)> DetermineAccessDecision(
        AccessDecisionContainer accessContainer)
    {
        if (!await IsCondoLoan(accessContainer))
        {
            var subjectProperty = await accessContainer.StateContainer.Get<OpenAmpSubjectProperty>();
            _logger.LogInformation("{App} Pilot decision {decision}: {@SubjectProperty}",
             AccessDecisionId, false, subjectProperty);
            return (false, true);
        }

        var initialContactTask = accessContainer.StateContainer.Get<DateTime?>(ExplicitStateProviderIds.ApplicationOrInitialContactDate);
        var keyLoanInfoTask = accessContainer.StateContainer.Get<AmpKeyLoanInfo>();
        var isProductOnLoan = accessContainer.StateContainer.Get<bool>(ExplicitStateProviderIds.IsProductOnLoan);

        var initialContact = await initialContactTask;
        if (initialContact == null)
        {
            _logger.LogInformation("{App} Pilot decision {decision}: InitialContact info returned no data",
                AccessDecisionId, false);
            return (false, false);
        }

        var keyLoanInfo = await keyLoanInfoTask;
        var piggyBackLoanNumber = keyLoanInfo?.PiggyBacklnNumber;
        if (!string.IsNullOrEmpty(piggyBackLoanNumber))
        {
            _logger.LogInformation("{App} Pilot decision {decision} {PiggybackLoanNumber}",
                AccessDecisionId, false, piggyBackLoanNumber);
            return (false, true);
        }

        if (!isProductOnLoan.Result)
        {
            return (false, false);
        }

        var decision = await base.DetermineAccessDecision(accessContainer);
        if (!decision.isInPilot)
        {
            var loanChannel = PilotCheck.HasLoanChannelCheck
                ? accessContainer.StateContainer.Get<string>(ExplicitStateProviderIds.LoanChannel)
                : Task.FromResult(string.Empty);

            var loanDate = GetPilotCutoffLoanDate(accessContainer);
            await Task.WhenAll(loanChannel, loanDate);
            _logger.LogInformation("{App} Pilot decision {decision}: {loanChannel} {@PilotLoanDate}",
                AccessDecisionId, decision, await loanChannel, await loanDate);
        }
        return decision;
    }

    private static bool IsSubjectPropertyTypeApplicable(OpenAmpSubjectProperty openAmpSubjectProperty)
    {
        return CondoSubjectPropertyTypes.Contains(openAmpSubjectProperty.PropertyType);
    }

    private async Task<bool> IsCondoLoan(AccessDecisionContainer accessContainer)
    {
        var subjectProperty = await accessContainer.StateContainer.Get<OpenAmpSubjectProperty>();

        return IsSubjectPropertyTypeApplicable(subjectProperty);
    }

    public override async Task<DateTime?> GetLoanDateForPilotComparison(
        AccessDecisionContainer container)
    {
        return (await GetPilotCutoffLoanDate(container)).loanDate;
    }

    private async Task<(DateTime? loanDate, string loanDateType)> GetPilotCutoffLoanDate(
        AccessDecisionContainer container)
    {

        var initialContactDate = await container.StateContainer.Get<DateTime?>(ExplicitStateProviderIds.ApplicationOrInitialContactDate);

        if (initialContactDate >= FolderReceivedCutoverDate)
        {
            return (initialContactDate, InitialContactDate);
        }

        var loanStatuses = await container.StateContainer.Get<AmpLoanStatusCollection>();

        var folderReceivedDate = loanStatuses
            .FirstOrDefault(status => status.LoanStatusName == FolderReceivedStatusName)?.LoanStatusDate;

        if (folderReceivedDate != null)
        {
            return (Convert.ToDateTime(folderReceivedDate), FolderReceivedDate);
        }

        return (null, UnknownDate);
    }
}
