using DecisionServices.Core.StateProvider;
using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot.PilotConfigProvider;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.Constants;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using LoanAccessAuthorizer.Domain.Product.Models;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.RLUnderwriting;

public class RLUnderwritingCreditAccessDecider : IAccessDecider, IClearDecision
{
    private const string FolderReceivedStatusName = "FolderReceived";
    private const string LockAndShopProductParamName = "73";
    private const string NonAgencyBankStatementParamName = "88";

    private static readonly ISet<string> _allowedLoanPurposes = new HashSet<string>(StringComparer.CurrentCultureIgnoreCase)
    {
        LoanPurpose.Refinance,
        LoanPurpose.Purchase,
        LoanPurpose.NewConstruction
    };
    private static readonly ISet<string> _excludedProducts = new HashSet<string>(StringComparer.CurrentCultureIgnoreCase)
    {
        LockAndShopProductParamName,
        NonAgencyBankStatementParamName
    };

    protected readonly IOrderedEnumerable<(DateTime date, PilotGroup group)> PilotsInDescendingOrder;

    protected readonly ILogger<RLUnderwritingCreditAccessDecider> _logger;
    public RLUnderwritingCreditAccessDecider(
        IPilotConfigProvider pilotConfigProvider,
        ILogger<RLUnderwritingCreditAccessDecider> logger)
    {
        _logger = logger;

        // Pilot decision based on first pilot found before the folder received date
        PilotsInDescendingOrder = pilotConfigProvider.GetPilots(AccessDecisionId)
            .Select(data => (data.PilotDate,
                new PilotGroup(
                    data.CommonIdentifiers,
                    data.ExcludedCommonIdentifiers,
                    data.LoanChannels,
                    data.IncludedQualificationGroups,
                    data.ExcludedQualificationGroups,
                    data.Features)))
            .OrderByDescending(x => x.PilotDate);
    }

    public string AccessDecisionId => ApplicationId.RocketLogicUnderwritingCredit;
    public Task<bool> CanClearDecision(AccessDecisionContainer accessContainer)
    {
        return Task.FromResult(false);
    }

    public async Task<bool> GetAccessDecision(AccessDecisionContainer accessContainer)
    {
        var isInPilot = await IsInPilot(accessContainer);

        if (!isInPilot)
        {
            _logger.LogInformation("Loan is not in rocket logic underwriting pilot");
        }

        return isInPilot;
    }

    private async Task<bool> IsInPilot(AccessDecisionContainer container)
    {
        var hasCQ = container.GetAccessDecision(ApplicationId.Credit);
        var hasMC = container.GetAccessDecision(ApplicationId.MortgageClient);
        await Task.WhenAll(hasCQ, hasMC);

        if (!hasCQ.Result || !hasMC.Result)
        {
            _logger.LogInformation("{App} Pilot decision {decision}: CQ access {CQ}, MC access {MC}",
                AccessDecisionId, false, hasCQ.Result, hasMC.Result);
            return false;
        }

        var folderReceivedDate = await GetFolderReceivedDate(container.StateContainer);
        if (folderReceivedDate == null)
        {
            _logger.LogInformation("{App} Pilot decision {decision}: Loan not in FR status / no FR date", AccessDecisionId, false);
            return false;
        }

        var ampKeyLoanInfo = await container.StateContainer.Get<AmpKeyLoanInfo>();
        if (!_allowedLoanPurposes.Contains(ampKeyLoanInfo.LoanPurpose))
        {
            _logger.LogInformation("{App} Pilot decision {decision}: Loan Purpose {LoanPurpose} not supported",
                AccessDecisionId, false, ampKeyLoanInfo.LoanPurpose);
            return false;
        }

        if (!string.IsNullOrEmpty(ampKeyLoanInfo.PiggyBacklnNumber))
        {
            _logger.LogInformation("{App} Pilot decision {decision}: Piggyback loans are not supported", AccessDecisionId, false);
            return false;
        }

        var loanChannelTask = container.StateContainer.Get<string>(ExplicitStateProviderIds.LoanChannel);
        var qualificationGroupsTask = container.StateContainer.Get<QualificationGroupSet>();
        var ampLoanDetailTask = container.StateContainer.Get<AmpLoanDetails>();
        var productInfoTask = container.StateContainer.Get<ProductInfo>();
        var isProductOnLoan = container.StateContainer.Get<bool>(ExplicitStateProviderIds.IsProductOnLoan);

        var pilot = PilotsInDescendingOrder.FirstOrDefault(x => folderReceivedDate >= x.date);
        var loanChannel = await loanChannelTask;
        var qualificationGroups = await qualificationGroupsTask;

        if (loanChannel == null
            || qualificationGroups == null
            || !qualificationGroups.Any()
            || pilot == default)
        {
            return false;
        }

        var isInPilot = pilot.group.ContainsLoanChannel(loanChannel)
            && pilot.group.AllowsQualificationGroups(qualificationGroups);

        if (!isInPilot)
        {
            return false;
        }

        var ampLoanDetail = await ampLoanDetailTask;
        var isNewConstructionPilot = pilot.group.Features.Contains(RLUnderwritingFeatures.RluwNewConstructionPilot);
        if (ampLoanDetail.IsNewConstruction && !isNewConstructionPilot)
        {
            _logger.LogInformation("{App} Pilot decision {decision}: New Construction loans are not supported", AccessDecisionId, false);
            return false;
        }

        // excludes Assumption loans
        if (ampLoanDetail.LeadTypeCode?.Contains("Assump", StringComparison.CurrentCultureIgnoreCase) ?? false)
        {
            _logger.LogInformation("{App} Pilot decision {decision}: Assumption loans are not supported", AccessDecisionId, false);
            return false;
        }

        if (!isProductOnLoan.Result)
        {
            _logger.LogInformation("{App} Pilot decision {decision}: Product is not on the loan", AccessDecisionId, false);
            return false;
        }

        var productInfo = await productInfoTask;
        var productParameters = productInfo?.ProductParameters?.Select(param => param.ParameterName)
                                ?? Enumerable.Empty<string>();
        if (_excludedProducts.Overlaps(productParameters))
        {
            _logger.LogInformation("{App} Pilot decision {decision}: Product {Product} {Purpose} not supported",
                AccessDecisionId, false, ampKeyLoanInfo.ProductCode, ampKeyLoanInfo.LoanPurpose);
            return false;
        }

        return true;
    }

    private async Task<DateTime?> GetFolderReceivedDate(StateContainer container)
    {
        var loanStatuses = await container.Get<AmpLoanStatusCollection>();

        var folderReceivedDate = loanStatuses
            .FirstOrDefault(status => status.LoanStatusName == FolderReceivedStatusName)?.LoanStatusDate;

        if (folderReceivedDate != null)
        {
            return Convert.ToDateTime(folderReceivedDate);
        }

        return null;
    }
}
