using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.RLUnderwriting;

public class RLUnderwritingAccessDecider : IExclusionDecider, IClearDecision
{
    private static readonly IList<string> _subApplicationIds = new List<string>
    {
        ApplicationId.RocketLogicUnderwritingCondo,
        ApplicationId.RocketLogicUnderwritingCredit,
        ApplicationId.RocketLogicUnderwritingSchwab,
        ApplicationId.RocketLogicUnderwritingTpo,
    };

    public string AccessDecisionId => ApplicationId.RocketLogicUnderwriting;
    public Task<bool> CanClearDecision(AccessDecisionContainer accessContainer)
    {
        return Task.FromResult(false);
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer accessContainer)
    {
        var isLoanInAmp = await accessContainer.StateContainer.Get<bool>(ExplicitStateProviderIds.IsLoanInAmp);
        if (!isLoanInAmp)
        {
            return new ExclusionDecision(false)
            {
                { ExclusionReason.NotInPilot, typeof(RLUnderwritingAccessDecider), "Loan is not in Amp" }
            };
        }

        var exclusions = new ExclusionDecision(false);

        var decisions = _subApplicationIds.Select(accessContainer.GetAccessDecision);
        var isInPilot = (await Task.WhenAll(decisions))
            .Any(decision => decision);
        if (!isInPilot)
        {
            exclusions.Add(new Exclusion
            {
                Reason = ExclusionReason.NotInPilot,
                ExcludedAt = DateTime.UtcNow,
            });
        }

        return exclusions;
    }
}
