using DecisionServices.Core.StateProvider;
using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.Constants;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using LoanAccessAuthorizer.Domain.Models;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;
using LoanAccessAuthorizer.Domain.Product.Models;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.RLUnderwriting;

public class RLUnderwritingSchwabAccessDecider : IExclusionDecider, IClearDecision
{
    public string AccessDecisionId => ApplicationId.RocketLogicUnderwritingSchwab;

    protected readonly ILogger<RLUnderwritingSchwabAccessDecider> _logger;

    private const string LockAndShopProductParamName = "73";
    private const string NonAgencyBankStatementParamName = "88";

    private static readonly ISet<string> _allowedLoanPurposes = new HashSet<string>(StringComparer.CurrentCultureIgnoreCase)
    {
        LoanPurpose.Refinance,
        LoanPurpose.Purchase,
        LoanPurpose.NewConstruction
    };
    private static readonly ISet<string> _excludedProducts = new HashSet<string>(StringComparer.CurrentCultureIgnoreCase)
    {
        LockAndShopProductParamName,
        NonAgencyBankStatementParamName
    };

    private readonly IPilotCheck _pilotCheck;

    public RLUnderwritingSchwabAccessDecider(
        PilotCheckFactory pilotCheckFactory,
        ILogger<RLUnderwritingSchwabAccessDecider> logger)
    {
        _logger = logger;
        _pilotCheck = pilotCheckFactory(PilotCheckType.Normal, AccessDecisionId);
    }
    
    public Task<bool> CanClearDecision(AccessDecisionContainer accessContainer)
    {
        return Task.FromResult(false);
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer accessContainer)
    {
        var exclusionDecision = new ExclusionDecision(false);

        var folderReceivedDate = await GetFolderReceivedDate(accessContainer.StateContainer);
        if (folderReceivedDate == null)
        {
            exclusionDecision.Add(
                ExclusionReason.NotInPilot,
                GetType(),
                "Loan not in FR status / no FR date"
            );
            return exclusionDecision;
        }

        var userId = await accessContainer.StateContainer.Get<UserId>(ExplicitStateProviderIds.LoanOfficerCommonId);
        var pilotCheck = (await _pilotCheck.IsInPilot(accessContainer, userId, folderReceivedDate));
        if (!pilotCheck.IsInPilot)
        {
            exclusionDecision.Add(
                ExclusionReason.NotInPilot,
                GetType()
            );
            return exclusionDecision;
        }

        var hasAQTask = accessContainer.GetAccessDecision(ApplicationId.Asset);
        var hasCQTask = accessContainer.GetAccessDecision(ApplicationId.Credit);
        var hasMCTask = accessContainer.GetAccessDecision(ApplicationId.MortgageClient);

        var hasAQ = await hasAQTask;
        if (!hasAQ)
        {
            exclusionDecision.Add(
                ExclusionReason.NotInPilot,
                GetType(),
                "Must have access to AQ"
            );
        }

        var hasCQ = await hasCQTask;
        if (!hasCQ)
        {
            exclusionDecision.Add(
                ExclusionReason.NotInPilot,
                GetType(),
                "Must have access to CQ"
            );
        }

        var hasMC = await hasMCTask;
        if (!hasMC)
        {
            exclusionDecision.Add(
                ExclusionReason.NotInPilot,
                GetType(),
                "Must have access to MC"
            );
        }

        var qualificationGroups = await accessContainer.StateContainer.Get<QualificationGroupSet>();
        var isJumbo = qualificationGroups.Contains("Jumbo");
        if (isJumbo)
        {
            var isSchwabProduct = qualificationGroups.Contains("SchwabProduct");
            if (isSchwabProduct)
            {
                var isSchwabJumboPilot = pilotCheck.Features.Contains(RLUnderwritingFeatures.RluwSchwabJumboPilotFeature);
                if (!isSchwabJumboPilot)
                {
                    exclusionDecision.Add(
                        ExclusionReason.NotInPilot,
                        GetType(),
                        $"Schwab Jumbo loan not in pilot"
                    );
                }
            }
            else
            {
                var isGovee = qualificationGroups.Contains("FHA") || qualificationGroups.Contains("VA");
                if (!isGovee)
                {
                    exclusionDecision.Add(
                        ExclusionReason.NotInPilot,
                        GetType(),
                        $"Jumbo is only supported for FHA/VA loans"
                    );
                }
            }
        }

        var ampKeyLoanInfo = await accessContainer.StateContainer.Get<AmpKeyLoanInfo>();
        if (!_allowedLoanPurposes.Contains(ampKeyLoanInfo.LoanPurpose))
        {
            exclusionDecision.Add(
                ExclusionReason.NotInPilot,
                GetType(),
                $"Loan Purpose {ampKeyLoanInfo.LoanPurpose} not supported"
            );
        }

        if (!string.IsNullOrEmpty(ampKeyLoanInfo.PiggyBacklnNumber))
        {
            exclusionDecision.Add(
                ExclusionReason.NotInPilot,
                GetType(),
                "Piggyback loans are not supported"
            );
        }

        var ampLoanDetail = await accessContainer.StateContainer.Get<AmpLoanDetails>();
        var isNewConstructionPilot = pilotCheck.Features.Contains(RLUnderwritingFeatures.RluwNewConstructionPilot);
        if (ampLoanDetail.IsNewConstruction && !isNewConstructionPilot)
        {
            exclusionDecision.Add(ExclusionReason.NewConstruction, GetType());
        }

        if (ampLoanDetail.LeadTypeCode?.Contains("Assump", StringComparison.CurrentCultureIgnoreCase) ?? false)
        {
            exclusionDecision.Add(ExclusionReason.AssumptionLoan, GetType());
        }

        var isProductOnLoan = await accessContainer.StateContainer.Get<bool>(ExplicitStateProviderIds.IsProductOnLoan);
        if (!isProductOnLoan)
        {
            exclusionDecision.Add(ExclusionReason.NoProductOnLoan, GetType());
        }

        var productInfo = await accessContainer.StateContainer.Get<ProductInfo>();
        var productParameters = productInfo?.ProductParameters?
            .Select(param => param.ParameterName) ?? Enumerable.Empty<string>();
        if (_excludedProducts.Overlaps(productParameters))
        {
            exclusionDecision.Add(
                ExclusionReason.NotInPilot,
                GetType(),
                $"Product {ampKeyLoanInfo.ProductCode} not supported"
            );
        }

        return exclusionDecision;
    }

    private async Task<DateTime?> GetFolderReceivedDate(StateContainer container)
    {
        var loanStatuses = await container.Get<AmpLoanStatusCollection>();

        var folderReceivedDate = loanStatuses.FirstOrDefault(status =>
            status.LoanStatusId == LoanStatus.FolderReceived.ToString())?.LoanStatusDate;

        if (folderReceivedDate != null)
        {
            return Convert.ToDateTime(folderReceivedDate);
        }

        return null;
    }
}
