#nullable enable

using System.ComponentModel.Design;
using System.Runtime.CompilerServices;
using AmpJobRunner.Models.Enums;
using AmpJobRunner.Models.Response;
using DecisionServices.Core.StateProvider;
using LoanAccessAuthorizer.Authorization.Rules.Features;
using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.Extensions;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Common.Constants;
using LoanAccessAuthorizer.Domain.Exceptions;
using LoanAccessAuthorizer.Domain.LoanData;
using LoanAccessAuthorizer.Domain.Models;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;

[assembly: InternalsVisibleTo("UnitTests.LoanAccessAuthorizer.Authorization.Rules.V2")]

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class PalValPrintingAccessDecider : IExclusionDecider
{
    private readonly IPilotCheck _pilotCheck;
    private readonly ILoanDataClient _loanDataClient;
    private readonly IFeatureEnabledConfigProvider _featureEnabledConfigProvider;

    private static readonly Type _excludedBy = typeof(PalValPrintingAccessDecider);

    internal const string PalStopperName = "palstoppers";
    internal const string RequiresHighQualityPalStopperMessage = "This loan requires a High Quality PAL.  Go to AMP to submit for review.";
    internal const string NonConformingPreApprovalStopperMessage = "Please run the 'Non-Conforming Pre-Approval'.";

    internal const int ALReviewTrackingItemNumber = 8278;

    internal static readonly ISet<string> ValTrackingItemStatuses = new HashSet<string>
    {
        "Approved",
        "Approved with Conditions",
        "Problem",
        "Denied"
    };

    public PalValPrintingAccessDecider(PilotCheckFactory pilotCheckFactory,
        ILoanDataClient loanDataClient,
        IFeatureEnabledConfigProvider featureEnabledConfigProvider)
    {
        _pilotCheck = pilotCheckFactory(PilotCheckType.Leader, AccessDecisionId);
        _loanDataClient = loanDataClient;
        _featureEnabledConfigProvider = featureEnabledConfigProvider;
    }

    public string AccessDecisionId => ApplicationId.PalValPrinting;

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var loanOfficerId = await container.StateContainer.Get<UserId>(
            ExplicitStateProviderIds.LoanOfficerCommonId
        );

        if (
            string.IsNullOrWhiteSpace(loanOfficerId.Id)
            || string.Equals(loanOfficerId.Id, CommonIdConstants.DefaultCommonId)
        )
        {
            return new ExclusionDecision(false) { { ExclusionReason.NotInPilot, GetType() } };
        }

        var (isRocketLogicBankingLoan, hasRocketLogicBankingDetails) =
            await container.StateContainer.IsRocketLogicBankingLoan();
        if (!isRocketLogicBankingLoan)
        {
            return new ExclusionDecision(persist: hasRocketLogicBankingDetails)
            {
                { ExclusionReason.NotInPilot, _excludedBy }
            };
        }

        var (isInPilot, hasLoanData) = await _pilotCheck.IsInPilot(container);

        if (!isInPilot)
        {
            return new ExclusionDecision(persist: hasLoanData)
            {
                { ExclusionReason.NotInPilot, _excludedBy }
            };
        }

        var exclusions = new List<Exclusion>();

        var hasVerifiedApprovalLetter = HasVerifiedApprovalLetter(container.StateContainer);

        var jobs = await container.StateContainer.Get<AmpJobsCollection>();
        if (HasStopperOfType(jobs, RequiresHighQualityPalStopperMessage))
        {
            exclusions.Add(new Exclusion
            {
                Reason = ExclusionReason.RequiresHighQualityPal,
                Comment = $"Excluded by {nameof(PalStopperName)}",
                ExcludedAt = DateTime.UtcNow,
            });
        }

        if (HasStopperOfType(jobs, NonConformingPreApprovalStopperMessage))
        {
            exclusions.Add(new Exclusion
            {
                Reason = ExclusionReason.NonConformingPreApproval,
                Comment = $"Excluded by {nameof(PalStopperName)}",
                ExcludedAt = DateTime.UtcNow,
            });
        }

        if (ValJobFailed(jobs))
        {
            exclusions.Add(new Exclusion
            {
                Reason = ExclusionReason.AmpJobFailed,
                Comment = $"Excluded by {nameof(PalStopperName)}",
                ExcludedAt = DateTime.UtcNow,
            });
        }

        if (await hasVerifiedApprovalLetter)
        {
            exclusions.Add(new Exclusion
            {
                Reason = ExclusionReason.VerifiedApprovalLetter,
                Comment = $"Excluded by {nameof(PalStopperName)}",
                ExcludedAt = DateTime.UtcNow,
            });
        }

        return new ExclusionDecision(exclusions, persist: exclusions.Count > 0);
    }

    private static bool HasStopperOfType(IEnumerable<JobDetails> jobs, string stopperMessage)
    {
        var mostRecentPalJob = MostRecentJobOfType(jobs, JobType.PrintPreApprovalLetter);
        if (mostRecentPalJob is null)
        {
            return false;
        }

        if (mostRecentPalJob.RequestStatus is RequestStatus.Complete or RequestStatus.CompletedAwaitingDocId)
        {
            var nonConformingPreApprovalStopper = mostRecentPalJob.Stoppers
                .Any(stopper => stopper.IsOverridden is not true &&
                                stopper.StopperName is PalStopperName &&
                                stopper.StopperMessage == stopperMessage);

            return nonConformingPreApprovalStopper;
        }

        return false;
    }

    private static bool ValJobFailed(IEnumerable<JobDetails> jobs)
    {
        var mostRecentValOptIn = MostRecentJobOfType(jobs, JobType.ValOptIn);
        return mostRecentValOptIn?.RequestStatus is RequestStatus.Failed;
    }

    private static LoanStopper? MostRecentJobOfType(IEnumerable<JobDetails> jobs, JobType jobType)
    {
        return jobs.Where(job => job.JobType == jobType)
            .Cast<LoanStopper>()
            .OrderByDescending(job => job.CreatedAt)
            .FirstOrDefault();
    }

    private async Task<bool> HasVerifiedApprovalLetter(StateContainer stateContainer)
    {
        if (!_featureEnabledConfigProvider.IsFeatureEnabled(FeatureName.CheckValTrackingItemStatusHistory))
        {
            return false;
        }

        var initialState = await stateContainer.Get<InitialLoanState>();

        try
        {
            var valTrackingItemStatusHistory = await _loanDataClient.GetTrackingItemStatusHistory(
                initialState.LoanNumber, ALReviewTrackingItemNumber);
            if (valTrackingItemStatusHistory is null || !valTrackingItemStatusHistory.Any())
            {
                return false;
            }

            var trackingItemStatuses = valTrackingItemStatusHistory.Select(item => item.StatusName);
            return ValTrackingItemStatuses.Overlaps(trackingItemStatuses);
        }
        catch (RequestNotFoundException)
        {
            return false;
        }
    }
}
