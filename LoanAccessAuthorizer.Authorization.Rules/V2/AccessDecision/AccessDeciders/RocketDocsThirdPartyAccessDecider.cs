using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class RocketDocsThirdPartyAccessDecider : IAccessDecider
{
    private readonly IApplicationAuthorizerDecisionStore _applicationAuthorizerDecisionStore;
    private readonly ILogger _logger;

    public string AccessDecisionId => ApplicationId.RocketDocsThirdParty;

    public RocketDocsThirdPartyAccessDecider(IApplicationAuthorizerDecisionStore applicationAuthorizerDecisionStore,
         ILogger<RocketDocsW2ExtractionAccessDecider> logger)
    {
        _applicationAuthorizerDecisionStore = applicationAuthorizerDecisionStore;
        _logger = logger;
    }

    public async Task<bool> GetAccessDecision(AccessDecisionContainer accessContainer)
    {
        var initialLoanDataTask = accessContainer.StateContainer.Get<InitialLoanState>();
        var isInDocIngestionThirdPartyPilotTask = accessContainer.GetAccessDecision(ApplicationId.DocIngestionThirdParty);

        await Task.WhenAll(initialLoanDataTask, isInDocIngestionThirdPartyPilotTask);

        return await StoreAndLogDecision(initialLoanDataTask.Result.LoanNumber, !isInDocIngestionThirdPartyPilotTask.Result);
    }

    private async Task<bool> StoreAndLogDecision(string loanNumber, bool access)
    {
        await _applicationAuthorizerDecisionStore.StoreDecision(loanNumber, AccessDecisionId, access);
        _logger.LogInformation("{App} Pilot decision ({decision}) persisted", AccessDecisionId, access);

        return access;
    }
}
