using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.Extensions;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class LegacyIdBridgeAccessDecider : IExclusionDecider
{
    private readonly IPilotCheck _pilotCheck;
    public string AccessDecisionId => ApplicationId.LegacyIdBridge;

    public LegacyIdBridgeAccessDecider(PilotCheckFactory pilotCheckFactory)
    {
        _pilotCheck = pilotCheckFactory(PilotCheckType.Normal, AccessDecisionId);
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var (isRlbLoan, hasLoanData) = await IsRlbLoan(container);
        var exclusionDecision = new ExclusionDecision(hasLoanData);

        if (!isRlbLoan)
        {
            exclusionDecision.Add(ExclusionReason.NotInPilot, GetType());
        }

        return exclusionDecision;
    }

    private static async Task<(bool, bool)> IsRlbLoan(AccessDecisionContainer container)
    {
        return (await container.StateContainer.IsRocketLogicBankingLoan());
    }
}
