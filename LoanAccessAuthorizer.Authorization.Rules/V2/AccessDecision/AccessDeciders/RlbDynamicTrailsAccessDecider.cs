using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot.Utils;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class RlbDynamicTrailsAccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.RlbDynamicTrails;

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer accessContainer)
    {
        // RlbDynamicTrails pilot is fully rolled out
        return new ExclusionDecision();
    }
}
