using LoanAccessAuthorizer.Authorization.Rules.V2.State.Extensions;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.MortgageClient;

public class MCAddressAccessDecider : IAccessDecider, IClearDecision
{
    public string AccessDecisionId => ApplicationId.MCAddress;
    private readonly IApplicationAuthorizerDecisionStore _decisionStore;

    public MCAddressAccessDecider(IApplicationAuthorizerDecisionStore decisionStore)
    {
        _decisionStore = decisionStore;
    }

    public Task<bool> CanClearDecision(AccessDecisionContainer accessContainer) =>
        accessContainer.StateContainer.ApplicationHasTrueCachedDecision(
            _decisionStore,
            AccessDecisionId
        );

    public async Task<bool> GetAccessDecision(AccessDecisionContainer accessContainer)
    {
        var initialLoanState = await accessContainer.StateContainer.Get<InitialLoanState>();
        var storedDecision = await _decisionStore.GetDecision(
            initialLoanState.LoanNumber,
            AccessDecisionId
        );
        return storedDecision
            ?? await accessContainer.GetAccessDecision(ApplicationId.PersonalInformation);
    }
}
