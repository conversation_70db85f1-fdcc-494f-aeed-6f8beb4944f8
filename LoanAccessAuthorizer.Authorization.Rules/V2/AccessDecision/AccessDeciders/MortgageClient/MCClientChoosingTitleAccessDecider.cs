namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.MortgageClient;

public class MCClientChoosingTitleAccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.MCClientChoosingTitle;

    public Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        return Task.FromResult(new ExclusionDecision(false));
    }
}
