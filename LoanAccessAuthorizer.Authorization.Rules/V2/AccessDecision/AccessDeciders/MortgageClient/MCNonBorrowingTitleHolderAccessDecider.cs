namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.MortgageClient;

public class MCNonBorrowingTitleHolderAccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.MCNonBorrowingTitleHolder;
    public Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        return Task.FromResult(new ExclusionDecision(false));
    }
}
