using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.Extensions;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.MortgageClient;

public class DemographicsAccessDecider : IAccessDecider
{
    public string AccessDecisionId => ApplicationId.Demographics;

    public async Task<bool> GetAccessDecision(AccessDecisionContainer accessContainer)
    {
        return await accessContainer.GetAccessDecision(ApplicationId.MortgageClient);
    }
}
