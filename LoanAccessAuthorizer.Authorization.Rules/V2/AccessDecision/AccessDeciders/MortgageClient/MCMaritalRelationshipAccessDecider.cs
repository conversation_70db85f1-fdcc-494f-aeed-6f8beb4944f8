using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;
using LoanAccessAuthorizer.Exclusions.Models;
using Microsoft.Extensions.Logging;
using Exclusion = LoanAccessAuthorizer.Domain.PilotExclusions.Models.Exclusion;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.MortgageClient;

public class MCMaritalRelationshipAccessDecider : IExclusionDecider, IClearDecision
{
    private readonly ILogger<MCMaritalRelationshipAccessDecider> _logger;
    public string AccessDecisionId => ApplicationId.MCMaritalRelationship;

    public MCMaritalRelationshipAccessDecider(ILogger<MCMaritalRelationshipAccessDecider> logger)
    {
        _logger = logger;
    }

    public async Task<bool> CanClearDecision(AccessDecisionContainer accessContainer)
    {
        var exclusionInformation = await accessContainer.StateContainer.Get<ExclusionInformation>();
        return !exclusionInformation.IsExcluded(AccessDecisionId);
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var piDecision = await container.GetAccessDecision(ApplicationId.PersonalInformation);
        if (!piDecision)
        {
            return NotInPilot(false, ExclusionReason.NotInPilot, "Not In PI Pilot");
        }

        var loanStatuses = await container.StateContainer.Get<AmpLoanStatusCollection>();
        var loanHasFolderReceived = loanStatuses.Any(
            status => status.LoanStatusId == LoanStatus.FolderReceived.ToString()
        );
        return loanHasFolderReceived
            ? NotInPilot(true, ExclusionReason.TitleMayBeVested, "Title may be vested")
            : InPilot();
    }

    private static ExclusionDecision NotInPilot(
        bool shouldPersist,
        ExclusionReason reason,
        string comment
    )
    {
        var exclusionDecision = new ExclusionDecision(shouldPersist)
        {
            new Exclusion()
            {
                Reason = reason,
                Comment = comment,
                ExcludedAt = DateTime.Now
            }
        };
        return exclusionDecision;
    }

    private static ExclusionDecision InPilot()
    {
        return new ExclusionDecision(false);
    }
}
