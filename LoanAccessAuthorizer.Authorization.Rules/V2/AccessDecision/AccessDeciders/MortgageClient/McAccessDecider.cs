using DecisionServices.Core.StateProvider;
using LoanAccessAuthorizer.Authorization.Rules.Configuration;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.Extensions;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.MortgageClient;

public class McAccessDecider : IAccessDecider, IClearDecision
{
    private const string RocketProTpoAppId = "206605";

    public string AccessDecisionId => ApplicationId.MortgageClient;

    private readonly IApplicationAuthorizerDecisionStore _decisionStore;

    private readonly MCOptions _options;

    private readonly ILogger<McAccessDecider> _logger;

    public McAccessDecider(
        IApplicationAuthorizerDecisionStore decisionStore,
        IOptionsSnapshot<MCOptions> options,
        ILogger<McAccessDecider> logger
    )
    {
        _decisionStore = decisionStore;
        _options = options.Value;
        _logger = logger;
    }

    public Task<bool> CanClearDecision(AccessDecisionContainer accessContainer) =>
        accessContainer.StateContainer.ApplicationHasTrueCachedDecision(
            _decisionStore,
            AccessDecisionId
        );

    public async Task<bool> GetAccessDecision(AccessDecisionContainer accessContainer)
    {
        var initialLoanState = await accessContainer.StateContainer.Get<InitialLoanState>();
        var storedDecision = await _decisionStore.GetDecision(
            initialLoanState.LoanNumber,
            AccessDecisionId
        );
        if (storedDecision.HasValue)
        {
            return storedDecision.Value;
        }

        var decisionDetails = await CalculateDecision(accessContainer.StateContainer);
        _logger.LogInformation(
            "Decision determined: {AccessDecisionId} {@DecisionDetails}",
            AccessDecisionId,
            decisionDetails
        );
        if (decisionDetails.Persist)
        {
            await _decisionStore.StoreDecision(
                initialLoanState.LoanNumber,
                AccessDecisionId,
                decisionDetails.Decision
            );
        }
        return decisionDetails.Decision;
    }

    private async Task<MCDecisionDetails> CalculateDecision(StateContainer stateContainer)
    {
        var rlApiLoanTask = stateContainer.Get<RocketLogicLoanDetails>();
        var rlbLoanTask = stateContainer.IsRocketLogicBankingLoan();
        var isInAmpTask = IsLoanInAmp(stateContainer);

        var rlApiLoan = await rlApiLoanTask;
        var rlbLoan = await rlbLoanTask;
        var isInAmp = await isInAmpTask;

        var decisionDetails = new MCDecisionDetails
        {
            IsRLApiLoan = rlApiLoan?.IsRocketLogicApiLoan ?? false,
            RLApiLoanCreatedOn = rlApiLoan?.CreatedDateTime?.ToUniversalTime().DateTime,
            IsTpoRLApiLoan = rlApiLoan is not null && rlApiLoan.CreatedByAppId == RocketProTpoAppId,
            IsRlbLoan = rlbLoan.isRocketLogicBankingLoan,
            HasRlbDetails = rlbLoan.hasRocketLogicBankingDetails,
            IsLoanInAmp = isInAmp,
            TpoEnabledOn = _options.TpoMCEnabledOn
        };

        if (decisionDetails.IsRlbLoan)
        {
            decisionDetails.Decision = true;
            decisionDetails.Persist = true;
            return decisionDetails;
        }

        if (decisionDetails.IsRLApiLoan)
        {
            decisionDetails.Decision =
                !(decisionDetails.IsTpoRLApiLoan.Value)
                || (
                    decisionDetails.TpoEnabledOn is not null
                    && decisionDetails.RLApiLoanCreatedOn is not null
                    && decisionDetails.RLApiLoanCreatedOn >= decisionDetails.TpoEnabledOn
                );
            decisionDetails.Persist = true;
            return decisionDetails;
        }

        if (!isInAmp)
        {
            // The loan is not in RL API, RLB, or AMP. Can't be in pilot, because it doesn't exist anywhere yet.
            // Don't persist because it might be a future valid loan number.
            decisionDetails.Decision = false;
            decisionDetails.Persist = false;
            return decisionDetails;
        }

        var loanPilotDateTask = GetAmpLoanPilotDateAsync(stateContainer);
        var isSchwabLoanTask = IsSchwabLoan(stateContainer);

        var loanPilotDate = await loanPilotDateTask;
        var isSchwabLoan = await isSchwabLoanTask;

        decisionDetails.AmpInitialContactOn = loanPilotDate;
        decisionDetails.IsSchwabLoan = isSchwabLoan;
        decisionDetails.SchwabEnabledOn = _options.SchwabLolaMCEnabledOn;

        if (loanPilotDate is null)
        {
            // At this point the loan exists in AMP, but we don't know the app/initial-contact date yet.
            // So we can't tell whether to use the LOLA PI logic or not yet.
            decisionDetails.Decision = false;
            decisionDetails.Persist = false;
            return decisionDetails;
        }

        if (isSchwabLoan && IsSchwabLolaMCEnabledForLoan(loanPilotDate.Value))
        {
            decisionDetails.Decision = true;
            decisionDetails.Persist = true;
            return decisionDetails;
        }

        decisionDetails.Decision = !isSchwabLoan;
        decisionDetails.Persist = true;
        return decisionDetails;
    }

    private static Task<bool> IsLoanInAmp(StateContainer stateContainer) =>
        stateContainer.Get<bool>(ExplicitStateProviderIds.IsLoanInAmp);

    private static Task<DateTime?> GetAmpLoanPilotDateAsync(StateContainer stateContainer) =>
        stateContainer.Get<DateTime?>(ExplicitStateProviderIds.AmpInitialContactDate);

    private bool IsSchwabLolaMCEnabledForLoan(DateTime pilotDate) => _options.SchwabLolaMCEnabledOn <= pilotDate;

    private static async Task<bool> IsSchwabLoan(StateContainer stateContainer)
    {
        var ampKeyLoanInfo = await stateContainer.Get<AmpKeyLoanInfo>();

        return string.Equals(
            ampKeyLoanInfo.LoanChannel,
            "Schwab",
            StringComparison.OrdinalIgnoreCase
        )
        || string.Equals(
            ampKeyLoanInfo.LoanChannel,
            "Cadillac",
            StringComparison.OrdinalIgnoreCase
        );
    }
}
