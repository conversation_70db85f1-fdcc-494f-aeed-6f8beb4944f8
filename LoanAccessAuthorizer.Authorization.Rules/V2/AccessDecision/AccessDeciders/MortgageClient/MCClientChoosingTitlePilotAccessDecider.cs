using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.MortgageClient;

public class MCClientChoosingTitlePilotAccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.MCClientChoosingTitlePilot;

    private readonly IPilotCheck _pilotCheck;
    private readonly Type _exclusionDeciderType = typeof(MCClientChoosingTitlePilotAccessDecider);

    public MCClientChoosingTitlePilotAccessDecider(PilotCheckFactory pilotCheckFactory)
    {
        _pilotCheck = pilotCheckFactory(PilotCheckType.Leader, AccessDecisionId);
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var decision = new ExclusionDecision(persist: true);
        var accessDecision = await GetAccessDecision(container);

        if (!accessDecision)
        {
            decision.Add(ExclusionReason.NotInPilot, _exclusionDeciderType);
            return decision;
        }

        var isLoanInAmp = await container.StateContainer.Get<bool>(ExplicitStateProviderIds.IsLoanInAmp);
        if (!isLoanInAmp)
        {
            return decision;
        }

        if (await HasReachedFolderReceived(container))
        {
            decision.Add(ExclusionReason.UnsupportedLoanStatus, _exclusionDeciderType);
            return decision;
        }

        if (await IsTpoLoan(container))
        {
            decision.Add(ExclusionReason.ThirdPartyOrigination, _exclusionDeciderType);
            return decision;
        }

        return decision;
    }

    private static async Task<bool> HasReachedFolderReceived(AccessDecisionContainer container)
    {
        var loanStatuses = await container.StateContainer.Get<AmpLoanStatusCollection>();
        return loanStatuses.Any(status => status.LoanStatusId == LoanStatus.FolderReceived.ToString());
    }

    private static async Task<bool> IsTpoLoan(AccessDecisionContainer container)
    {
        var thirdPartyOrigination = await container.StateContainer.Get<ThirdPartyOrigination>();
        return thirdPartyOrigination?.IsRocketProTPOLoan == true ||
               thirdPartyOrigination?.IsCorrespondentLoan == true;
    }

    private async Task<bool> GetAccessDecision(AccessDecisionContainer accessContainer)
    {
        var (accessDecision, _) = await _pilotCheck.IsInPilot(accessContainer);
        return accessDecision;
    }
}
