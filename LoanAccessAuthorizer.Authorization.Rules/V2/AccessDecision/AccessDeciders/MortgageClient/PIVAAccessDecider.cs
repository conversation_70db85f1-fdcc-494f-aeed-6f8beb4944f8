﻿using LoanAccessAuthorizer.Authorization.Rules.V2.State.Extensions;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.MortgageClient;

public class PIVAAccessDecider : IAccessDecider
{
    public string AccessDecisionId => ApplicationId.PersonalInformationVeteransAffairs;
    private readonly IApplicationAuthorizerDecisionStore _decisionStore;

    public PIVAAccessDecider(IApplicationAuthorizerDecisionStore decisionStore)
    {
        _decisionStore = decisionStore;
    }

    public async Task<bool> GetAccessDecision(AccessDecisionContainer accessContainer)
    {
        var initialLoanState = await accessContainer.StateContainer.Get<InitialLoanState>();
        var storedDecision = await _decisionStore.GetDecision(
            initialLoanState.LoanNumber,
            AccessDecisionId
        );
        return storedDecision
            ?? await accessContainer.GetAccessDecision(ApplicationId.PersonalInformation);
    }
}
