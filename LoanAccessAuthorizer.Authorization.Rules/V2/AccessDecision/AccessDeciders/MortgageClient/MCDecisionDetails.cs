namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.MortgageClient;

internal class MCDecisionDetails
{
    public bool IsRlbLoan { get; init; }
    public bool IsRLApiLoan { get; init; }
    public bool? IsTpoRLApiLoan { get; init; }
    public bool HasRlbDetails { get; init; }
    public bool IsLoanInAmp { get; init; }
    public DateTime? RLApiLoanCreatedOn { get; set; }
    public DateTime? AmpInitialContactOn { get; set; }
    public bool? IsSchwabLoan { get; set; }
    public DateTime? SchwabEnabledOn { get; set; }
    public DateTime? TpoEnabledOn { get; set; }
    public bool Decision { get; set; }
    public bool Persist { get; set; }
}
