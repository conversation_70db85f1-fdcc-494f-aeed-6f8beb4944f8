using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.MortgageClient;
public class VATitleInformationDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.VATitleInformation;
    private const ExclusionReason DecisionReason = ExclusionReason.NotInPilot;
    private const string ParentApplicationId = ApplicationId.PersonalInformation;
    private static readonly Type DeciderType = typeof(VATitleInformationDecider);
    private readonly IPilotCheck _pilotCheck;

    public VATitleInformationDecider(PilotCheckFactory pilotCheckFactory)
    {
        _pilotCheck = pilotCheckFactory(PilotCheckType.Normal, AccessDecisionId);
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var accessResult = await container.GetAccessDecision(ParentApplicationId);
        if (!accessResult.AccessDecision)
        {
            return new ExclusionDecision(persist: true)
            {
                { DecisionReason, DeciderType, $"Not in {ParentApplicationId} pilot" }
            };
        }

        var pilotCheckResult = await _pilotCheck.IsInPilot(container);
        if (!pilotCheckResult.IsInPilot)
        {
            return new ExclusionDecision(persist: true)
            {
                { DecisionReason, DeciderType, $"Not in {AccessDecisionId} pilot" }
            };
        }

        return new ExclusionDecision(persist: true);
    }
}
