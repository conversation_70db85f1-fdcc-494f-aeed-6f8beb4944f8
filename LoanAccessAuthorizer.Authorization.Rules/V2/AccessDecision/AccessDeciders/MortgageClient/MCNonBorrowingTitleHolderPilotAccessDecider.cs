using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;
using LoanPurpose = LoanAccessAuthorizer.RocketLogicApi.Models.LoanPurpose;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.MortgageClient;

public class MCNonBorrowingTitleHolderPilotAccessDecider: IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.MCNonBorrowingTitleHolderPilot;

    private readonly IPilotCheck _pilotCheck;
    private static readonly Type _exclusionDeciderType = typeof(MCNonBorrowingTitleHolderPilotAccessDecider);

    public MCNonBorrowingTitleHolderPilotAccessDecider(PilotCheckFactory pilotCheckFactory)
    {
        _pilotCheck = pilotCheckFactory(PilotCheckType.Leader, AccessDecisionId);
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var decision = new ExclusionDecision(persist: true);
        var (accessDecision, _) = await _pilotCheck.IsInPilot(container);

        if (!accessDecision)
        {
            decision.Add(ExclusionReason.NotInPilot, _exclusionDeciderType);
            return decision;
        }

        var rocketLogicLoanDetailsTask = container.StateContainer.Get<RocketLogicLoanDetails>();
        var loanPurpose = (await rocketLogicLoanDetailsTask)?.LoanPurpose;

        if (!IsRefinanceLoan(loanPurpose))
        {
            decision.Add(ExclusionReason.UnsupportedLoanPurpose, _exclusionDeciderType);
            return decision;
        }

        if (await HasReachedFolderReceived(container))
        {
            decision.Add(ExclusionReason.UnsupportedLoanStatus, _exclusionDeciderType);
            return decision;
        }

        return decision;
    }

    private static async Task<bool> HasReachedFolderReceived(AccessDecisionContainer container)
    {
        var loanStatuses = await container.StateContainer.Get<AmpLoanStatusCollection>();
        return loanStatuses.Any(status => status.LoanStatusId == LoanStatus.FolderReceived.ToString());
    }
    private static bool IsRefinanceLoan(LoanPurpose? loanPurpose) =>
        loanPurpose is LoanPurpose.Refinance;
}
