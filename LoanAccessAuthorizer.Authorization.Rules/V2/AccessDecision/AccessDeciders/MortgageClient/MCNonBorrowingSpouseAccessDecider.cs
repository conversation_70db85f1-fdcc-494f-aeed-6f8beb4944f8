namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.MortgageClient;

public class MCNonBorrowingSpouseAccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.MCNonBorrowingSpouse;
    public Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        return Task.FromResult(new ExclusionDecision(false));
    }
}
