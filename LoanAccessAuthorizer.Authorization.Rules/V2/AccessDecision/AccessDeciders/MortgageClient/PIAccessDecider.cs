using DecisionServices.Core.StateProvider;
using LoanAccessAuthorizer.Authorization.Rules.Configuration;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.Extensions;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.MortgageClient;

public class PIAccessDecider : IAccessDecider, IClearDecision
{
    private const string RocketProTpoAppId = "206605";

    public string AccessDecisionId => ApplicationId.PersonalInformation;
    private readonly IApplicationAuthorizerDecisionStore _decisionStore;
    private readonly PIOptions _options;
    private readonly ILogger<PIAccessDecider> _logger;

    public PIAccessDecider(
        IApplicationAuthorizerDecisionStore decisionStore,
        IOptionsSnapshot<PIOptions> options,
        ILogger<PIAccessDecider> logger
    )
    {
        _decisionStore = decisionStore;
        _options = options.Value;
        _logger = logger;
    }

    public Task<bool> CanClearDecision(AccessDecisionContainer accessContainer) =>
        accessContainer.StateContainer.ApplicationHasTrueCachedDecision(
            _decisionStore,
            AccessDecisionId
        );

    public async Task<bool> GetAccessDecision(AccessDecisionContainer accessContainer)
    {
        var stateContainer = accessContainer.StateContainer;
        var initialLoanState = await stateContainer.Get<InitialLoanState>();
        var storedDecision = await _decisionStore.GetDecision(
            initialLoanState.LoanNumber,
            AccessDecisionId
        );
        if (storedDecision.HasValue)
        {
            return storedDecision.Value;
        }

        var decisionDetails = await CalculateNewDecision(accessContainer.StateContainer);
        _logger.LogInformation(
            "Decision determined: {AccessDecisionId} {@DecisionDetails}",
            AccessDecisionId,
            decisionDetails
        );
        if (decisionDetails.Persist)
        {
            await _decisionStore.StoreDecision(
                initialLoanState.LoanNumber,
                AccessDecisionId,
                decisionDetails.Decision
            );
        }
        return decisionDetails.Decision;
    }

    private async Task<PIDecisionDetails> CalculateNewDecision(StateContainer stateContainer)
    {
        var rlApiLoanTask = stateContainer.Get<RocketLogicLoanDetails>();
        var rlbLoanTask = stateContainer.IsRocketLogicBankingLoan();
        var isInAmpTask = IsLoanInAmp(stateContainer);

        var rlApiLoan = await rlApiLoanTask;
        var rlbLoan = await rlbLoanTask;
        var isInAmp = await isInAmpTask;

        var decisionDetails = new PIDecisionDetails
        {
            IsRLApiLoan = rlApiLoan?.IsRocketLogicApiLoan ?? false,
            RLApiLoanCreatedOn = rlApiLoan?.CreatedDateTime?.ToUniversalTime().DateTime,
            IsTpoRLApiLoan = rlApiLoan is not null && rlApiLoan.CreatedByAppId == RocketProTpoAppId,
            IsRlbLoan = rlbLoan.isRocketLogicBankingLoan,
            HasRlbDetails = rlbLoan.hasRocketLogicBankingDetails,
            IsLoanInAmp = isInAmp,
            TpoEnabledOn = _options.TpoPIEnabledOn
        };

        if (decisionDetails.IsRlbLoan)
        {
            decisionDetails.Decision = true;
            decisionDetails.Persist = true;
            return decisionDetails;
        }

        if (decisionDetails.IsRLApiLoan)
        {
            decisionDetails.Decision =
                !(decisionDetails.IsTpoRLApiLoan.Value)
                || (
                    decisionDetails.TpoEnabledOn is not null
                    && decisionDetails.RLApiLoanCreatedOn is not null
                    && decisionDetails.RLApiLoanCreatedOn >= decisionDetails.TpoEnabledOn
                );
            decisionDetails.Persist = true;
            return decisionDetails;
        }

        if (!isInAmp)
        {
            // The loan is not in RL API, RLB, or AMP. Can't be in pilot, because it doesn't exist anywhere yet.
            // Don't persist because it might be a future valid loan number.
            decisionDetails.Decision = false;
            decisionDetails.Persist = false;
            return decisionDetails;
        }

        // For everything below this point, the loan exists in AMP and is not an RL API or RLB loan.
        var isLoanCopiedInAmpTask = IsLoanCopiedInAmp(stateContainer);
        var loanPilotDateTask = GetAmpLoanPilotDateAsync(stateContainer);
        var isSchwabLoanTask = IsSchwabChannelLoan(stateContainer);

        var isLoanCopiedInAmp = await isLoanCopiedInAmpTask;
        var loanPilotDate = await loanPilotDateTask;
        var isSchwabLoan = await isSchwabLoanTask;

        decisionDetails.IsLoanCopiedInAmp = isLoanCopiedInAmp;
        decisionDetails.AmpInitialContactOn = loanPilotDate;
        decisionDetails.LolaEnabledOn = _options.LolaPIEnabledOn;
        decisionDetails.SchwabEnabledOn = _options.SchwabLolaPIEnabledOn;
        decisionDetails.IsSchwabLoan = isSchwabLoan;

        if (isLoanCopiedInAmp ?? false)
        {
            // Copied loans are never in PI pilot, because the data won't exist in MC.
            decisionDetails.Decision = false;
            decisionDetails.Persist = true;
            return decisionDetails;
        }

        if (loanPilotDate is null)
        {
            // At this point the loan exists in AMP, but we don't know the app/initial-contact date yet.
            // So we can't tell whether to use the LOLA PI logic or not yet.
            decisionDetails.Decision = false;
            decisionDetails.Persist = false;
            return decisionDetails;
        }

        if (!IsLolaPIEnabledForLoan(loanPilotDate.Value))
        {
            // If we get here, the loan is in AMP, but wasn't in RLB or RL API, and LOLA PI is not enabled.
            // The loan is not in pilot. Persist because the AMP loan shouldn't show up in RL API or RLB later.
            decisionDetails.Decision = false;
            decisionDetails.Persist = true;
            return decisionDetails;
        }

        if (IsSchwabLolaPIEnabledForLoan(loanPilotDate.Value))
        {
            // No need to check if the loan is a Schwab channel loan, because if we get here
            // all LOLA loans, including Schwab channel loans are in PI pilot.
            decisionDetails.Decision = true;
            decisionDetails.Persist = true;
            return decisionDetails;
        }

        if (!isSchwabLoan)
        {
            // LOLA PI is enabled, and the loan is not a Schwab channel loan. The loan is in PI pilot.
            decisionDetails.Decision = true;
            decisionDetails.Persist = true;
            return decisionDetails;
        }

        // LOLA PI is enabled, but the loan is a Schwab channel loan, and Schwab LOLA PI is not enabled for the loan.
        // Exclude from pilot.
        decisionDetails.Decision = false;
        decisionDetails.Persist = true;
        return decisionDetails;
    }

    private static Task<DateTime?> GetAmpLoanPilotDateAsync(StateContainer stateContainer)
    {
        return stateContainer.Get<DateTime?>(ExplicitStateProviderIds.AmpInitialContactDate);
    }

    private bool IsLolaPIEnabledForLoan(DateTime pilotDate) => _options.LolaPIEnabledOn <= pilotDate;

    private bool IsSchwabLolaPIEnabledForLoan(DateTime pilotDate) => _options.SchwabLolaPIEnabledOn <= pilotDate;

    private static async Task<bool?> IsLoanCopiedInAmp(StateContainer stateContainer)
    {
        return await stateContainer.Get<bool?>(ExplicitStateProviderIds.IsLoanCopiedInAmp);
    }

    private static Task<bool> IsLoanInAmp(StateContainer stateContainer) =>
        stateContainer.Get<bool>(ExplicitStateProviderIds.IsLoanInAmp);

    private static async Task<bool> IsSchwabChannelLoan(StateContainer stateContainer)
    {
        var ampKeyLoanInfo = await stateContainer.Get<AmpKeyLoanInfo>();
        var loanChannel = ampKeyLoanInfo.LoanChannel;
        return string.Equals(loanChannel, "Schwab", StringComparison.OrdinalIgnoreCase) ||
            string.Equals(loanChannel, "Cadillac", StringComparison.OrdinalIgnoreCase);
    }
}
