using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.Extensions;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;
using LoanAccessAuthorizer.Exclusions.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.MortgageClient;

public class MortgageClientV3AccessDecider : IExclusionDecider, IClearDecision
{
    private readonly IPilotCheck _pilotCheck;
    public string AccessDecisionId => ApplicationId.MCV3;

    public MortgageClientV3AccessDecider(PilotCheckFactory pilotCheckFactory)
    {
        _pilotCheck = pilotCheckFactory(PilotCheckType.Leader, AccessDecisionId);
    }

    public async Task<bool> CanClearDecision(AccessDecisionContainer accessContainer)
    {
        var exclusionInformation = await accessContainer.StateContainer.Get<ExclusionInformation>();
        var isExcluded = exclusionInformation.IsExcluded(AccessDecisionId);

        return !isExcluded;
    }

    public async Task<ExclusionDecision> GetExclusionDecision(
        AccessDecisionContainer accessContainer
    )
    {
        var (isInPilot, hasLoanData) = await _pilotCheck.IsInPilot(accessContainer);

        var exclusion = new ExclusionDecision(hasLoanData);
        if (!isInPilot)
        {
            exclusion.Add(ExclusionReason.NotInPilot, GetType());
        }

        return exclusion;
    }
}
