using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class RlxpAssistantDecider : IExclusionDecider
{
    private readonly IPilotCheck _pilotCheck;
    public string AccessDecisionId => ApplicationId.RlxpAssistant;

    public RlxpAssistantDecider(PilotCheckFactory pilotCheckFactory)
    {
        _pilotCheck = pilotCheckFactory(PilotCheckType.Leader, AccessDecisionId);
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var initialState = await container.StateContainer.Get<InitialLoanState>();
        if (initialState.CallerId == null)
        {
            return new(false)
            {
                {
                    ExclusionReason.NotInPilot,
                    GetType(),
                    "A caller ID (Common ID or RHID header) is required to make a decision."
                },
            };
        }

        DateTime? pilotDeterminantDate;
        try
        {
            pilotDeterminantDate = await container.StateContainer.Get<DateTime?>(
                ExplicitStateProviderIds.ApplicationOrInitialContactDate);
        }
        catch
        {
            pilotDeterminantDate = DateTime.UtcNow;
        }

        var (userInPilot, _) = await _pilotCheck.IsInPilot(
            container,
            initialState.CallerId,
            pilotDeterminantDate);

        var exclusion = new ExclusionDecision(persist: userInPilot);
        if (!userInPilot)
        {
            exclusion.Add(
                ExclusionReason.NotInPilot,
                GetType(),
                "User accessing RL XP Assistant is not in pilot group."
            );
        }

        return exclusion;
    }
}
