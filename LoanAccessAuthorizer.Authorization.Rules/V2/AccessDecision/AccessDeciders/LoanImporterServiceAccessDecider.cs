using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using Microsoft.Extensions.Logging;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class LoanImporterServiceAccessDecider: LeaderPilotAccessDecider
{
    private static readonly string RocketProTpoAppId = "206605";

    public LoanImporterServiceAccessDecider(IApplicationAuthorizerDecisionStore decisionStore,
        PilotCheckFactory pilotCheckFactory, ILogger<LoanImporterServiceAccessDecider> logger) :
        base(decisionStore, pilotCheckFactory, logger) { }
    public override string AccessDecisionId => ApplicationId.LoanImporterService;

    protected override async Task<(bool isInPilot, bool shouldPersist)> DetermineAccessDecision(
        AccessDecisionContainer accessContainer)
    {
        var rlLoanDetails = await accessContainer.StateContainer.Get<RocketLogicLoanDetails>();

        if (rlLoanDetails?.IsRocketLogicApiLoan ?? false)
        {
            return (rlLoanDetails.CreatedByAppId != RocketProTpoAppId, true);
        }

        return await base.DetermineAccessDecision(accessContainer);
    }

    public override async Task<DateTime?> GetLoanDateForPilotComparison(AccessDecisionContainer container)
    {
        var rlLoanDetails = await container.StateContainer.Get<RocketLogicLoanDetails>();
        return rlLoanDetails?.CreatedDateTime?.DateTime;
    }
}
