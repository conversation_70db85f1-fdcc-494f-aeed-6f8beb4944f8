using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Domain.Models;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class SingleBureauAccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.SingleBureau;
    private readonly IPilotCheck _pilotCheck;

    public SingleBureauAccessDecider(PilotCheckFactory pilotCheckFactory)
    {
        _pilotCheck = pilotCheckFactory(PilotCheckType.Leader, AccessDecisionId);
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var isRmaLoan = await container.StateContainer.Get<bool?>(
            ExplicitStateProviderIds.IsRmaLoan
        );

        if (isRmaLoan == true)
        {
            return new ExclusionDecision(true);
        }

        var loanOfficerId = await container.StateContainer.Get<UserId>(
            ExplicitStateProviderIds.LoanOfficerCommonId
        );
        if (string.IsNullOrWhiteSpace(loanOfficerId.Id))
        {
            var isTpoLoan = await container.StateContainer.Get<bool>(ExplicitStateProviderIds.IsTpoLoan);
            if (isTpoLoan == true) {
                return new ExclusionDecision(true);
            }

            return new ExclusionDecision(false) { { ExclusionReason.NotInPilot, GetType(), "Loan has no common id" } };
        }

        var isSchwabLoanTask = container.StateContainer.Get<bool>(
            ExplicitStateProviderIds.IsSchwabLoan
        );
        var isSchwabLoan = await isSchwabLoanTask;

        var exclusionDecision = new ExclusionDecision(true);

        if (isSchwabLoan)
        {
            exclusionDecision.Add(ExclusionReason.NotInPilot, GetType(), "Loan is Schwab");
            return exclusionDecision;
        }

        var (isInPilot, _) = await _pilotCheck.IsInPilot(container);
        if (!isInPilot)
        {
            exclusionDecision.Add(ExclusionReason.NotInPilot, GetType());
        }

        return exclusionDecision;
    }
}
