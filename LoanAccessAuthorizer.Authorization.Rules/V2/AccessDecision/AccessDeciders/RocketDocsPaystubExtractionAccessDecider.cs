using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class RocketDocsPaystubExtractionAccessDecider : LeaderPilotAccessDecider
{
    public override string AccessDecisionId => ApplicationId.RocketDocsPaystub;
    public RocketDocsPaystubExtractionAccessDecider(IApplicationAuthorizerDecisionStore applicationAuthorizerDecisionStore,
        PilotCheckFactory pilotCheckFactory, ILogger<RocketDocsPaystubExtractionAccessDecider> logger) :
        base(applicationAuthorizerDecisionStore, pilotCheckFactory, logger)
    {
    }

    protected override async Task<(bool isInPilot, bool shouldPersist)> DetermineAccessDecision(AccessDecisionContainer container)
    {
        var decision = base.DetermineAccessDecision(container);
        var isInDocIngestionPaystubPilot = container.GetAccessDecision(ApplicationId.DocIngestionPaystub);

        await Task.WhenAll(decision, isInDocIngestionPaystubPilot);

        return (decision.Result.isInPilot && !isInDocIngestionPaystubPilot.Result, decision.Result.shouldPersist);
    }
}
