using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class BypassCaptivaAccessDecider : IAccessDecider
{
    private readonly IApplicationAuthorizerDecisionStore applicationAuthorizerDecisionStore;
    private readonly ILogger logger;

    public string AccessDecisionId => "BypassCaptiva";

    public BypassCaptivaAccessDecider(IApplicationAuthorizerDecisionStore applicationAuthorizerDecisionStore,
        ILogger<RocketDocsAccessDecider> logger)
    {
        this.applicationAuthorizerDecisionStore = applicationAuthorizerDecisionStore;
        this.logger = logger;
    }

    public async Task<bool> GetAccessDecision(AccessDecisionContainer accessContainer)
    {
        var initialLoanData = await accessContainer.StateContainer.Get<InitialLoanState>();

        var storedDecision = await applicationAuthorizerDecisionStore.GetDecision(initialLoanData.LoanNumber, AccessDecisionId);

        if (storedDecision.HasValue && !storedDecision.Value)
        {
            return storedDecision.Value;
        }

        var loanStatusesTask = accessContainer.StateContainer.Get<AmpLoanStatusCollection>();
        var loanDetailsTask = accessContainer.StateContainer.Get<AmpLoanDetails>();

        var loanStatuses = await loanStatusesTask;

        var isPastFolderReceived = loanStatuses
            .Any(status => status.LoanStatusId == $"{LoanStatus.FolderReceived}");

        var loanDetails = await loanDetailsTask;
        var isEmployeeLoan = loanDetails?.IsEmployeeLoan ?? false;

        var hasAccess = !isEmployeeLoan && !isPastFolderReceived;

        if (!hasAccess)
        {
            await applicationAuthorizerDecisionStore.StoreDecision(initialLoanData.LoanNumber, AccessDecisionId, hasAccess);

            logger.LogInformation("{App} Pilot decision ({decision}) persisted", AccessDecisionId, hasAccess);
        }

        return hasAccess;
    }
}
