using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class PQUWAccessDecider : PilotAccessDecider
{
    public PQUWAccessDecider(IApplicationAuthorizerDecisionStore applicationAuthorizerDecisionStore,
        PilotCheckFactory pilotCheckFactory, ILogger<PQUWAccessDecider> logger) : base(
        applicationAuthorizerDecisionStore, pilotCheckFactory, logger)
    {
    }

    public override string AccessDecisionId => ApplicationId.PropertyUnderwriting;
}
