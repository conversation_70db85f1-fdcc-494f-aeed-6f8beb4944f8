using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.Models;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class RlXpClientDataImportDecider : IExclusionDecider
{
    private readonly IPilotCheck _pilotCheck;
    public string AccessDecisionId => ApplicationId.RlxpClientDataImport;

    public RlXpClientDataImportDecider(PilotCheckFactory pilotCheckFactory)
    {
        _pilotCheck = pilotCheckFactory(PilotCheckType.Leader, AccessDecisionId);
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var initialState = await container.StateContainer.Get<InitialLoanState>();
        var callerId = initialState.CallerId;

        if (string.IsNullOrEmpty(callerId.Id))
        {
            return new(false)
            {
                {
                    ExclusionReason.NotInPilot,
                    GetType(),
                    "A commonId header is required to make a decision."
                }
            };
        }

        DateTime? initialContactDate = null;
        try
        {
            initialContactDate = await container.StateContainer.Get<DateTime?>(
                ExplicitStateProviderIds.ApplicationOrInitialContactDate
            );
        }
        catch (Exception) { }

        var (userInPilot, hasLoanData) = await _pilotCheck.IsInPilot(
            container,
            callerId,
            initialContactDate ?? DateTime.Now
        );

        var exclusion = new ExclusionDecision(hasLoanData);
        if (!userInPilot)
        {
            exclusion.Add(
                ExclusionReason.NotInPilot,
                GetType(),
                "User accessing RL XP Client Data Import is not in pilot group."
            );
        }

        return exclusion;
    }
}
