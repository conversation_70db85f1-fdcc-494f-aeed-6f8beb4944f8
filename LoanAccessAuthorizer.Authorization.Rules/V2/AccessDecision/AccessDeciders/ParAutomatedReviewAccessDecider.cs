#nullable enable

using System.Text.RegularExpressions;
using Ivc.DocumentStorage.Models.V1.Responses;
using LoanAccessAuthorizer.Authorization.Rules.Configuration;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.Extensions;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using ParOrch.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class ParAutomatedReviewAccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.ParAutomatedReview;

    private readonly ParAutomatedReviewConfiguration _config;
    private readonly ILogger<ParAutomatedReviewAccessDecider> _logger;

    internal const string AmpLoanPurposePurchase = "Purchase";
    internal const string AmpLoanPurposeNewConstruction = "Construction-Permanent";
    internal const string AmpLoanPurposeMortgageFirst = "Mortgage First";

    private static readonly Regex _alphaNumericRegex = new Regex(@"\w+");

    public ParAutomatedReviewAccessDecider(IOptionsSnapshot<ParAutomatedReviewConfiguration> config,
        ILogger<ParAutomatedReviewAccessDecider> logger)
    {
        _config = config.Value;
        _logger = logger;
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var pilotDeterminantDate = await container.StateContainer.Get<DateTime?>(ExplicitStateProviderIds.ApplicationOrInitialContactDate);
        if (pilotDeterminantDate is null || pilotDeterminantDate < _config.StartDate)
        {
            return new ExclusionDecision(persist: pilotDeterminantDate is not null)
            {
                { ExclusionReason.NotInPilot, typeof(ParAutomatedReviewAccessDecider), "Loan from before pilot start date" }
            };
        }

        var isLoanInAmp = await container.StateContainer.Get<bool>(ExplicitStateProviderIds.IsLoanInAmp);
        if (!isLoanInAmp)
        {
            return new ExclusionDecision(persist: false)
            {
                { ExclusionReason.NotInPilot, typeof(ParAutomatedReviewAccessDecider), "Loan not in Amp" }
            };
        }

        var (isRlbLoan, _) = await container.StateContainer.IsRocketLogicBankingLoan();
        if (!isRlbLoan)
        {
            return new ExclusionDecision(persist: true)
            {
                { ExclusionReason.NotInPilot, typeof(ParAutomatedReviewAccessDecider), "Loan is not an RLB loan" }
            };
        }

        var loanDetails = await container.StateContainer.Get<AmpLoanDetails>();
        if (loanDetails.IsEmployeeLoan)
        {
            return new ExclusionDecision(persist: true)
            {
                { ExclusionReason.NotInPilot, typeof(ParAutomatedReviewAccessDecider), "Loan is a Team Member loan" }
            };
        }

        ExclusionReason? loanPurposeExclusion = loanDetails.LoanPurpose switch
        {
            AmpLoanPurposePurchase => null,
            AmpLoanPurposeMortgageFirst => null,
            AmpLoanPurposeNewConstruction => ExclusionReason.NewConstruction,
            _ => ExclusionReason.UnsupportedLoanPurpose
        };
        // new construction is checked below
        if (loanPurposeExclusion is not null and not ExclusionReason.NewConstruction)
        {
            return new ExclusionDecision(persist: true)
            {
                { loanPurposeExclusion.Value, typeof(ParAutomatedReviewAccessDecider), loanDetails.LoanPurpose }
            };
        }

        _logger.LogInformation("Using {@documentTypes}", _config.DocumentTypes);

        // Exclude loans from the pilot if there isn't a PA or ICC associated with the loan.
        // Many of the data points checked below will only be set once these documents
        // have beeen associated to the loan.
        var rocketLogicDocs = await container.StateContainer.Get<RocketLogicDocuments>();
        var purchaseAgreements = GetDocumentsOfType(rocketLogicDocs, _config.DocumentTypes?.PurchaseAgreement);
        var initialCallChecklists = GetDocumentsOfType(rocketLogicDocs, _config.DocumentTypes?.InitialCallChecklist);
        var purchaseAgreementAddendums = GetDocumentsOfType(rocketLogicDocs, _config.DocumentTypes?.PurchaseAgreementAddendum);
        var anyConsideredDocsPresent = purchaseAgreements.Any() || initialCallChecklists.Any() ||
            (_config.AddendumsEnabled && purchaseAgreementAddendums.Any());
        if (!anyConsideredDocsPresent)
        {
            return new ExclusionDecision(persist: false);
        }

        var subjectPropertyTask = container.StateContainer.Get<OpenAmpSubjectProperty>();
        var openAmpLoanPurchaseDetailTask = container.StateContainer.Get<AmpLoanPurchaseDetail>();
        var qualificationGroupsTask = container.StateContainer.Get<QualificationGroupSet>();
        var collatedPurchaseAgreementTask = container.StateContainer.Get<CalculatedParData>();

        var exclusions = new List<Exclusion>();

        if (loanPurposeExclusion is ExclusionReason.NewConstruction)
        {
            exclusions.Add(new Exclusion
            {
                Reason = ExclusionReason.NewConstruction,
                ExcludedAt = DateTime.UtcNow,
            });
        }

        var subjectProperty = await subjectPropertyTask;
        if (subjectProperty is not null && !_config.SupportedStates.Contains(ToStateCode(subjectProperty.State)))
        {
            exclusions.Add(new Exclusion
            {
                Reason = ExclusionReason.UnsupportedState,
                ExcludedAt = DateTime.UtcNow,
            });
        }

        var openAmpLoanPurchaseDetail = await openAmpLoanPurchaseDetailTask;
        if (openAmpLoanPurchaseDetail?.IsShortSale == true)
        {
            exclusions.Add(new Exclusion
            {
                Reason = ExclusionReason.ShortSale,
                ExcludedAt = DateTime.UtcNow,
            });
        }

        if (!_config.AddendumsEnabled && purchaseAgreementAddendums.Any())
        {
            exclusions.Add(new Exclusion
            {
                Reason = ExclusionReason.UnsupportedDocumentType,
                ExcludedAt = DateTime.UtcNow,
                Comment = "Purchase Agreement Addendums are not supported"
            });
        }

        if (initialCallChecklists.Any(doc => doc.DocumentIssues?.Any() ?? false))
        {
            exclusions.Add(new Exclusion
            {
                Reason = ExclusionReason.IssueOnDocument,
                ExcludedAt = DateTime.UtcNow,
                Comment = "An issue was created on an ICC document"
            });
        }

        var qualificationGroups = await qualificationGroupsTask;
        if (!_config.SupportedQualificationGroups.Overlaps(qualificationGroups))
        {
            exclusions.Add(new Exclusion
            {
                Reason = ExclusionReason.UnsupportedQualificationGroup,
                ExcludedAt = DateTime.UtcNow
            });
        }

        var initialState = await container.StateContainer.Get<InitialLoanState>();
        var calculatedParData = await collatedPurchaseAgreementTask;

        if (calculatedParData is not null)
        {
            if (!IsPurchaseAgreementSupported(calculatedParData.Data))
            {
                _logger.LogInformation("Removing {loanIdentifier} from PAR Automated Review pilot due to unsupported automation {@dataPoints}",
                    initialState.LoanNumber, calculatedParData);
                exclusions.Add(new Exclusion
                {
                    Reason = ExclusionReason.UnsupportedAutomation,
                    ExcludedAt = DateTime.UtcNow
                });
            }

            if (calculatedParData.Issues.Any(issue => issue.Type is ParIssueType.DocumentHasIssues or ParIssueType.PaDocumentNotValid))
            {
                exclusions.Add(new Exclusion
                {
                    Reason = ExclusionReason.IssueOnDocument,
                    ExcludedAt = DateTime.UtcNow
                });
            }
        }

        var decision = new ExclusionDecision(exclusions, persist: exclusions.Count > 0);

        if (!decision.IsExcluded)
        {
            _logger.LogInformation("{LoanIdentifier} is in the PAR Automated Review pilot", initialState.LoanNumber);
        }

        return decision;
    }

    private static bool IsPurchaseAgreementSupported(PurchaseAgreementData? purchaseAgreementData)
    {
        if (purchaseAgreementData is null)
        {
            return true;
        }

        return purchaseAgreementData is
        {
            SellerIsGovernmentEntityOrBank: { Value: Decision.No },
            MultipleParcelsAndLots: { Value: Decision.No },
            BuyerClosingInTrust: { Value: Decision.No },
            HasRepairs: { Value: Decision.No },
            HasTemporaryBuydown: { Value: Decision.No },
            IsShortSale: { Value: Decision.No },
            PurchasedThroughSheriffSale: { Value: Decision.No },
            HasCoOpMaintenance: { Value: Decision.No },
            HasAgeRestrictedCommunity: { Value: Decision.No },
            PropertyIsFarm: { Value: Decision.Yes or Decision.No },
            HasPersonalPropertyIncluded: { Value: Decision.No },
            HasMineralRights: { Value: Decision.No },
            Has1031ExchangeProvision: { Value: Decision.Yes or Decision.No },
            HasSolarPanels: { Value: Decision.Yes or Decision.No },
            HasListingAgentInformation: { Value: Decision.Yes or Decision.No },
            HasSellingAgentInformation: { Value: Decision.Yes or Decision.No },
            IsNewConstruction: { Value: Decision.Yes or Decision.No },
            HasSpecificDollarAmountOwedInTransferTaxes: { Value: Decision.No },
            HasTransferTaxesPaidOutsideOfStateCustom: { Value: Decision.No },
            HasRentCreditsOrRentToOwnClause: { Value: Decision.No },
            HasBuyerPayingPartOfHomeWarranty: { Value: Decision.No },
            HasRealtorCredits: { Value: Decision.No },
            HasStateDeedDocStampTaxPayer: { Value: Decision.No },
            GiftOfEquityAmount: null or { Value: 0 },
            MonthlyHOAAmount: null or { Value: 0 },
            OneTimeHOAAmount: null or { Value: 0 },
            BrokerComplianceTransactionFee: null or { Value: 0 },
            BuyerHomeWarrantyPaidAmount: null or { Value: 0 },
            RentCreditsAmount: null or { Value: 0 },
            ApplianceCreditsAmount: null or { Value: 0 },
            PrivateTransferFee: null or { Value: 0 }
        };
    }

    private static IEnumerable<string> ToAlphaNumericTokens(string str)
    {
        var matches = _alphaNumericRegex.Matches(str)
            .SelectMany(match => match.Captures)
            .Select(capture => capture.Value);
        return matches;
    }

    private static IEnumerable<GetDocumentMetadataResponse> GetDocumentsOfType(RocketLogicDocuments rocketLogicDocs, string? documentType)
    {
        if (string.IsNullOrEmpty(documentType))
        {
            return Enumerable.Empty<GetDocumentMetadataResponse>();
        }

        return rocketLogicDocs.GetDocumentsOfType(documentType);
    }

    private static string? ToStateCode(string? state)
    {
        if (state == null)
        {
            return null;
        }

        var sanitized = string.Join(" ", ToAlphaNumericTokens(state))?.ToUpper();
        if (sanitized == null)
        {
            return null;
        }
        return sanitized switch
        {
            "AL" or "ALABAMA" => "AL",
            "AK" or "ALASKA" => "AK",
            "AZ" or "ARIZONA" => "AZ",
            "AR" or "ARKANSAS" => "AR",
            "CA" or "CALIFORNIA" => "CA",
            "CO" or "COLORADO" => "CO",
            "CT" or "CONNECTICUT" => "CT",
            "DC" or "DISTRICT OF COLUMBIA" => "DC",
            "DE" or "DELAWARE" => "DE",
            "FL" or "FLORIDA" => "FL",
            "GA" or "GEORGIA" => "GA",
            "HI" or "HAWAII" => "HI",
            "ID" or "IDAHO" => "ID",
            "IL" or "ILLINOIS" => "IL",
            "IN" or "INDIANA" => "IN",
            "IA" or "IOWA" => "IA",
            "KS" or "KANSAS" => "KS",
            "KY" or "KENTUCKY" => "KY",
            "LA" or "LOUISIANA" => "LA",
            "ME" or "MAINE" => "ME",
            "MD" or "MARYLAND" => "MD",
            "MA" or "MASSACHUSETTS" => "MA",
            "MI" or "MICHIGAN" => "MI",
            "MN" or "MINNESOTA" => "MN",
            "MS" or "MISSISSIPPI" => "MS",
            "MO" or "MISSOURI" => "MO",
            "MT" or "MONTANA" => "MT",
            "NE" or "NEBRASKA" => "NE",
            "NV" or "NEVADA" => "NV",
            "NH" or "NEW HAMPSHIRE" => "NH",
            "NJ" or "NEW JERSEY" => "NJ",
            "NM" or "NEW MEXICO" => "NM",
            "NY" or "NEW YORK" => "NY",
            "NC" or "NORTH CAROLINA" => "NC",
            "ND" or "NORTH DAKOTA" => "ND",
            "OH" or "OHIO" => "OH",
            "OK" or "OKLAHOMA" => "OK",
            "OR" or "OREGON" => "OR",
            "PA" or "PENNSYLVANIA" => "PA",
            "RI" or "RHODE ISLAND" => "RI",
            "SC" or "SOUTH CAROLINA" => "SC",
            "SD" or "SOUTH DAKOTA" => "SD",
            "TN" or "TENNESSEE" => "TN",
            "TX" or "TEXAS" => "TX",
            "UT" or "UTAH" => "UT",
            "VT" or "VERMONT" => "VT",
            "VA" or "VIRGINIA" => "VA",
            "WA" or "WASHINGTON" => "WA",
            "WV" or "WEST VIRGINIA" => "WV",
            "WI" or "WISCONSIN" => "WI",
            "WY" or "WYOMING" => "WY",
            _ => null,
        };
    }
}
