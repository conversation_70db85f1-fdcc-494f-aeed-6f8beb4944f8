using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class StateSpecificQuestionPilotAccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.StateSpecificQuestions;

    private readonly IPilotCheck _pilotCheck;
    private readonly Type _exclusionDeciderType = typeof(StateSpecificQuestionPilotAccessDecider);

    public StateSpecificQuestionPilotAccessDecider(PilotCheckFactory pilotCheckFactory)
    {
        _pilotCheck = pilotCheckFactory(PilotCheckType.Leader, AccessDecisionId);
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var decision = new ExclusionDecision(persist: true);
        var accessDecision = await GetAccessDecision(container);

        if (!accessDecision)
        {
            decision.Add(ExclusionReason.NotInPilot, _exclusionDeciderType);
        }

        return decision;
    }

    private async Task<bool> GetAccessDecision(AccessDecisionContainer accessContainer)
    {
        var (accessDecision, _) = await _pilotCheck.IsInPilot(accessContainer);
        return accessDecision;
    }
}
