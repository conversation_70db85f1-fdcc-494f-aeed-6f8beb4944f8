using Amazon.DynamoDBv2;
using DecisionServices.Core.StateProvider;
using LoanAccessAuthorizer.Authorization.Rules.Features;
using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Exceptions;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;
using LoanAccessAuthorizer.RocketLogicApi.Models;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class RlXpDecider : IExclusionDecider
{
    private readonly IPilotCheck _pilotCheck;
    private readonly ILogger<RlXpDecider> _logger;
    public string AccessDecisionId => ApplicationId.RlXp;
    private const string _pqLockoutId = Rules.ApplicationId.Property;
    private const string _aqPilotId = Rules.ApplicationId.Asset;
    private const string _iqPilotId = Rules.ApplicationId.Income;
    private const string _cqPilotId = Rules.ApplicationId.Credit;
    private const string _piPilotId = Rules.ApplicationId.PersonalInformation;

    public RlXpDecider(ILogger<RlXpDecider> logger, PilotCheckFactory pilotCheckFactory)
    {
        _logger = logger;
        _pilotCheck = pilotCheckFactory(PilotCheckType.Leader, AccessDecisionId);
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var rlbLeadTask = container.StateContainer.Get<RocketLogicBankingLeadDetails>();
        var rlLoanDetailsTask = container.StateContainer.Get<RocketLogicLoanDetails>();

        var rlbLead = await rlbLeadTask;
        var rlLoanDetails = await rlLoanDetailsTask;

        var exclusions = new List<Exclusion>();

        var pilotDecision = await PilotCheckHandler(container);

        var features = pilotDecision.pilotResult.Features.ToHashSet();

        if (rlbLead?.IsUnsupportedLeadType ?? false)
        {
            exclusions.Add(new Exclusion
            {
                Reason = ExclusionReason.UnsupportedLeadType,
                Comment = "Lead type is unsupported in RLXP"
            });
        }

        _logger.LogInformation("Checking if loan is in AMP.");

        var isLoanInAmp = await container.StateContainer.Get<bool>(
            ExplicitStateProviderIds.IsLoanInAmp
        );
        if (isLoanInAmp)
        {
            var pqLockoutDecisionTask = container.GetAccessDecision(_pqLockoutId);
            var aqPilotDecisionTask = container.GetAccessDecision(_aqPilotId);
            var iqPilotDecisionTask = container.GetAccessDecision(_iqPilotId);
            var cqPilotDecisionTask = container.GetAccessDecision(_cqPilotId);
            var piPilotDecisionTask = container.GetAccessDecision(_piPilotId);

            var pqPilotDecision = await pqLockoutDecisionTask;
            var aqPilotDecision = await aqPilotDecisionTask;
            var iqPilotDecision = await iqPilotDecisionTask;
            var cqPilotDecision = await cqPilotDecisionTask;
            var piPilotDecision = await piPilotDecisionTask;

            var isInRequiredPilots = aqPilotDecision.AccessDecision &&
                iqPilotDecision.AccessDecision &&
                cqPilotDecision.AccessDecision &&
                pqPilotDecision.AccessDecision &&
                piPilotDecision.AccessDecision;

            if (!isInRequiredPilots)
                exclusions.Add(new Exclusion
                {
                    Reason = ExclusionReason.NotInPilot,
                    Comment = "Loan is not in required pilots"
                });

        }

        // Originated by is set to RLB for RLB, RLXP, and RMA loans
        var isOriginatedByRLB = isLoanInAmp
            ? await container.StateContainer.Get<bool>(
                ExplicitStateProviderIds.IsLoanOriginatedByRLB
            )
            : true;

        if (!isOriginatedByRLB)
        {
            exclusions.Add(new Exclusion
            {
                Reason = ExclusionReason.OriginatedByAmp,
                Comment = "Loan already exists in AMP and wasn't imported by RL"
            });
        }

        _logger.LogInformation("Checking if loan is in AMP archived.");
        var isLoanArchived = await container.StateContainer.Get<bool>(
            ExplicitStateProviderIds.IsLoanArchivedInAmp
        );
        if (isLoanArchived)
        {
            exclusions.Add(new Exclusion
            {
                Reason = ExclusionReason.InAmpArchive,
                Comment = "Loan exists in AMP Archive"
            });
        }

        if (exclusions.Count > 0 || rlLoanDetails.LoanPurpose != LoanPurpose.Purchase)
        {
            features = new HashSet<string>();
        }

        var exclusion = new ExclusionDecision(exclusions, features, pilotDecision.shouldPersist);

        return exclusion;
    }

    private async Task<(PilotCheckResult pilotResult, bool shouldPersist)> PilotCheckHandler(AccessDecisionContainer container)
    {
        PilotCheckResult pilotResult;

        try
        {
            pilotResult = await _pilotCheck.IsInPilot(container);
        }
        catch
        {
            return (new PilotCheckResult(true, false), false);
        }

        return (pilotResult, true);
    }
}
