using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.Product.Models;
using LoanAccessAuthorizer.IncomeQualifier.Models;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class RLBBackHalfDecider : LeaderPilotAccessDecider
{
    public override string AccessDecisionId => ApplicationId.RLBBackHalf;
    private readonly HashSet<IncomeType> supportedIncomeTypes = new HashSet<IncomeType>{ IncomeType.Passive, IncomeType.Employment };
    private readonly HashSet<IncomeSubType> supportedIncomeSubTypes = new HashSet<IncomeSubType>{ IncomeSubType.QuickCheck, IncomeSubType.Standard, IncomeSubType.Pension, IncomeSubType.SocialSecurity };

    public RLBBackHalfDecider(
        IApplicationAuthorizerDecisionStore decisionStore,
        PilotCheckFactory pilotCheckFactory,
        ILogger<RLBBackHalfDecider> logger
        ) : base(decisionStore, pilotCheckFactory, logger)
    {
    }

    protected override async Task<(bool isInPilot, bool shouldPersist)> DetermineAccessDecision(AccessDecisionContainer accessContainer)
    {
        var pilotDecisionTask = await base.DetermineAccessDecision(accessContainer);
        if (!pilotDecisionTask.isInPilot)
        {
            return pilotDecisionTask;
        }

        var isProductOnLoan = await accessContainer.StateContainer.Get<bool>(ExplicitStateProviderIds.IsProductOnLoan);

        if (!isProductOnLoan)
        {
            return (false, false);
        }

        var isInMODSPilot = await accessContainer.GetAccessDecision(ApplicationId.MODS);
        if (!isInMODSPilot)
        {
            return (false, false);
        }

        var productInfoTask = accessContainer.StateContainer.Get<ProductInfo>();
        var incomeSourcesTask = accessContainer.StateContainer.Get<IncomeSourcesWrapper>();
        var productInfo = await productInfoTask;
        var incomeSources = await incomeSourcesTask;


        return (
            productInfo.ProductSettings.FirstOrDefault()?.LoanClass == "Conventional" &&
            (incomeSources?.Items?.All(incomeSource =>
            {
                return supportedIncomeTypes.Contains(incomeSource.IncomeType) &&
                    supportedIncomeSubTypes.Contains(incomeSource.IncomeSubType);
            }) ?? true),
            false
        );
    }
}
