using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Common.AccessPopulations;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public abstract class BypassCaptivaPercentageBasedAccessDecider : IAccessDecider
{
    private readonly IApplicationAuthorizerDecisionStore _applicationAuthorizerDecisionStore;
    private readonly ILogger _logger;
    private readonly IAccessPopulation _accessPopulation;

    public abstract string AccessDecisionId { get; }

    public BypassCaptivaPercentageBasedAccessDecider(IApplicationAuthorizerDecisionStore applicationAuthorizerDecisionStore,
        ILogger logger, AccessPopulationFactory accessPopulationFactory)
    {
        _applicationAuthorizerDecisionStore = applicationAuthorizerDecisionStore;
        _logger = logger;
        _accessPopulation = accessPopulationFactory.GetAccessPopulation(AccessDecisionId);
    }

    public async Task<bool> GetAccessDecision(AccessDecisionContainer accessContainer)
    {
        var initialLoanData = await accessContainer.StateContainer.Get<InitialLoanState>();

        var storedDecision =
            await _applicationAuthorizerDecisionStore.GetDecision(initialLoanData.LoanNumber, AccessDecisionId);

        if (storedDecision == false)
        {
            return false;
        }

        var loanStatusesTask = accessContainer.StateContainer.Get<AmpLoanStatusCollection>();
        var loanDetailsTask = accessContainer.StateContainer.Get<AmpLoanDetails>();

        var loanDetails = await loanDetailsTask;
        if (loanDetails?.IsEmployeeLoan ?? false)
        {
            await StoreAndLogDecision(initialLoanData.LoanNumber, false);
            return false;
        }

        var loanStatuses = await loanStatusesTask;
        if (IsAboveLoanStatusThreshold(loanStatuses))
        {
            await StoreAndLogDecision(initialLoanData.LoanNumber, false);
            return false;
        }

        if (IsBelowLoanStatusThreshold(loanStatuses))
        {
            return true;
        }

        if (storedDecision.HasValue)
        {
            return true;
        }

        var hasAccess = await _accessPopulation.IsInPopulation();
        await StoreAndLogDecision(initialLoanData.LoanNumber, hasAccess);

        if (hasAccess)
        {
            _logger.LogInformation("{App} access population granted access to {loan}.", AccessDecisionId, initialLoanData.LoanNumber);
        }

        return hasAccess;
    }

    protected virtual bool IsAboveLoanStatusThreshold(AmpLoanStatusCollection loanStatuses) 
    {
        return loanStatuses.Any(status => status.LoanStatusId == $"{LoanStatus.FinalSignoff}");
    }

    protected virtual bool IsBelowLoanStatusThreshold(AmpLoanStatusCollection loanStatuses) 
    {
        return loanStatuses.All(status => status.LoanStatusId != $"{LoanStatus.FolderReceived}");
    }

    private async Task StoreAndLogDecision(string loanNumber, bool access)
    {
        await _applicationAuthorizerDecisionStore.StoreDecision(loanNumber, AccessDecisionId, access);
        _logger.LogInformation("{App} Pilot decision ({decision}) persisted", AccessDecisionId, access);
    }
}
