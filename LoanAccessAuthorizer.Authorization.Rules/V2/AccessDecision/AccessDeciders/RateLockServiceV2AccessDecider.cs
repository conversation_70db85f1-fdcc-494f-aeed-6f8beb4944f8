using System.Globalization;
using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.Extensions;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class RateLockServiceV2AccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.RateLockServiceV2;
    private readonly IPilotCheck _pilotCheck;
    private readonly Type _exclusionDeciderType = typeof(RateLockServiceV2AccessDecider);

    public RateLockServiceV2AccessDecider(PilotCheckFactory pilotCheckFactory)
    {
        _pilotCheck = pilotCheckFactory(PilotCheckType.Leader, AccessDecisionId);
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var exclusionDescision = new ExclusionDecision(true);

        var accessDecision = await GetAccessDecision(container);

        if (!accessDecision)
        {
            exclusionDescision.Add(ExclusionReason.NotInPilot, _exclusionDeciderType, "Loan is not part of this pilot.");
            return exclusionDescision;
        }

        var loanDetailsTask = container.StateContainer.Get<AmpLoanDetails>();
        var loanStatusesTask = container.StateContainer.Get<AmpLoanStatusCollection>();
        var isRocketLogicBankingTask = container.StateContainer.IsRocketLogicBankingLoan();
        var isInRCLockoutPilotTask = container.GetAccessDecision(ApplicationId.RCLockout);

        var loanDetails = await loanDetailsTask;
        if (loanDetails?.RateLockIndicator ?? false)
        {
            exclusionDescision.Add(ExclusionReason.RateLocked, _exclusionDeciderType, "Rate is locked.");
        }

        var loanStatuses = await loanStatusesTask;
        if (loanStatuses.Any(status => status.LoanStatusId == LoanStatus.FolderReceived.ToString(CultureInfo.InvariantCulture)))
        {
            exclusionDescision.Add(ExclusionReason.UnsupportedLoanStatus, _exclusionDeciderType, "Loan is at or past folder received.");
        }

        var isRocketLogicBanking = await isRocketLogicBankingTask;
        if (!isRocketLogicBanking.isRocketLogicBankingLoan)
        {
            exclusionDescision.Add(ExclusionReason.NotInPilot, _exclusionDeciderType, "Loan is not a RocketLogicBanking loan.");
        }

        var isInRCLockoutPilot = await isInRCLockoutPilotTask;
        if (!isInRCLockoutPilot)
        {
            exclusionDescision.Add(ExclusionReason.NotInPilot, _exclusionDeciderType, "Loan is not in RCLockout pilot.");
        }

        if (exclusionDescision.IsExcluded)
        {
            return exclusionDescision;
        }

        return new ExclusionDecision(false);
    }

    private async Task<bool> GetAccessDecision(AccessDecisionContainer accessContainer)
    {
        var (accessDecision, _) = await _pilotCheck.IsInPilot(accessContainer);
        return accessDecision;
    }
}
