using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class DocViewerAccessDecider : LeaderPilotAccessDecider
{
    public DocViewerAccessDecider(IApplicationAuthorizerDecisionStore decisionStore, PilotCheckFactory pilotCheckFactory, ILogger<DocViewerAccessDecider> logger)
        : base(decisionStore, pilotCheckFactory, logger)
    {
    }

    public override string AccessDecisionId => ApplicationId.DV;
}
