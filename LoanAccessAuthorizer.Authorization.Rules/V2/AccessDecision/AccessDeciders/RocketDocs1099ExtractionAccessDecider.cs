using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class RocketDocs1099ExtractionAccessDecider : LeaderPilotAccessDecider
{
    public override string AccessDecisionId => ApplicationId.RocketDocs1099;
    protected override bool ShouldPersist => true;

    public RocketDocs1099ExtractionAccessDecider(
        IApplicationAuthorizerDecisionStore applicationAuthorizerDecisionStore,
        PilotCheckFactory pilotCheckFactory,
        ILogger<RocketDocs1099ExtractionAccessDecider> logger) :
        base(applicationAuthorizerDecisionStore, pilotCheckFactory, logger)
    {
    }

    protected override async Task<(bool isInPilot, bool shouldPersist)> DetermineAccessDecision(
        AccessDecisionContainer container
    )
    {
        var (isInPilot, _) = await base.DetermineAccessDecision(container);

        if (!isInPilot)
        {
            return (isInPilot: false, shouldPersist: false);
        }

        return (isInPilot, shouldPersist: isInPilot);
    }
}
