using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Domain.Models;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class BankerOutOfAmpAccessDecider : IExclusionDecider
{
    private readonly IAmpAuthorizer _ampAuthorizer;

    public string AccessDecisionId => ApplicationId.BankerOutOfAmp;

    public BankerOutOfAmpAccessDecider(IAmpAuthorizer ampAuthorizer)
    {
        _ampAuthorizer = ampAuthorizer;
    }

    public async Task<ExclusionDecision> GetExclusionDecision(
        AccessDecisionContainer accessContainer
    )
    {
        var exclusion = new ExclusionDecision(false);

        var loanOfficerCommonId = await accessContainer.StateContainer.Get<UserId>(
            ExplicitStateProviderIds.LoanOfficerCommonId
        );
        if (!int.TryParse(loanOfficerCommonId.Id, out var numericLoanOfficerCommonId))
        {
            return exclusion;
        }

        var isInPilot = await _ampAuthorizer.IsInPilot(numericLoanOfficerCommonId, accessContainer);
        if (!isInPilot)
        {
            exclusion.Add(ExclusionReason.NotInPilot, GetType(), "User not in pilot");
        }

        return exclusion;
    }
}
