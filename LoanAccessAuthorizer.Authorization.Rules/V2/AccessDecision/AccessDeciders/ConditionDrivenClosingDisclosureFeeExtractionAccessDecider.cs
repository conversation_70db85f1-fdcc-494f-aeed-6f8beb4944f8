using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Common.AccessPopulations;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class ConditionDrivenClosingDisclosureFeeExtractionAccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.ConditionDrivenClosingDisclosureFeeExtraction;

    private readonly IAccessPopulation _accessPopulation;

    private readonly ILogger<ConditionDrivenClosingDisclosureFeeExtractionAccessDecider> _logger;

    public ConditionDrivenClosingDisclosureFeeExtractionAccessDecider(AccessPopulationFactory accessPopulationFactory,
        ILogger<ConditionDrivenClosingDisclosureFeeExtractionAccessDecider> logger)
    {
        _accessPopulation = accessPopulationFactory.GetAccessPopulation(AccessDecisionId);
        _logger= logger;
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var exclusionDecision = new ExclusionDecision(persist: true);

        var getAmpKeyLoanInfoTask = container.StateContainer.Get<AmpKeyLoanInfo>();
        var isFocTitleCompanyTask = container.StateContainer.Get<bool>(ExplicitStateProviderIds.IsFocTitleCompany);
        var isVaLoanTask = IsVaLoan(container);
        var isRetailLoanTask = IsRetailLoan(container);

        var ampKeyLoanInfo = await getAmpKeyLoanInfoTask;

        var deciderType = GetType();

        if (!IsRefinanceLoan(ampKeyLoanInfo))
        {
            exclusionDecision.Add(ExclusionReason.UnsupportedLoanPurpose, deciderType);
        }

        if (await isFocTitleCompanyTask)
        {
            exclusionDecision.Add(ExclusionReason.FOCTitleCompany, deciderType);
        }

        if (LoanIsForClosingSpecialist(ampKeyLoanInfo))
        {
            exclusionDecision.Add(ExclusionReason.UnsupportedLoanStatus, deciderType);
        }

        if (await isVaLoanTask)
        {
            exclusionDecision.Add(ExclusionReason.UnsupportedVaLoan, deciderType);
        }

        if (!await isRetailLoanTask)
        {
            exclusionDecision.Add(ExclusionReason.ThirdPartyOrigination, deciderType);
        }

        if (!exclusionDecision.IsExcluded && !await _accessPopulation.IsInPopulation())
        {
            exclusionDecision.Add(ExclusionReason.NotInPilot, deciderType);
        }

        return exclusionDecision;
    }

    private static bool IsRefinanceLoan(AmpKeyLoanInfo ampKeyLoanInfo)
    {
        return string.Equals(ampKeyLoanInfo.LoanPurpose, "Refinance", StringComparison.CurrentCultureIgnoreCase);
    }

    private bool LoanIsForClosingSpecialist(AmpKeyLoanInfo ampKeyLoanInfo)
    {
        var loanStatus = ampKeyLoanInfo.CurrentLoanStatus;

        if (loanStatus != null)
        {
            return loanStatus > LoanStatus.FinalSignoffPendingAction;
        }

        _logger.LogWarning("Loan lacks loan status");
        return true;
    }

    private static async Task<bool> IsVaLoan(AccessDecisionContainer container)
    {
        var qualificationGroup = await container.StateContainer.Get<QualificationGroupSet>();
        return qualificationGroup.Contains("VA");
    }

    private static async Task<bool> IsRetailLoan(AccessDecisionContainer container)
    {
        var isThirdPartyOriginationTask = IsThirdPartyOrigin(container);
        var isFranchise = await container.StateContainer.Get<bool>(ExplicitStateProviderIds.IsFranchiseLoan);

        return !await isThirdPartyOriginationTask && !isFranchise;
    }

    private static async Task<bool> IsThirdPartyOrigin(AccessDecisionContainer container)
    {
        var isTpoLoanTask = container.StateContainer.Get<bool?>(ExplicitStateProviderIds.IsTpoLoan);
        var thirdPartyOrigination = await container.StateContainer.Get<ThirdPartyOrigination>();

        return thirdPartyOrigination?.IsCorrespondentLoan == true || await isTpoLoanTask == true;
    }
}
