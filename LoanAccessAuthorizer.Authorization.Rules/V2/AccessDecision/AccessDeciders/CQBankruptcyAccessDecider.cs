using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class CQBankruptcyAccessDecider : LeaderPilotAccessDecider
{
    public override string AccessDecisionId => ApplicationId.Bankruptcy;

    public CQBankruptcyAccessDecider(
        IApplicationAuthorizerDecisionStore decisionStore,
        PilotCheckFactory pilotCheckFactory,
        ILogger<CQBankruptcyAccessDecider> logger)
        : base(decisionStore, pilotCheckFactory, logger)
    { }

    protected override async Task<(bool isInPilot, bool shouldPersist)> DetermineAccessDecision(
       AccessDecisionContainer accessContainer)
    {
        return await base.DetermineAccessDecision(accessContainer);
    }
}
