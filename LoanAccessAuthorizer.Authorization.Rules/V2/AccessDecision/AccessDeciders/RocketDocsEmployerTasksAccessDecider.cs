using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class RocketDocsEmployerTasksAccessDecider : LeaderPilotAccessDecider
{
    public override string AccessDecisionId => ApplicationId.RocketDocsEmployerTasks;
    public RocketDocsEmployerTasksAccessDecider(IApplicationAuthorizerDecisionStore applicationAuthorizerDecisionStore,
        PilotCheckFactory pilotCheckFactory, ILogger<RocketDocsEmployerTasksAccessDecider> logger) :
        base(applicationAuthorizerDecisionStore, pilotCheckFactory, logger)
    {
    }

    protected override async Task<(bool isInPilot, bool shouldPersist)> DetermineAccessDecision(
        AccessDecisionContainer container)
    {
        var rocketDocsEmployerDecisionTask = base.DetermineAccessDecision(container);
        var tasksDragAndDropDisabledDecisionTask = container.GetAccessDecision(ApplicationId.TaskDndLockout); 
        
        await Task.WhenAll(tasksDragAndDropDisabledDecisionTask, rocketDocsEmployerDecisionTask);

        var (isInPilot, shouldPersist) = rocketDocsEmployerDecisionTask.Result;
        var isTaskDragAndDropDisabled = tasksDragAndDropDisabledDecisionTask.Result;

        return (isInPilot || isTaskDragAndDropDisabled, shouldPersist);
    }
}
