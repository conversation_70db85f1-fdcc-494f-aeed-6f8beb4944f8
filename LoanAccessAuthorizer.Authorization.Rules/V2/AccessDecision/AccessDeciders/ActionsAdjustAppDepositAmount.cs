using System.ComponentModel;
using AmpJobRunner.Models.Enums;
using AmpJobRunner.Models.Response;
using DecisionServices.Core.StateProvider;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;
using LoanAccessAuthorizer.RLTasks;
using LoanAccessAuthorizer.RLTasks.Types;
using RLTasks.Model.Shared.Tasks;
using RLTasks.Model.Shared.Tasks.Output;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class ActionsAdjustAppDepositAmount : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.ActionsAdjustAppDepositAmount;
    private const int DuplicateCardInProcessStatusCode = 41;
    public readonly IRLTasksClient _rlTasksClient;

    public ActionsAdjustAppDepositAmount(IRLTasksClient rlTasksClient)
    {
        _rlTasksClient = rlTasksClient;
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var ampJobsTask = container.StateContainer.Get<AmpJobsCollection>();
        var hasStatus21Task = HasStatus21(container);
        var isAppDepositShortFailedTask = GetAppDepositShortFailedResult(container.StateContainer);
        var rlbLeadTask = container.StateContainer.Get<RocketLogicBankingLeadDetails>();
        var isOriginatedByRlbTask = IsOriginatedByRlb(container.StateContainer);

        var exclusions = new List<Exclusion>();

        var ampJobs = await ampJobsTask;
        var duplicateCardDetected = DuplicateCardCheck(ampJobs);
        if (duplicateCardDetected)
        {
            exclusions.Add(new Exclusion
            {
                Reason = ExclusionReason.SameCreditCardForDifferentClient,
                Comment = "There is another loan in process with the same credit card info for different client.",
                ExcludedAt = DateTime.Now
            });
        }

        var hasStatus21 = await hasStatus21Task;
        if (hasStatus21)
        {
            exclusions.Add(new Exclusion
            {
                Reason = ExclusionReason.UnsupportedLoanStatus,
                Comment = "Loan has been Folder Received. Application Deposit must be worked in AMP",
                ExcludedAt = DateTime.Now
            });
        }

        var isAppDepositShortFailed = await isAppDepositShortFailedTask;
        if (isAppDepositShortFailed)
        {
            exclusions.Add(new Exclusion
            {
                Reason = ExclusionReason.DepositLowerDenied,
                Comment = "App deposit lowering is denied",
                ExcludedAt = DateTime.Now
            });
        }

        var leadDetails = await rlbLeadTask;
        if (leadDetails?.IsUnsupportedLeadType ?? false)
        {
            exclusions.Add(new Exclusion
            {
                Reason = ExclusionReason.UnsupportedLeadType,
                Comment = "Unsupported lead type code",
                ExcludedAt = DateTime.Now
            });
        }

        var isOriginatedByRlb = await isOriginatedByRlbTask;
        if (isOriginatedByRlb == false)
        {
            exclusions.Add(new Exclusion
            {
                Reason = ExclusionReason.InAmpButNotImportedByRlb,
                Comment = "The loan is in AMP but not imported by RLB",
                ExcludedAt = DateTime.Now
            });
        }

        return new ExclusionDecision(exclusions, exclusions.Count > 0);
    }

    private static bool DuplicateCardCheck(IEnumerable<JobDetails> jobs)
    {
        return jobs.Where(job => job.JobType == JobType.SubmitAppDeposit)
            .Cast<AppDepositEntity>()
            .Any(appDepo => appDepo.StatusCode == DuplicateCardInProcessStatusCode);
    }

    private async Task<bool> GetAppDepositShortFailedResult(StateContainer container)
    {
        var initialState = await container.Get<InitialLoanState>();

        var appDepositShortTasks = await _rlTasksClient.GetTasks(
            initialState.LoanNumber,
            AppDepositType.ShortAppDepositApproval.ToString(),
            RocketLogicTaskStatus.Completed);

        if (appDepositShortTasks is null)
        {
            return false;
        }

        return appDepositShortTasks.Any(task =>
        {
            return task.CompletedTaskInformation?.Status is CompletedStatus.Failed;
        });
    }

    private static async Task<bool> HasStatus21(AccessDecisionContainer accessDecisionContainer)
    {
        var ampLoanStatusCollection = await IsLoanInAmpUtil.ExecuteIfLoanInAmp(accessDecisionContainer.StateContainer,
            () => accessDecisionContainer.StateContainer.Get<AmpLoanStatusCollection>());

        return ampLoanStatusCollection?.Any(
            status => status.LoanStatusId == LoanStatus.FolderReceived.ToString()) ?? false;
    }

    private static async Task<bool?> IsOriginatedByRlb(StateContainer container)
    {
        var inAmp = await container.Get<bool>(ExplicitStateProviderIds.IsLoanInAmp);
        if (!inAmp)
        {
            return null;
        }

        return await container.Get<bool>(ExplicitStateProviderIds.IsLoanOriginatedByRLB);
    }
}
