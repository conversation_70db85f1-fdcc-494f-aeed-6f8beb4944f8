﻿using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Common.AccessPopulations;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class BypassCaptivaQdocAccessDecider : BypassCaptivaPercentageBasedAccessDecider
{
    public override string AccessDecisionId => ApplicationId.BypassCaptivaQdoc;

    public BypassCaptivaQdocAccessDecider(IApplicationAuthorizerDecisionStore applicationAuthorizerDecisionStore,
        ILogger<BypassCaptivaQdocAccessDecider> logger, AccessPopulationFactory accessPopulationFactory) : 
        base(applicationAuthorizerDecisionStore, logger, accessPopulationFactory)
    {
    }

    protected override bool IsAboveLoanStatusThreshold(AmpLoanStatusCollection loanStatuses) => false;
    protected override bool IsBelowLoanStatusThreshold(AmpLoanStatusCollection loanStatuses) => true;
}