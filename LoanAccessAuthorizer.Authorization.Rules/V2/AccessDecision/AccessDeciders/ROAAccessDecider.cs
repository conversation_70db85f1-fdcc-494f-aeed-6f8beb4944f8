using System.Globalization;
using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Exceptions;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.Extensions;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;
using LoanAccessAuthorizer.Domain.TeamMemberData;
using Microsoft.Extensions.Logging;
using LoanAccessAuthorizer.Exclusions.Models;
using LoanAccessAuthorizer.Domain.Models;
using LoanAccessAuthorizer.Common.Constants;
using System.Collections.Immutable;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class ROAAccessDecider : IExclusionDecider, IClearDecision
{
    public string AccessDecisionId => ApplicationId.RocketOutOfAMP;
    private readonly Type _exclusionDeciderType = typeof(ROAAccessDecider);
    private readonly IApplicationAuthorizerDecisionStore _decisionStore;
    private readonly IPilotCheck _pilotCheck;
    private readonly ILeaderHierarchyProvider _leaderHierarchyProvider;
    private readonly ILogger<ROAAccessDecider> _logger;
    private const string RelocationLoan = "Relocation loans are unsupported";
    private const string AssumptionLoan = "Assumption loans are unsupported";

    private static readonly HashSet<string> _defaultServiceLoanOfficerCommonIds = new()
    {
        CommonIdConstants.DefaultCommonId
    };

    private static readonly ImmutableHashSet<string> excludedChannels = ImmutableHashSet.Create(
        StringComparer.CurrentCultureIgnoreCase,
        new[] { "Schwab", "Cadillac" }
    );

    private static readonly HashSet<string> _unsupportedLoanStatuses = new()
    {
        LoanStatus.Closed.ToString(CultureInfo.InvariantCulture),
        LoanStatus.DraftHonoredWarehoused.ToString(CultureInfo.InvariantCulture),
        LoanStatus.FileReadyToShip.ToString(CultureInfo.InvariantCulture),
        LoanStatus.FundedByInvestor.ToString(CultureInfo.InvariantCulture),
        LoanStatus.LoanStatus135.ToString(CultureInfo.InvariantCulture),
        LoanStatus.ApplicationApprovedNotAccepted.ToString(CultureInfo.InvariantCulture),
    };

    public ROAAccessDecider(
        IApplicationAuthorizerDecisionStore applicationAuthorizerDecisionStore,
        PilotCheckFactory pilotCheckFactory,
        ILeaderHierarchyProvider leaderHierarchyProvider,
        ILogger<ROAAccessDecider> logger
    )
    {
        _decisionStore = applicationAuthorizerDecisionStore;
        _pilotCheck = pilotCheckFactory(PilotCheckType.Leader, ApplicationId.RocketOutOfAMP);
        _leaderHierarchyProvider = leaderHierarchyProvider;
        _logger = logger;
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        if (await GetLegacyDecision(container) == false)
        {
            return new ExclusionDecision(persist: true)
            {
                {ExclusionReason.NotInPilot,
                _exclusionDeciderType,
                "Migrating legacy pilot decision"}
            };
        }

        var loanOfficerCommonId = await container.StateContainer.Get<UserId>(ExplicitStateProviderIds.LoanOfficerCommonId);
        var pilotDecision = IsInPilotCheck(container);
        var loanStatusesCollection = container.StateContainer.Get<AmpLoanStatusCollection>();
        var isOriginatedByRlb = container.StateContainer.Get<bool>(
            ExplicitStateProviderIds.IsLoanOriginatedByRLB
        );
        var isThirdPartyOrigination = container.StateContainer.Get<ThirdPartyOrigination>();
        var leaderHierarchy = _leaderHierarchyProvider.GetLeaderHierarchy(loanOfficerCommonId);
        var isProductOnLoan = container.StateContainer.Get<bool>(
            ExplicitStateProviderIds.IsProductOnLoan
        );
        var hasValidLoanOfficerCommonId = loanOfficerCommonId != null &&
            loanOfficerCommonId.Type == UserIdType.CommonId &&
            !string.IsNullOrWhiteSpace(loanOfficerCommonId.Id) &&
            !_defaultServiceLoanOfficerCommonIds.Contains(loanOfficerCommonId.Id);
        var isTPO = (await isThirdPartyOrigination).IsRocketProTPOLoan ?? false;
        var ampKeyLoanInfoTask = container.StateContainer.Get<AmpKeyLoanInfo>();

        var baseDecision = new ExclusionDecision(persist: hasValidLoanOfficerCommonId || isTPO);

        var rlbLead = await container.StateContainer.Get<RocketLogicBankingLeadDetails>();

        if ((await pilotDecision) == false)
        {
            baseDecision.ExtendOrAdd(
                ExclusionReason.NotInPilot,
                _exclusionDeciderType,
                "This loan is not in the Rocket Findings pilot."
            );
            _logger.LogInformation("ROA: Loan not in pilot {CommonIdsChecked} {ShouldPersist}",
                (await leaderHierarchy).LeaderCommonIds.Prepend(loanOfficerCommonId.Id),
                baseDecision.ShouldPersist
            );
        }

        if (rlbLead?.IsRelocation ?? false)
        {
            baseDecision.ExtendOrAdd(
                ExclusionReason.RelocationLoan,
                _exclusionDeciderType,
                RelocationLoan
            );
        }

        if (rlbLead?.IsAssumption ?? false)
        {
            baseDecision.ExtendOrAdd(
                ExclusionReason.AssumptionLoan,
                _exclusionDeciderType,
                AssumptionLoan
            );
        }

        var hasUnsupportedLoanStatus = false;

        foreach (var status in await loanStatusesCollection)
        {
            hasUnsupportedLoanStatus |= _unsupportedLoanStatuses.Contains(status.LoanStatusId);
        }

        if (
            !await isOriginatedByRlb
            && (await isThirdPartyOrigination).IsRocketProTPOLoan == false
        )
        {
            baseDecision.ExtendOrAdd(
                ExclusionReason.NotInPilot,
                _exclusionDeciderType,
                "Rocket Findings only supports loans originated in Rocket Logic Banking or TPO."
            );
        }

        var ampKeyLoanInfo = await ampKeyLoanInfoTask;
        if (excludedChannels.Contains(ampKeyLoanInfo?.LoanChannel))
        {
            baseDecision.ExtendOrAdd(
                ExclusionReason.UnsupportedLoanChannel,
                _exclusionDeciderType,
                $"The loan channel '{ampKeyLoanInfo?.LoanChannel}' is not supported for Rocket Findings"
            );
        }

        if (hasUnsupportedLoanStatus)
        {
            baseDecision.ExtendOrAdd(
                ExclusionReason.UnsupportedLoanStatus,
                _exclusionDeciderType,
                "Loan has a status not supported by Rocket Findings."
            );
        }

        if (!await isProductOnLoan)
        {
            baseDecision.ExtendOrAdd(
                ExclusionReason.NoProductOnLoan,
                _exclusionDeciderType,
                "An active product selection is required to rocket this loan."
            );
        }

        if (baseDecision.Any())
        {
            return baseDecision;
        }

        // No reasons for exclusion. Don't kick from pilot or save
        return new ExclusionDecision(persist: false);
    }

    private async Task<bool?> GetLegacyDecision(AccessDecisionContainer accessContainer)
    {
        var initialLoanState = await accessContainer.StateContainer.Get<InitialLoanState>();

        var existingDecisionTask = _decisionStore.GetDecision(
            initialLoanState.LoanNumber,
            AccessDecisionId
        );

        return await existingDecisionTask;
    }

    public async Task<bool> CanClearDecision(AccessDecisionContainer accessContainer)
    {
        var exclusionInfoTask = accessContainer.StateContainer.Get<ExclusionInformation>();
        var applicationCachedTrueTask = accessContainer.StateContainer.ApplicationHasTrueCachedDecision(
                _decisionStore,
                AccessDecisionId
            );
        return (await exclusionInfoTask).Exclusions.Any(exclusion => exclusion.Reason == ExclusionReason.NoProductOnLoan) || await applicationCachedTrueTask;
    }

    private async Task<bool?> IsInPilotCheck(AccessDecisionContainer accessDecisionContainer)
    {
        bool isInPilot;
        try
        {
            (isInPilot, _) = await _pilotCheck.IsInPilot(accessDecisionContainer);
        }
        catch (LoanDoesNotExistInAmpException)
        {
            return true;
        }

        return isInPilot;
    }

}
