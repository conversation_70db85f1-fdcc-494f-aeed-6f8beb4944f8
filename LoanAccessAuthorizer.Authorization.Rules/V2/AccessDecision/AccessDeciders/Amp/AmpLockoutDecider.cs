#nullable enable
using DecisionServices.Core.StateProvider;
using LoanAccessAuthorizer.Authorization.Rules.Features;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;
using LoanAccessAuthorizer.RocketLogicApi.Models;
using LoanAccessAuthorizer.MODS.Models;
using Property.Domain.Entities.Properties;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Amp;

public class AmpLockoutDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.AmpLockout;
    private static readonly ISet<string> RateShieldProductCodes = new HashSet<string>()
    {
        "130LC",
        "130LH",
        "930LC",
        "930LH",
        "830LC",
        "830LH"
    };
    private static readonly HashSet<string> StateSpecificQuestionStates =
        new(StringComparer.InvariantCultureIgnoreCase) { "CO", "ME", "NJ", "RI", "WI" };

    private static readonly HashSet<string> StateSpecificQuestionPurchaseOnlyStates =
        new(StringComparer.InvariantCultureIgnoreCase) { "TN" };
    private static IFeatureEnabledConfigProvider? _featureEnabledConfigProvider;

    public AmpLockoutDecider(IFeatureEnabledConfigProvider featureEnabledConfigProvider)
    {
        _featureEnabledConfigProvider = featureEnabledConfigProvider;
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var results = await Task.WhenAll(
            GetFHASpecificExclusions(container.StateContainer),
            GetSubjectPropertyStateExclusions(container),
            GetNewConstructionExclusions(container.StateContainer),
            GetRateShieldExclusions(container.StateContainer)
        );

        var name = nameof(AmpLockoutDecider);
        var exclusions = results
            .SelectMany(
                result => result,
                (_, reason) =>
                    new Exclusion
                    {
                        Reason = reason,
                        Comment = $"Excluded by {name}",
                        ExcludedAt = DateTime.UtcNow
                    }
            )
            .ToList();

        return new ExclusionDecision(exclusions, persist: exclusions.Any());
    }

    private async Task<IEnumerable<ExclusionReason>> GetFHASpecificExclusions(
        StateContainer stateContainer
    )
    {
        if (!await IsInProductGroup(stateContainer, "FHA"))
        {
            return Enumerable.Empty<ExclusionReason>();
        }

        var exclusions = new List<ExclusionReason>();

        var uwInfo = await stateContainer.Get<AmpUnderwritingSystemInfo>();
        var manualDowngrade = uwInfo.ManualDowngrade ?? false;
        var referEligible = isReferEligible(uwInfo.AusFinding);
        if (manualDowngrade || referEligible)
        {
            exclusions.Add(ExclusionReason.GovernmentWithholdings);
        }

        return exclusions;
    }

    private bool isReferEligible(IEnumerable<AmpAusFinding> findings)
    {
        var current = findings?.FirstOrDefault(au => au.IsCurrentAUSEngine);
        return current?.AUSRecommendation?.Equals(
                "refer/eligible",
                StringComparison.InvariantCultureIgnoreCase
            ) ?? false;
    }

    private static async Task<IEnumerable<ExclusionReason>> GetSubjectPropertyStateExclusions(
        AccessDecisionContainer accessDecisionContainer
    )
    {
        var exclusions = new List<ExclusionReason>();
        var stateContainer = accessDecisionContainer.StateContainer;
        var subjectProperty = await stateContainer.Get<OpenAmpSubjectProperty>();
        var rlLoanDetails = await stateContainer.Get<RocketLogicLoanDetails>();

        if (subjectProperty is null)
        {
            return exclusions;
        }

        if (
            rlLoanDetails.LoanPurpose == LoanPurpose.Refinance
            && "TX".Equals(subjectProperty.State, StringComparison.InvariantCultureIgnoreCase)
        )
        {
            exclusions.Add(ExclusionReason.Texas50a6);
        }

        if (rlLoanDetails.LoanPurpose == LoanPurpose.Refinance &&
            "SC".Equals(subjectProperty.State, StringComparison.InvariantCultureIgnoreCase))
        {
            exclusions.Add(ExclusionReason.SouthCarolinaClosingAttorneyProcess);
        }

        var isInSsqPilot = await accessDecisionContainer.GetAccessDecision(
            ApplicationId.StateSpecificQuestions
        );

        if (isInSsqPilot)
        {
            return exclusions;
        }

        if (StateSpecificQuestionStates.Contains(subjectProperty.State))
        {
            exclusions.Add(ExclusionReason.StateSpecificQuestions);
        }

        if (
            rlLoanDetails.LoanPurpose == LoanPurpose.Purchase
            && StateSpecificQuestionPurchaseOnlyStates.Contains(subjectProperty.State)
        )
        {
            exclusions.Add(ExclusionReason.StateSpecificQuestions);
        }

        return exclusions;
    }

    private static async Task<IEnumerable<ExclusionReason>> GetNewConstructionExclusions(
        StateContainer stateContainer
    )
    {
        if (_featureEnabledConfigProvider.IsFeatureEnabled(FeatureName.AllowAllNewConstruction))
        {
            return Enumerable.Empty<ExclusionReason>();
        }

        var pqSubjectProperty = await stateContainer.Get<SubjectProperty>();

        if (pqSubjectProperty?.IsNewConstruction == true)
        {
            return new List<ExclusionReason>() { ExclusionReason.NewConstruction };
        }

        return Enumerable.Empty<ExclusionReason>();
    }

    private async Task<bool> IsInProductGroup(StateContainer stateContainer, string productGroup)
    {
        var originationData = await stateContainer.Get<ModsResponse>(); // using MODS data because it is not subject to caching
        var activeProduct = GetActiveProduct(originationData.IsActive);

        return activeProduct?.ParentEligibilityGroups.Contains(productGroup) ?? false;
    }

    private Product GetActiveProduct(IDictionary<string, MortgageOriginationData> originationData)
    {
        if (originationData is null)
        {
            return null;
        }

        var latestDataKey = originationData.Keys
            .Select(
                key =>
                    (
                        key,
                        timestamp: DateTime.TryParse(key, out var timestamp)
                            ? timestamp
                            : (DateTime?)null
                    )
            )
            .OrderByDescending(item => item.timestamp)
            .Select(item => item.key)
            .FirstOrDefault();
        return latestDataKey is null ? null : originationData[latestDataKey]?.Product;
    }

    private static async Task<IEnumerable<ExclusionReason>> GetRateShieldExclusions(
        StateContainer stateContainer
    )
    {
        var ampKeyLoanDetails = await stateContainer.Get<AmpKeyLoanInfo>();

        if (ampKeyLoanDetails?.ProductCode is null)
        {
            return Enumerable.Empty<ExclusionReason>();
        }

        return RateShieldProductCodes.Contains(ampKeyLoanDetails.ProductCode)
            ? new List<ExclusionReason>() { ExclusionReason.RateShieldProduct }
            : Enumerable.Empty<ExclusionReason>();
    }
}
