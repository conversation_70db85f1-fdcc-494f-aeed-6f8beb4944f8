using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using LoanAccessAuthorizer.Domain.Models;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class AppDepositFolderingAccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.AppDepositFoldering;

    private readonly IAppDepositFolderingAuthorizer _authorizer;

    public AppDepositFolderingAccessDecider(IAppDepositFolderingAuthorizer authorizer)
    {
        _authorizer = authorizer;
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var loanOfficerCommonId = await container.StateContainer.Get<UserId>(
            ExplicitStateProviderIds.LoanOfficerCommonId
        );

        if (!int.TryParse(loanOfficerCommonId.Id, out var numericLoanOfficerCommonId))
        {
            return new ExclusionDecision(false);
        }

        var (isInPilot, hasLoanData) = await _authorizer.IsInPilotWithLoanData(
            numericLoanOfficerCommonId,
            container
        );

        var exclusion = new ExclusionDecision(hasLoanData);
        if (!isInPilot)
        {
            exclusion.Add(ExclusionReason.NotInPilot, GetType(), "User not in pilot");
        }

        var lead = await container.StateContainer.Get<RocketLogicBankingLeadDetails>();
        if (lead?.IsUnsupportedLeadType ?? false)
        {
            return new ExclusionDecision(true)
            {
                new Exclusion
                {
                    Reason = ExclusionReason.UnsupportedLeadType,
                    Comment = "Lead Is Unsupported",
                    ExcludedAt = DateTime.Now
                }
            };
        }

        return exclusion;
    }
}
