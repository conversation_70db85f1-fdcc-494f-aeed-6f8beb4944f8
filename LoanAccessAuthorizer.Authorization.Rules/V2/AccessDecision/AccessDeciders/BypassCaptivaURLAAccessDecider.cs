﻿using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Common.AccessPopulations;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class BypassCaptivaURLAAccessDecider : BypassCaptivaPercentageBasedAccessDecider
{
    public override string AccessDecisionId => ApplicationId.BypassCaptivaURLA;

    public BypassCaptivaURLAAccessDecider(IApplicationAuthorizerDecisionStore applicationAuthorizerDecisionStore,
        ILogger<BypassCaptivaRMOAccessDecider> logger, AccessPopulationFactory accessPopulationFactory) : 
        base(applicationAuthorizerDecisionStore, logger, accessPopulationFactory)
    {
    }

    protected override bool IsAboveLoanStatusThreshold(AmpLoanStatusCollection loanStatuses)
    {
        return loanStatuses.Any(status => status.LoanStatusId == $"{LoanStatus.FolderReceived}");
    }

    protected override bool IsBelowLoanStatusThreshold(AmpLoanStatusCollection loanStatuses) => false;
}