using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class W2ConditionTrackingItemsAccessDecider : LeaderPilotAccessDecider
{
    protected override bool ShouldPersist => true;
    public override string AccessDecisionId => ApplicationId.W2ConditionTrackingItems;

    public W2ConditionTrackingItemsAccessDecider(
        IApplicationAuthorizerDecisionStore decisionStore,
        PilotCheckFactory pilotCheckFactory,
        ILogger<W2ConditionTrackingItemsAccessDecider> logger)
        : base(decisionStore, pilotCheckFactory, logger)
    { }
    
    protected override async Task<(bool isInPilot, bool shouldPersist)> DetermineAccessDecision(AccessDecisionContainer container)
    {
        var (isInPilot, shouldPersist) = await base.DetermineAccessDecision(container);
        
        if (!isInPilot)
        {
            return (false, shouldPersist);
        }
        
        var tpo = await container.StateContainer.Get<ThirdPartyOrigination>();
        return (!tpo?.IsRocketProTPOLoan ?? true, shouldPersist);
    }
}
