﻿using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class RocketDocsW2ExtractionAccessDecider : IAccessDecider
{
    private readonly IApplicationAuthorizerDecisionStore _applicationAuthorizerDecisionStore;
    private readonly ILogger _logger;

    public string AccessDecisionId => ApplicationId.RocketDocsW2;

    public RocketDocsW2ExtractionAccessDecider(IApplicationAuthorizerDecisionStore applicationAuthorizerDecisionStore,
        ILogger<RocketDocsW2ExtractionAccessDecider> logger)
    {
        _applicationAuthorizerDecisionStore = applicationAuthorizerDecisionStore;
        _logger = logger;
    }

    public async Task<bool> GetAccessDecision(AccessDecisionContainer accessContainer)
    {
        var initialLoanData = await accessContainer.StateContainer.Get<InitialLoanState>();

        var storedDecision =
            await _applicationAuthorizerDecisionStore.GetDecision(initialLoanData.LoanNumber, AccessDecisionId);

        if (storedDecision.HasValue)
        {
            return storedDecision.Value;
        }

        var tpo = await accessContainer.StateContainer.Get<ThirdPartyOrigination>();

        if (tpo?.IsRocketProTPOLoan == true)
        {
            return await StoreAndLogDecision(initialLoanData.LoanNumber, false);
        }

        var isInDocIngestionW2Pilot = await accessContainer.GetAccessDecision(ApplicationId.DocIngestionW2);

        return await StoreAndLogDecision(initialLoanData.LoanNumber, !isInDocIngestionW2Pilot);
    }

    private async Task<bool> StoreAndLogDecision(string loanNumber, bool access)
    {
        await _applicationAuthorizerDecisionStore.StoreDecision(loanNumber, AccessDecisionId, access);
        _logger.LogInformation("{App} Pilot decision ({decision}) persisted", AccessDecisionId, access);

        return access;
    }
}
