using System.Runtime.CompilerServices;
using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Common.AccessPopulations;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;

[assembly: InternalsVisibleTo("UnitTests.LoanAccessAuthorizer.Authorization.Rules")]

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class RlFeesAccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.RLFees;

    public static class FeeCategory
    {
        public const string Flood = "flood-fees";
        public const string LifeOfLoanTax = "life-of-loan-tax-fees";
        public const string Origination = "origination-fees";
    }

    internal const string RlFeesAccessPop = "RLFeesPopulation";
    internal const string RLFeesPrePilot = "RLFeesPrePilot";
    private readonly IPilotCheck _pilotCheck;
    private readonly IAccessPopulation _accessPopulationForRLFees;
    private readonly IAccessPopulation _accessPopulationForPrePilot;

    private readonly List<string> _categoriesRolledOutToAllLoans = [FeeCategory.Flood, FeeCategory.LifeOfLoanTax];
    private readonly Dictionary<string, IAccessPopulation> _categoryBandToAccessPopulation = [];
    private readonly List<Tuple<string, List<string>>> _categoryBandToCategories =
    [
        new("RLFeesCategoriesRollOutToOneInTwoLoans", []),
        new("RLFeesCategoriesRollOutToOneInTenLoans", []),
        new("RLFeesCategoriesRollOutToOneInOneHundredLoans", []),
        new("RLFeesCategoriesRollOutToOneInOneThousandLoans", [ FeeCategory.Origination ])
    ];

    public RlFeesAccessDecider(PilotCheckFactory pilotCheckFactory, AccessPopulationFactory accessPopulationFactory)
    {
        _pilotCheck = pilotCheckFactory(PilotCheckType.Normal, AccessDecisionId);
        _accessPopulationForRLFees = accessPopulationFactory.GetAccessPopulation(RlFeesAccessPop);
        _accessPopulationForPrePilot = accessPopulationFactory.GetAccessPopulation(RLFeesPrePilot);
        foreach (var (categoryBand, _) in _categoryBandToCategories)
        {
            _categoryBandToAccessPopulation.Add(categoryBand, accessPopulationFactory.GetAccessPopulation(categoryBand));
        }
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var pilotDataResult = await _pilotCheck.IsInPilot(container);
        if (!pilotDataResult.IsInPilot)
        {
            return new ExclusionDecision([new Exclusion
            {
                Reason = ExclusionReason.NotInPilot,
                ExcludedAt = DateTime.UtcNow
            }], persist: true);
        }

        var features = new List<string>();

        var rcLockoutAccessTask = container.GetAccessDecision(ApplicationId.RCLockout);
        var foeAccessTask = container.GetAccessDecision(ApplicationId.FeesOrchestratorEvaluation);
        var isInPopulation = await _accessPopulationForRLFees.IsInPopulation();
        var rcLockoutAccess = await rcLockoutAccessTask;
        var foeAccess = await foeAccessTask;
        var exclusions = new List<Exclusion>();

        if (!isInPopulation
            || !rcLockoutAccess.AccessDecision
            || !foeAccess.AccessDecision)
        {
            exclusions.Add(new Exclusion
            {
                Reason = ExclusionReason.NotInPilot,
                ExcludedAt = DateTime.UtcNow,
            });
        }
        else
        {
            features = await GetFeaturesForPilot(pilotDataResult.Features);
        }
        return new ExclusionDecision(exclusions, features, persist: true);
    }

    private async Task<List<string>> GetFeaturesForPilot(IEnumerable<string> pilotFeatures)
    {
        var featureCategories = new List<string>(_categoriesRolledOutToAllLoans);
        var features = pilotFeatures.ToHashSet();

        var isInRlFeesPrePilotFeaturePopulation = await _accessPopulationForPrePilot.IsInPopulation();

        foreach (var (categoryBand, categories) in _categoryBandToCategories)
        {
            if (await _categoryBandToAccessPopulation[categoryBand].IsInPopulation())
            {
                featureCategories.AddRange(categories);
            }
            else
            {
                break;
            }
        }

        if (!isInRlFeesPrePilotFeaturePopulation)
        {
            features.Remove(RLFeesPrePilot);
        }

        features.UnionWith(featureCategories);

        return [.. features];
    }
}
