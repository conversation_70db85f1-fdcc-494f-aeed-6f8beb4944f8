using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Common.Constants;
using LoanAccessAuthorizer.Domain.Models;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;
using LoanAccessAuthorizer.Domain.TeamMemberData;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class PreAppCreditReportUpgradeAccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.PreAppCreditReportUpgrade;
    private readonly IPilotCheck _pilotCheck;
    private readonly IRhidToCommonIdProvider _rhidToCommonIdProvider;

    public PreAppCreditReportUpgradeAccessDecider(
        PilotCheckFactory pilotCheckFactory,
        IRhidToCommonIdProvider rhidToCommonIdProvider
    )
    {
        _pilotCheck = pilotCheckFactory(PilotCheckType.Leader, AccessDecisionId);
        _rhidToCommonIdProvider = rhidToCommonIdProvider;
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var loanOfficerId = await container.StateContainer.Get<UserId>(
            ExplicitStateProviderIds.LoanOfficerCommonId
        );
        var commonId = await GetCommonId(loanOfficerId);

        var exclusionDecision = new ExclusionDecision(true);

        if (string.IsNullOrWhiteSpace(commonId.Id))
        {
            var isTpoLoan = await container.StateContainer.Get<bool>(ExplicitStateProviderIds.IsTpoLoan);
            if (isTpoLoan)
            {
                return exclusionDecision;
            }

            return new ExclusionDecision(false) { { ExclusionReason.NotInPilot, GetType(), "Loan has no common id" } };
        }

        if (string.Equals(commonId.Id, CommonIdConstants.DefaultCommonId))
        {
            return new ExclusionDecision(false) { { ExclusionReason.NotInPilot, GetType() } };
        }

        var isSchwabLoanTask = container.StateContainer.Get<bool>(
            ExplicitStateProviderIds.IsSchwabLoan
        );

        var isSchwabLoan = await isSchwabLoanTask;


        if (isSchwabLoan)
        {
            exclusionDecision.Add(ExclusionReason.NotInPilot, GetType(), "Loan is Schwab");
            return exclusionDecision;
        }

        var (isInPilot, _) = await _pilotCheck.IsInPilot(container);
        if (!isInPilot)
        {
            exclusionDecision.Add(ExclusionReason.NotInPilot, GetType());
        }

        return exclusionDecision;
    }

    private async Task<UserId> GetCommonId(UserId loanOfficerId)
    {
        if (
            !string.IsNullOrWhiteSpace(loanOfficerId.Id)
            && loanOfficerId.Type == UserIdType.RockHumanId
        )
        {
            return UserId.FromCommonId(
                await _rhidToCommonIdProvider.GetCommonIdFromRhid(loanOfficerId.Id)
            );
        }

        return loanOfficerId;
    }
}
