using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.Extensions;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class PurchaseNoAddressAccessDecider : IExclusionDecider
{
    private readonly IPilotCheck _pilotCheck;
    public string AccessDecisionId => ApplicationId.PurchaseNoAddress;

    public PurchaseNoAddressAccessDecider(PilotCheckFactory pilotCheckFactory)
    {
        _pilotCheck = pilotCheckFactory(PilotCheckType.Normal, AccessDecisionId);
    }
    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer accessContainer)
    {
        var isInPilotTask = _pilotCheck.IsInPilot(accessContainer);
        var isRlbLoanTask = StateContainerExtensions.IsRocketLogicBankingLoan(accessContainer.StateContainer);
        var (isInPilot, hasLoanData) = await isInPilotTask;
        var (isRlbLoan, _) = await isRlbLoanTask;

        var exclusion = new ExclusionDecision(hasLoanData);

        if (!isInPilot || !isRlbLoan)
        {
            exclusion.Add(ExclusionReason.NotInPilot, GetType());
        }

        return exclusion;
    }
}
