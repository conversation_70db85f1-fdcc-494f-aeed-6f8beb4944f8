using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Common.AccessPopulations;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class RocketDocsPostCloseAccessDecider : IAccessDecider
{
    public string AccessDecisionId => "RocketDocsPostClose";
    private readonly IApplicationAuthorizerDecisionStore _applicationAuthorizerDecisionStore;
    private readonly ILogger _logger;
    private readonly IAccessPopulation _accessPopulation;

    public RocketDocsPostCloseAccessDecider(IApplicationAuthorizerDecisionStore applicationAuthorizerDecisionStore,
        ILogger<RocketDocsPostCloseAccessDecider> logger, AccessPopulationFactory accessPopulationFactory)
    {
        _applicationAuthorizerDecisionStore = applicationAuthorizerDecisionStore;
        _logger = logger;
        _accessPopulation = accessPopulationFactory.GetAccessPopulation(AccessDecisionId);
    }

    public async Task<bool> GetAccessDecision(AccessDecisionContainer accessContainer)
    {
        var initialLoanData = await accessContainer.StateContainer.Get<InitialLoanState>();

        var storedDecision =
            await _applicationAuthorizerDecisionStore.GetDecision(initialLoanData.LoanNumber, AccessDecisionId);

        if (storedDecision == true)
        {
            return true;
        }

        var loanStatusesTask = accessContainer.StateContainer.Get<AmpLoanStatusCollection>();
        var loanDetailsTask = accessContainer.StateContainer.Get<AmpLoanDetails>();

        var loanDetails = await loanDetailsTask;
        var isEmployeeLoan = loanDetails?.IsEmployeeLoan ?? false;
        if (isEmployeeLoan)
        {
            return false;
        }

        var loanStatuses = await loanStatusesTask;
        if (!HasClosedLoanStatus(loanStatuses))
        {
            return false;
        }

        var hasAccess = await _accessPopulation.IsInPopulation();
        await StoreAndLogDecision(initialLoanData.LoanNumber, hasAccess);

        if (hasAccess)
        {
            _logger.LogInformation("{App} access population granted access to {loan}.", AccessDecisionId, initialLoanData.LoanNumber);
        }

        return hasAccess;
    }

    private static bool HasClosedLoanStatus(AmpLoanStatusCollection loanStatuses)
    {
        return loanStatuses.Any(status => status.LoanStatusId == $"{LoanStatus.Closed}");
    }

    private async Task StoreAndLogDecision(string loanNumber, bool access)
    {
        await _applicationAuthorizerDecisionStore.StoreDecision(loanNumber, AccessDecisionId, access);
        _logger.LogInformation("{App} Pilot decision ({decision}) persisted", AccessDecisionId, access);
    }
}
