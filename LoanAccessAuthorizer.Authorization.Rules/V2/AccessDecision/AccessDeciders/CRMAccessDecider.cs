using LoanAccessAuthorizer.Authorization.Rules.Features;
using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.Extensions;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class CRMAccessDecider : LeaderPilotAccessDecider, IClearDecision
{
    public override string AccessDecisionId => ApplicationId.CRM;
    private readonly IFeatureEnabledConfigProvider _featureEnabledConfigProvider;

    public CRMAccessDecider(
        IFeatureEnabledConfigProvider featureEnabledConfigProvider,
        IApplicationAuthorizerDecisionStore decisionStore,
        PilotCheckFactory pilotCheckFactory,
        ILogger<CRMAccessDecider> logger
    )
        : base(decisionStore, pilotCheckFactory, logger)
    {
        _featureEnabledConfigProvider = featureEnabledConfigProvider;
    }

    public Task<bool> CanClearDecision(AccessDecisionContainer accessContainer) =>
        accessContainer.StateContainer.ApplicationHasTrueCachedDecision(
            _decisionStore,
            AccessDecisionId
        );

    protected override async Task<(bool isInPilot, bool shouldPersist)> DetermineAccessDecision(
        AccessDecisionContainer accessContainer
    )
    {
        var allowTpo = _featureEnabledConfigProvider.IsFeatureEnabled(FeatureName.CrmTpoLoansAllowed);
        if (!allowTpo)
        {
            var tpo = await accessContainer.StateContainer.Get<ThirdPartyOrigination>();

            var shouldExcludeTpoLoan =
                (tpo.IsRocketProTPOLoan ?? true) || (tpo.IsCorrespondentLoan ?? true);
            if (shouldExcludeTpoLoan)
            {
                return (false, true);
            }
        }

        var rlApiTask = accessContainer.StateContainer.Get<RocketLogicLoanDetails>();
        var rlbTask = IsRlbLoan(accessContainer);
        await Task.WhenAll(rlApiTask, rlbTask);

        var rlApiLoan = rlApiTask.Result;
        var isRlbLoan = rlbTask.Result;

        if (rlApiLoan?.IsRocketLogicApiLoan ?? false)
        {
            return (true, true);
        }

        if (!isRlbLoan)
        {
            return (false, true);
        }

        var (isInPilot, _) = await base.DetermineAccessDecision(accessContainer);
        return (isInPilot, true);
    }

    private static async Task<bool> IsRlbLoan(AccessDecisionContainer accessContainer)
    {
        return (
            await accessContainer.StateContainer.IsRocketLogicBankingLoan()
        ).isRocketLogicBankingLoan;
    }
}
