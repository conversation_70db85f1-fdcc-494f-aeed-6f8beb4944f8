using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class RlbAssistantDecider : IExclusionDecider
{
    private readonly IPilotCheck _pilotCheck;

    public string AccessDecisionId => ApplicationId.RlbAssistant;

    public RlbAssistantDecider(PilotCheckFactory pilotCheckFactory)
    {
        _pilotCheck = pilotCheckFactory(PilotCheckType.Leader, AccessDecisionId);
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var isInPilot = await _pilotCheck.IsCommonIdInPilot(container);

        var exclusion = new ExclusionDecision(true);

        if (!isInPilot)
        {
            exclusion.Add(ExclusionReason.NotInPilot, GetType(), "Assigned loan officer not in pilot group.");
        }

        return exclusion;
    }
}
