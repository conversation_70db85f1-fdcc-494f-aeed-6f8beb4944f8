﻿using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Common.AccessPopulations;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class BypassCaptivaTPOAccessDecider : BypassCaptivaPercentageBasedAccessDecider
{
    public override string AccessDecisionId => ApplicationId.BypassCaptivaTPO;

    public BypassCaptivaTPOAccessDecider(IApplicationAuthorizerDecisionStore applicationAuthorizerDecisionStore,
        ILogger<BypassCaptivaTPOAccessDecider> logger, AccessPopulationFactory accessPopulationFactory) : 
        base(applicationAuthorizerDecisionStore, logger, accessPopulationFactory)
    {
    }

    protected override bool IsBelowLoanStatusThreshold(AmpLoanStatusCollection loanStatuses) => false;
}