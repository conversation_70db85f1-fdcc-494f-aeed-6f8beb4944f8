using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class AssetConditionProviderAccessDecider : PilotAccessDecider
{
    protected override bool ShouldPersist => true;

    public AssetConditionProviderAccessDecider(
        IApplicationAuthorizerDecisionStore decisionStore,
        PilotCheckFactory pilotCheckFactory,
        ILogger<AssetConditionProviderAccessDecider> logger
    )
        : base(decisionStore, pilotCheckFactory, logger)
    {
    }

    public override string AccessDecisionId => ApplicationId.AssetConditionProvider;
}

