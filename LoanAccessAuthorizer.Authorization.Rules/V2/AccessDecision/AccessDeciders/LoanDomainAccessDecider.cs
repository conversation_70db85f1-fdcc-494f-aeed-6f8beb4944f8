﻿using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using Microsoft.Extensions.Logging;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class LoanDomainAccessDecider : IAccessDecider
{
    public string AccessDecisionId => ApplicationId.LoanDomain;
    
    private readonly IApplicationAuthorizerDecisionStore _applicationAuthorizerDecisionStore;
    private readonly ILogger _logger;

    public LoanDomainAccessDecider(
        IApplicationAuthorizerDecisionStore applicationAuthorizerDecisionStore, 
        ILogger<LoanDomainAccessDecider> logger)
    {
        _applicationAuthorizerDecisionStore = applicationAuthorizerDecisionStore;
        _logger = logger;
    }

    public async Task<bool> GetAccessDecision(AccessDecisionContainer accessContainer)
    {
        var initialLoanState = await accessContainer.StateContainer.Get<InitialLoanState>();

        var storedDecision =
            await _applicationAuthorizerDecisionStore.GetDecision(initialLoanState.LoanNumber, AccessDecisionId);

        if (storedDecision.HasValue)
        {
            return storedDecision.Value;
        }

        var isLoanInAmp = await accessContainer.StateContainer.Get<bool>(ExplicitStateProviderIds.IsLoanInAmp);

        if (!isLoanInAmp)
        {
            return true;
        }

        return await StoreAndLogDecision(initialLoanState.LoanNumber, false);
    }

    private async Task<bool> StoreAndLogDecision(string loanNumber, bool access)
    {
        await _applicationAuthorizerDecisionStore.StoreDecision(loanNumber, AccessDecisionId, access);
        _logger.LogInformation("{App} Decision ({decision}) persisted, AccessDecisionId, access");

        return access;
    }
}
