using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class AmpSlimMenuFlowDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.AmpSlimMenuFlow;

    private readonly IPilotCheck _pilotCheck;

    private static readonly string _folderReceived = LoanStatus.FolderReceived.ToString();

    public AmpSlimMenuFlowDecider(PilotCheckFactory pilotCheckFactory)
    {
        _pilotCheck = pilotCheckFactory(PilotCheckType.Leader, AccessDecisionId);
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var (isInPilot, hasLoanData) = await _pilotCheck.IsInPilot(container);
        if (!isInPilot)
        {
            return new ExclusionDecision(hasLoanData)
            {
                new()
                {
                    Reason = ExclusionReason.NotInPilot,
                    ExcludedAt = DateTime.Now
                }
            };
        }

        var leadDetails = await container.StateContainer.Get<RocketLogicBankingLeadDetails>();

        if (leadDetails?.IsUnsupportedLeadType ?? false)
        {
            return new ExclusionDecision(true)
            {
                new()
                {
                    Reason = ExclusionReason.UnsupportedLeadType,
                    Comment = "Unsupported lead type code",
                    ExcludedAt = DateTime.Now
                }
            };
        }

        var isLoanInAmp = await container.StateContainer.Get<bool>(ExplicitStateProviderIds.IsLoanInAmp);
        if (!isLoanInAmp)
        {
            return new ExclusionDecision(false);
        }

        var loanStatuses = await container.StateContainer.Get<AmpLoanStatusCollection>();
        if (loanStatuses.Any(loanStatus => loanStatus.LoanStatusId == _folderReceived))
        {
            return new ExclusionDecision(true)
            {
                new()
                {
                    Reason = ExclusionReason.UnsupportedLoanStatus,
                    Comment = "Amp Slim Menu is only active until Folder Received",
                    ExcludedAt = DateTime.Now
                }
            };
        }

        var isRlbLoan = await container.StateContainer.Get<bool>(ExplicitStateProviderIds.IsLoanOriginatedByRLB);
        if (!isRlbLoan)
        {
            return new ExclusionDecision(true)
            {
                new()
                {
                    Reason = ExclusionReason.InAmpButNotImportedByRlb,
                    Comment = "The loan is in AMP but not imported by RLB",
                    ExcludedAt = DateTime.Now
                }
            };
        }

        return new ExclusionDecision(false);
    }
}
