using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class AppraisalOrderMgmtAccessDecider : PilotAccessDecider
{
    public AppraisalOrderMgmtAccessDecider(IApplicationAuthorizerDecisionStore applicationAuthorizerDecisionStore,
        PilotCheckFactory pilotCheckFactory, ILogger<AppraisalOrderMgmtAccessDecider> logger) : base(
        applicationAuthorizerDecisionStore, pilotCheckFactory, logger)
    {
    }

    public override string AccessDecisionId => ApplicationId.AppraisalOrderManagement;
}
