using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class TaskManagerBlocksFolderReceivedAccessDecider : LeaderPilotAccessDecider
{
    public TaskManagerBlocksFolderReceivedAccessDecider(IApplicationAuthorizerDecisionStore decisionStore,
        PilotCheckFactory pilotCheckFactory, ILogger<TaskManagerBlocksFolderReceivedAccessDecider> logger)
        : base(decisionStore, pilotCheckFactory, logger)
    {
    }

    public override string AccessDecisionId => ApplicationId.TaskManagerBlocksFolderReceived;
}
