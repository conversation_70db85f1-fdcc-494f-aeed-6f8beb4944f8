using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using LoanAccessAuthorizer.IncomeQualifier.Models;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class TaskDndLockoutAccessDecider : LeaderPilotAccessDecider
{
    private readonly static ISet<EmploymentIncomeDocumentType> _supportedDocTypes = new HashSet<EmploymentIncomeDocumentType>
    {
        EmploymentIncomeDocumentType.PayStubAndW2,
        EmploymentIncomeDocumentType.ThirdPartyVerificationOfIncome,
    };

    public TaskDndLockoutAccessDecider(IApplicationAuthorizerDecisionStore decisionStore,
        PilotCheckFactory pilotCheckFactory,
        ILogger<TaskDndLockoutAccessDecider> logger)
        : base(decisionStore, pilotCheckFactory, logger)
    {
    }
    
    public override string AccessDecisionId => ApplicationId.TaskDndLockout;

    protected override async Task<(bool isInPilot, bool shouldPersist)> DetermineAccessDecision(AccessDecisionContainer container)
    {
        var accessDecision = await base.DetermineAccessDecision(container);

        if (!accessDecision.isInPilot)
        {
            return accessDecision;
        }

        var loanDetails = await container.StateContainer.Get<AmpLoanDetails>();

        if (loanDetails?.IsEmployeeLoan ?? false)
        {
            return (false, true);
        }

        var incomeSources = await container.StateContainer.Get<IncomeSourcesWrapper>();
        if (incomeSources.Items.All(ShouldInclude))
        {
            return (accessDecision.isInPilot, false);
        }

        return (false, true);
    }

    private static bool ShouldInclude(IncomeSource incomeSource)
    {
        if(incomeSource.IncomeType == IncomeType.Employment)
        {
            return incomeSource.IncomeSubType switch
            {
                // Union, Self Employment, Active Military, and Military Reservist income should be excluded from the pilot
                IncomeSubType.Union => false,
                IncomeSubType.SelfEmployment => false,
                IncomeSubType.ActiveMilitary => false,
                IncomeSubType.MilitaryReservist => false,
                // Only include supported standard income documentation types in the pilot.
                IncomeSubType.Standard when incomeSource.DocumentationType != null =>
                    _supportedDocTypes.Contains(incomeSource.DocumentationType.Value),
                _ => true
            };
        }

        return true;
    }
}
