using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class DocIngestionW2AccessDecider : LeaderPilotAccessDecider
{
    protected override bool ShouldPersist => true;
    public override string AccessDecisionId => ApplicationId.DocIngestionW2;

    public DocIngestionW2AccessDecider(
        IApplicationAuthorizerDecisionStore decisionStore,
        PilotCheckFactory pilotCheckFactory,
        ILogger<DocIngestionW2AccessDecider> logger)
        : base(decisionStore, pilotCheckFactory, logger)
    { }

    protected override Task<(bool isInPilot, bool shouldPersist)> DetermineAccessDecision(
        AccessDecisionContainer container
    ) => Task.FromResult((true, true));
}
