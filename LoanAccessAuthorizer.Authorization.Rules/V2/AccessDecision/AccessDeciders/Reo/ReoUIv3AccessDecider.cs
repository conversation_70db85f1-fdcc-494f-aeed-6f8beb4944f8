using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Reo;

public class ReoUIv3AccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.ReoUIv3;
    private readonly IPilotCheck _pilotCheck;
    private readonly Type _exclusionDeciderType = typeof(ReoUIv3AccessDecider);

    public ReoUIv3AccessDecider(PilotCheckFactory pilotCheckFactory)
    {
        _pilotCheck = pilotCheckFactory(PilotCheckType.Leader, AccessDecisionId);
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var pilotDecisionTask = _pilotCheck.IsInPilot(container);
        var reoDecisionTask = container.GetAccessDecision(ApplicationId.REO);
        var pilotDecision = await pilotDecisionTask;
        var reoDecision = await reoDecisionTask;
        if (!pilotDecision.IsInPilot || !reoDecision.AccessDecision)
        {
            var exclusionDescision = new ExclusionDecision(true);
            exclusionDescision.Add(ExclusionReason.NotInPilot, _exclusionDeciderType, "Loan is not part of this pilot.");
            return exclusionDescision;
        }

        return new ExclusionDecision(false, pilotDecision.Features);
    }
}
