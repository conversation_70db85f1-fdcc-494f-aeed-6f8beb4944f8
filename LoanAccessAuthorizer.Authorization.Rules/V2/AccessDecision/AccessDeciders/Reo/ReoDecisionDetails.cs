namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Reo;

internal class ReoDecisionDetails
{
    public bool IsRlbLoan { get; init; }
    public bool IsRLApiLoan { get; init; }
    public bool IsTpoRLApiLoan { get; set; }
    public bool IsRelocation { get; init; }
    public bool IsAssumption { get; init; }
    public bool IsAmpLoan { get; init; }
    public bool? IsTpoLoan { get; set; }
    public bool HasStatus21 { get; set; }
    public bool HasUnsupportedInvestmentSubjectProperty { get; set; }
    public bool IsRefinanceLoan { get; set; }
    public bool HasUnsupportedMultiUnitPrimarySubjectProperty { get; set; }
    public bool HasUnsupportedMultiUnitProperty { get; set; }
    public bool HasInvestmentSubjectProperty { get; set; }
    public bool HasMultiFamilySubjectProperty { get; set; }
    public bool IsStreamlineProduct { get; set; }
    public bool IsAmeripriseHeloc { get; set; }
    public bool IsInPilotCheck { get; set; }
    public bool Decision { get; set; }
}
