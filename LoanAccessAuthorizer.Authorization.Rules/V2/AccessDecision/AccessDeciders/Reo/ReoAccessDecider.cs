using System.Runtime.CompilerServices;
using DecisionServices.Core.StateProvider;
using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.Extensions;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Exceptions;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;
using LoanAccessAuthorizer.RocketLogicApi.Models;
using Microsoft.Extensions.Logging;
using Property.Domain.Entities.Enums;
using Property.Domain.Entities.Properties;
using OwnedProperty = Property.Domain.Entities.Properties.OwnedProperty;

[assembly: InternalsVisibleTo("UnitTests.LoanAccessAuthorizer.Authorization.Rules.V2")]
namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Reo;

public class ReoAccessDecider : IExclusionDecider, IClearDecision
{
    private readonly IApplicationAuthorizerDecisionStore _decisionStore;
    private readonly IPilotCheck _pilotCheck;
    private readonly ILogger _logger;

    internal const string ReoTpoPostRegistrationFeature = "ReoTpoPostRegistration";
    private const string RocketProTpoAppId = "206605";
    private const string PreviouslyRemovedFromPilotCondition = "Previously Removed From Pilot";
    private const string UnderwritingNotSupportedCondition =
        "Loan is Past Folder Received, Underwriting Not Supported";
    private const string HasInvestmentOccupancyTypeCondition =
        "Subject Property occupancy type is investment";
    private const string HasMultiUnitPrimarySubjectPropertyCondition =
        "Subject Property occupancy type is primary and property type is 2-4 Family";
    private const string HasMultiUnitPropertyCondition =
        "Property type is 2-4 Family, or Owned type is Multi Family or 5 Or More Units";
    private const string LoanNotInPilot = "Loan did not pass the pilot check";
    private const string LoanNotAvailable = "Loan is not available";
    private const string NonRlbOrTpoLoan =
        "Loan was not created in or bounced out of Rocket Logic Banking and is not a TPO loan supported for post registration";

    private const string RelocationLoan = "Relocation loans are unsupported";
    private const string AssumptionLoan = "Assumption loans are unsupported";
    private const string StreamlineProduct = "Loan has a streamline product";
    private const string StreamlineRcLockout = "Streamline loan removed from RCLockout";
    private const string AmeripriseLoan = "Heloc Ameriprise loans are unsupported";
    private static readonly ISet<string> _excludedQualificationGroups = new HashSet<string>(StringComparer.CurrentCultureIgnoreCase)
    {
        "FHACreditQualifyingStreamline",
        "FHANonCreditQualifyingStreamline",
        "FHAStreamline",
        "VACreditQualifyingIRRRL",
        "VAIRRRL",
        "VANonCreditQualifyingIRRRL"
    };
    public const string ReoSubjectPropertyInvestmentAllowed = "ReoSubjectPropertyInvestmentAllowed";
    public const string ReoFhaAndVaStreamlineAllowed = "ReoFhaAndVaStreamlineAllowed";

    private static readonly ISet<string> _allReoFeatures = new HashSet<string>(StringComparer.CurrentCultureIgnoreCase)
    {
        ReoSubjectPropertyInvestmentAllowed,
        ReoFhaAndVaStreamlineAllowed
    };

    public ReoAccessDecider(
        IApplicationAuthorizerDecisionStore decisionStore,
        PilotCheckFactory pilotCheckFactory,
        ILogger<ReoAccessDecider> logger
    )
    {
        _decisionStore = decisionStore;
        _pilotCheck = pilotCheckFactory(PilotCheckType.Leader, ApplicationId.REO);
        _logger = logger;
    }

    public string AccessDecisionId => ApplicationId.REO;

    public Task<bool> CanClearDecision(AccessDecisionContainer accessContainer) =>
        accessContainer.StateContainer.ApplicationHasTrueCachedDecision(
            _decisionStore,
            AccessDecisionId
        );

    public async Task<ExclusionDecision> GetExclusionDecision(
        AccessDecisionContainer accessContainer
    )
    {
        var existingDecision = await GetLegacyDecision(accessContainer);
        if (existingDecision == false)
        {
            return new ExclusionDecision(persist: true)
            {
                {
                    ExclusionReason.NotInPilot,
                    GetType(),
                    PreviouslyRemovedFromPilotCondition
                }
            };
        }

        var (decision, decisionDetails) = await CalculateDecision(accessContainer);
        _logger.LogInformation(
            "Decision determined: {AccessDecisionId} {@DecisionDetails}",
            AccessDecisionId,
            decisionDetails
        );

        return decision;
    }

    private async Task<bool?> GetLegacyDecision(AccessDecisionContainer accessContainer)
    {
        var initialLoanState = await accessContainer.StateContainer.Get<InitialLoanState>();

        return await _decisionStore.GetDecision(
            initialLoanState.LoanNumber,
            AccessDecisionId
        ); ;
    }

    private async Task<(ExclusionDecision, ReoDecisionDetails)> CalculateDecision(
        AccessDecisionContainer accessContainer)
    {
        var isRlbLoanTask = IsRlbLoan(accessContainer);
        var isAmpLoanTask = IsLoanInAmp(accessContainer.StateContainer);
        var rlApiLoanTask = accessContainer.StateContainer.Get<RocketLogicLoanDetails>();
        var rlbLeadDetailsTask = accessContainer.StateContainer.Get<RocketLogicBankingLeadDetails>();
        var isAmeripriseHelocTask = accessContainer.StateContainer.Get<bool>(ExplicitStateProviderIds.IsHelocAmeripriseLoan);

        var rlApiLoan = await rlApiLoanTask;
        var isRlApiLoan = rlApiLoan?.IsRocketLogicApiLoan ?? false;
        var isRlbLoan = await isRlbLoanTask;
        var isAmpLoan = await isAmpLoanTask;
        var rlbLead = await rlbLeadDetailsTask;
        var isAmeripriseHeloc = await isAmeripriseHelocTask;

        var decisionDetails = new ReoDecisionDetails
        {
            IsRLApiLoan = isRlApiLoan,
            IsTpoRLApiLoan = isRlApiLoan && rlApiLoan.CreatedByAppId == RocketProTpoAppId,
            IsRlbLoan = isRlbLoan,
            IsAmpLoan = isAmpLoan,
            IsRelocation = rlbLead?.IsRelocation ?? false,
            IsAssumption = rlbLead?.IsAssumption ?? false,
            IsAmeripriseHeloc = isAmeripriseHeloc
        };

        if (!decisionDetails.IsRLApiLoan && !decisionDetails.IsRlbLoan && !decisionDetails.IsAmpLoan)
        {
            var exclusion = new ExclusionDecision(persist: false);

            exclusion.Add(
                ExclusionReason.NotInPilot,
                GetType(),
                LoanNotAvailable
            );

            return (exclusion, decisionDetails);
        }

        var isInPilotCheck = await IsInPilotCheck(accessContainer);
        var decision = new ExclusionDecision(persist: isAmpLoan, isInPilotCheck.Features);


        var reoTpoPreRegDecision = await accessContainer.GetAccessDecision(ApplicationId.ReoTpoPreRegistration);

        if (reoTpoPreRegDecision.AccessDecision ||
            (isRlApiLoan && !isRlbLoan && !decisionDetails.IsTpoRLApiLoan))
        {
            decisionDetails.HasStatus21 = !reoTpoPreRegDecision.AccessDecision && await HasStatus21(accessContainer);
            if (decisionDetails.HasStatus21)
            {
                decision.Add(
                    ExclusionReason.UnderwritingNotSupported,
                    GetType(),
                    UnderwritingNotSupportedCondition
                );
            }

            decisionDetails.Decision = !decision.Any();
            return (decision, decisionDetails);
        }

        decisionDetails.IsInPilotCheck = isInPilotCheck.IsInPilot;
        if (!decisionDetails.IsInPilotCheck)
        {
            decision.Add(ExclusionReason.NotInPilot, GetType(), LoanNotInPilot);
        }

        if (!isRlbLoan)
        {
            var isSupportedTpoLoan = isInPilotCheck.Features.Contains(ReoTpoPostRegistrationFeature) &&
                (decisionDetails.IsTpoRLApiLoan || await IsTpoLoan(accessContainer));

            if (!isSupportedTpoLoan)
            {
                decision.Add(ExclusionReason.NotInPilot, GetType(), NonRlbOrTpoLoan);
                return (decision, decisionDetails);
            }
            decisionDetails.IsTpoLoan = isSupportedTpoLoan;
        }

        if (decisionDetails.IsRelocation)
        {
            decision.Add(ExclusionReason.RelocationLoan, GetType(), RelocationLoan);
            return (decision, decisionDetails);
        }
        if (decisionDetails.IsAmeripriseHeloc)
        {
            decision.Add(ExclusionReason.AmeripriseHelocLoan, GetType(), AmeripriseLoan);
            return (decision, decisionDetails);
        }

        if (decisionDetails.IsAssumption)
        {
            decision.Add(ExclusionReason.AssumptionLoan, GetType(), AssumptionLoan);
            return (decision, decisionDetails);
        }

        var subjectProperty = await accessContainer.StateContainer.Get<SubjectProperty>();
        decisionDetails.HasInvestmentSubjectProperty = subjectProperty?.OccupancyType == OccupancyType.INVESTMENT;
        decisionDetails.HasMultiFamilySubjectProperty = subjectProperty?.PropertyType == PropertyType.MULTI_FAMILY;
        decisionDetails.IsRefinanceLoan = rlApiLoan?.LoanPurpose == LoanPurpose.Refinance;

        decisionDetails.HasUnsupportedInvestmentSubjectProperty = HasUnsupportedInvestmentSubjectProperty(
            subjectProperty,
            rlApiLoan,
            isInPilotCheck);

        if (decisionDetails.HasUnsupportedInvestmentSubjectProperty)
        {
            decision.Add(
                ExclusionReason.InvestmentSubjectProperty,
                GetType(),
                HasInvestmentOccupancyTypeCondition
            );
        }

        decisionDetails.HasUnsupportedMultiUnitPrimarySubjectProperty = HasMultiUnitPrimarySubjectProperty(subjectProperty);

        if (decisionDetails.HasUnsupportedMultiUnitPrimarySubjectProperty)
        {
            decision.Add(
                ExclusionReason.UnsupportedMultiUnitPrimarySubjectProperty,
                GetType(),
                HasMultiUnitPrimarySubjectPropertyCondition);
        }

        decisionDetails.HasUnsupportedMultiUnitProperty = await HasUnsupportedMultiUnitProperty(subjectProperty, accessContainer);
        if (decisionDetails.HasUnsupportedMultiUnitProperty)
        {
            decision.Add(
                ExclusionReason.UnsupportedMultiUnitProperty,
                GetType(),
                HasMultiUnitPropertyCondition);
        }

        var isReoStreamlineAllowed = isInPilotCheck.Features.Contains(ReoFhaAndVaStreamlineAllowed);
        decisionDetails.IsStreamlineProduct = isAmpLoan ? await IsStreamlineProduct(accessContainer) : false;
        if (decisionDetails.IsStreamlineProduct && !isReoStreamlineAllowed)
        {
            decision.Add(
                ExclusionReason.StreamlineProduct,
                GetType(),
                StreamlineProduct
            );
        }

        if (decisionDetails.IsStreamlineProduct && isReoStreamlineAllowed && decisionDetails.IsTpoLoan != true)
        {
            var rcLockoutAccessDecision = await accessContainer.GetAccessDecision(ApplicationId.RCLockout);

            if (rcLockoutAccessDecision.AccessDecision == false)
            {
                decision.Add(
                    ExclusionReason.StreamlineProduct,
                    GetType(),
                    StreamlineRcLockout
                );
            }
        }

        decisionDetails.HasStatus21 = isAmpLoan ? await HasStatus21(accessContainer) : false;
        if (decisionDetails.HasStatus21)
        {
            decision.Add(
                ExclusionReason.UnderwritingNotSupported,
                GetType(),
                UnderwritingNotSupportedCondition
            );
        }

        decisionDetails.Decision = !decision.Any();
        if (!decision.Any())
        {
            return (new ExclusionDecision(persist: false, isInPilotCheck.Features), decisionDetails);
        }

        return (decision, decisionDetails);
    }

    private static Task<bool> IsLoanInAmp(StateContainer stateContainer) =>
        stateContainer.Get<bool>(ExplicitStateProviderIds.IsLoanInAmp);

    private static async Task<bool> IsRlbLoan(AccessDecisionContainer accessContainer)
    {
        return (
            await accessContainer.StateContainer.IsRocketLogicBankingLoan()
        ).isRocketLogicBankingLoan;
    }

    private static async Task<bool> HasStatus21(AccessDecisionContainer accessDecisionContainer)
    {
        try
        {
            var ampLoanStatusCollection =
                await accessDecisionContainer.StateContainer.Get<AmpLoanStatusCollection>();

            return ampLoanStatusCollection
                .Any(status => status.LoanStatusId == LoanStatus.FolderReceived.ToString());
        }
        catch (LoanDoesNotExistInAmpException)
        {
            return false;
        }
    }

    private bool HasMultiUnitPrimarySubjectProperty(
        SubjectProperty subjectProperty)
    {
        return subjectProperty?.OccupancyType == OccupancyType.PRIMARY &&
            subjectProperty?.PropertyType == PropertyType.MULTI_FAMILY;
    }

    private static async Task<bool> HasUnsupportedMultiUnitProperty(
        SubjectProperty subjectProperty,
        AccessDecisionContainer accessDecisionContainer)
    {
        if (subjectProperty?.PropertyType == PropertyType.MULTI_FAMILY)
        {
            return true;
        }

        var ownedProperties = await accessDecisionContainer.StateContainer.Get<IEnumerable<OwnedProperty>>();

        return ownedProperties.Any(property => property.OwnedType == OwnedType.MultiFamily ||
                property.OwnedType == OwnedType.FiveOrMore);
    }

    private bool HasUnsupportedInvestmentSubjectProperty(
        SubjectProperty subjectProperty,
        RocketLogicLoanDetails rlApiLoan,
        PilotCheckResult isInPilotCheck)
    {
        if (!isInPilotCheck.Features.Contains(ReoSubjectPropertyInvestmentAllowed))
        {
            return subjectProperty?.OccupancyType == OccupancyType.INVESTMENT;
        }

        var hasSupportedInvestmentSubjectProperty = rlApiLoan?.LoanPurpose == LoanPurpose.Refinance &&
            subjectProperty?.OccupancyType == OccupancyType.INVESTMENT &&
            subjectProperty?.PropertyType != PropertyType.MULTI_FAMILY;

        return subjectProperty?.OccupancyType == OccupancyType.INVESTMENT && !hasSupportedInvestmentSubjectProperty;
    }

    private async Task<bool> IsStreamlineProduct(AccessDecisionContainer accessDecisionContainer)
    {
        try
        {
            var qualificationGroup = await accessDecisionContainer.StateContainer.Get<QualificationGroupSet>();
            return _excludedQualificationGroups.Overlaps(qualificationGroup);
        }
        catch (LoanDoesNotExistInAmpException)
        {
            return false;
        }
    }

    private static async Task<bool> IsTpoLoan(AccessDecisionContainer accessDecisionContainer)
    {
        try
        {
            var thirdPartyOrigination =
                await accessDecisionContainer.StateContainer.Get<ThirdPartyOrigination>();
            return thirdPartyOrigination?.IsRocketProTPOLoan == true;
        }
        catch (LoanDoesNotExistInAmpException)
        {
            return false;
        }
    }

    private async Task<PilotCheckResult> IsInPilotCheck(AccessDecisionContainer accessDecisionContainer)
    {
        PilotCheckResult isInPilot;

        try
        {
            return await _pilotCheck.IsInPilot(accessDecisionContainer);
        }
        catch (LoanDoesNotExistInAmpException)
        {
            isInPilot = new PilotCheckResult(true, true, _allReoFeatures);
        }

        return isInPilot;
    }
}
