namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Reo;

internal class ReoTpoPreRegistrationDecisionDetails
{
    public bool IsRLApiLoan { get; init; }
    public bool? IsTpoRLApiLoan { get; init; }
    public bool IsAmpLoan { get; init; }
    public bool? IsTpoLoan { get; init; }
    public bool HasStatus21 { get; set; }
    public bool IsInPilotCheck { get; set; }
    public bool Decision { get; set; }
}
