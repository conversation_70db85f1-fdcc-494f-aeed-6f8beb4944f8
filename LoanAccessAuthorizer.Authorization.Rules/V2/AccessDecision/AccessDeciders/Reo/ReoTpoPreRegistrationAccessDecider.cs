using DecisionServices.Core.StateProvider;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.Extensions;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Exceptions;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Reo;

public class ReoTpoPreRegistrationAccessDecider : IExclusionDecider, IClearDecision
{
    public string AccessDecisionId => ApplicationId.ReoTpoPreRegistration;
    private readonly IApplicationAuthorizerDecisionStore _decisionStore;
    private readonly ILogger _logger;

    private const string RocketProTpoAppId = "206605";
    private const string LoanNotInPilot = "Loan is not part of this pilot.";
    private const string UnderwritingNotSupportedCondition =
        "Loan is Past Folder Received, Underwriting Not Supported";

    public ReoTpoPreRegistrationAccessDecider(
        IApplicationAuthorizerDecisionStore decisionStore,
        ILogger<ReoAccessDecider> logger
    )
    {
        _decisionStore = decisionStore;
        _logger = logger;
    }

    public Task<bool> CanClearDecision(AccessDecisionContainer accessContainer) =>
        accessContainer.StateContainer.ApplicationHasTrueCachedDecision(
            _decisionStore,
            AccessDecisionId
        );

    public async Task<ExclusionDecision> GetExclusionDecision(
        AccessDecisionContainer accessContainer
    )
    {
        var (decision, decisionDetails) = await CalculateDecision(accessContainer);
        _logger.LogInformation(
            "Decision determined: {AccessDecisionId} {@DecisionDetails}",
            AccessDecisionId,
            decisionDetails
        );

        return decision;
    }

    private async Task<(ExclusionDecision, ReoTpoPreRegistrationDecisionDetails)> CalculateDecision(
        AccessDecisionContainer accessContainer)
    {
        var isAmpLoanTask = IsLoanInAmp(accessContainer.StateContainer);
        var rlApiLoanTask = accessContainer.StateContainer.Get<RocketLogicLoanDetails>();

        var rlApiLoan = await rlApiLoanTask;
        var isRlApiLoan = rlApiLoan?.IsRocketLogicApiLoan ?? false;
        var isTpoLoan = isRlApiLoan ? rlApiLoan.CreatedByAppId == RocketProTpoAppId :
            await IsTpoLoan(accessContainer);
        var isAmpLoan = await isAmpLoanTask;

        var decisionDetails = new ReoTpoPreRegistrationDecisionDetails
        {
            IsRLApiLoan = isRlApiLoan,
            IsTpoRLApiLoan = isRlApiLoan && rlApiLoan.CreatedByAppId == RocketProTpoAppId,
            IsTpoLoan = isTpoLoan,
            IsAmpLoan = isAmpLoan,
        };

        var decision = new ExclusionDecision(persist: isAmpLoan);

        if (isTpoLoan)
        {
            decisionDetails.HasStatus21 = await HasStatus21(accessContainer);
            if (decisionDetails.HasStatus21)
            {
                decision.Add(
                    ExclusionReason.UnderwritingNotSupported,
                    GetType(),
                    UnderwritingNotSupportedCondition
                );
            }

            decisionDetails.Decision = !decision.Any();
            return (decision, decisionDetails);
        }

        decision.Add(ExclusionReason.NotInPilot, GetType(), LoanNotInPilot);

        return (decision, decisionDetails);
    }

    private static Task<bool> IsLoanInAmp(StateContainer stateContainer) =>
        stateContainer.Get<bool>(ExplicitStateProviderIds.IsLoanInAmp);

    private static async Task<bool> IsTpoLoan(AccessDecisionContainer accessDecisionContainer)
    {
        try
        {
            var thirdPartyOrigination =
                await accessDecisionContainer.StateContainer.Get<ThirdPartyOrigination>();
            return thirdPartyOrigination?.IsRocketProTPOLoan == true;
        }
        catch (LoanDoesNotExistInAmpException)
        {
            return false;
        }
    }

    private static async Task<bool> HasStatus21(AccessDecisionContainer accessDecisionContainer)
    {
        try
        {
            var ampLoanStatusCollection =
                await accessDecisionContainer.StateContainer.Get<AmpLoanStatusCollection>();

            return ampLoanStatusCollection
                .Any(status => status.LoanStatusId == LoanStatus.FolderReceived.ToString());
        }
        catch (LoanDoesNotExistInAmpException) {
            return false;
        }
    }
}
