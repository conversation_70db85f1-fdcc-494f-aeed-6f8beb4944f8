using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.LoanData.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Reo;

public class ReoImportAccessDecider : IAccessDecider, IClearDecision
{
    private readonly IApplicationAuthorizerDecisionStore _decisionStore;
    private readonly DateTime _pilotDate;

    private static readonly DateTime ProdStartDate = DateTime.Parse("2022-01-28T00:00:00");
    private static readonly DateTime BetaStartDate = DateTime.Parse("2022-01-01T00:00:00");
    private static readonly DateTime TestStartDate = DateTime.Parse("2020-01-01T00:00:00");

    public string AccessDecisionId => ApplicationId.ReoImport;

    public ReoImportAccessDecider(
        IApplicationAuthorizerDecisionStore decisionStore,
        EnvironmentInfo environmentInfo
    )
    {
        _decisionStore = decisionStore;
        _pilotDate = GetPilotDate(environmentInfo);
    }

    public async Task<bool> GetAccessDecision(AccessDecisionContainer accessContainer)
    {
        var initialLoanData = await accessContainer.StateContainer.Get<InitialLoanState>();

        var storedDecision = await _decisionStore.GetDecision(initialLoanData.LoanNumber, AccessDecisionId);

        if (storedDecision.HasValue)
        {
            return storedDecision.Value;
        }

        var decision = await DetermineAccessDecision(accessContainer);

        // NOTE: If the loan is pre amp we want to avoid saving this decision in case the loan bounces out of RLB
        // Currently there are no events to inform systems when a loan bounces out of RLB.
        var isLoanInAmp = await accessContainer.StateContainer.Get<bool>(ExplicitStateProviderIds.IsLoanInAmp);
        if (!isLoanInAmp)
        {
            return decision;
        }

        await _decisionStore.StoreDecision(initialLoanData.LoanNumber, AccessDecisionId, decision);
        return decision;
    }

    public async Task<bool> CanClearDecision(AccessDecisionContainer accessContainer)
    {
        var isInPilot = await GetAccessDecision(accessContainer);
        return isInPilot;
    }

    private async Task<bool> DetermineAccessDecision(AccessDecisionContainer accessContainer)
    {

        var applicationStartDate =
           await accessContainer.StateContainer.Get<DateTime?>(ExplicitStateProviderIds.ApplicationOrInitialContactDate);
        var isDatePrePilot = applicationStartDate < _pilotDate;
        if (isDatePrePilot)
        {
            return false;
        }

        var isLoanInAmp = await accessContainer.StateContainer.Get<bool>(ExplicitStateProviderIds.IsLoanInAmp);
        if (isLoanInAmp)
        {
            var isOriginatedByRLB = await accessContainer.StateContainer.Get<bool>(ExplicitStateProviderIds.IsLoanOriginatedByRLB);
            return isOriginatedByRLB;
        }

        var isRlbReadOnly = await accessContainer.StateContainer.Get<bool?>(ExplicitStateProviderIds.IsRlbReadOnly);
        return isRlbReadOnly is false;
    }

    private static DateTime GetPilotDate(EnvironmentInfo environment)
    {
        var environmentName = environment.EnvironmentName;
        return environmentName switch
        {
            "prod" => ProdStartDate,
            "beta" => BetaStartDate,
            "test" => TestStartDate,
            "development" => TestStartDate,
            _ => ProdStartDate
        };
    }
}
