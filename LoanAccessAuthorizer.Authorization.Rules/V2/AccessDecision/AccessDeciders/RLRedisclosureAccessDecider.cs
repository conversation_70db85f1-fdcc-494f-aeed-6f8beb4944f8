﻿using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class RLRedisclosureAccessDecider : LeaderPilotAccessDecider
{
    public override string AccessDecisionId => ApplicationId.RLRedisclosure;

    public RLRedisclosureAccessDecider(IApplicationAuthorizerDecisionStore decisionStore, PilotCheckFactory pilotCheckFactory,
        ILogger<RLRedisclosureAccessDecider> logger) : base(decisionStore, pilotCheckFactory, logger)
    {
    }

    protected override async Task<(bool isInPilot, bool shouldPersist)> DetermineAccessDecision(AccessDecisionContainer container)
    {
        return (true, true);
    }
}
