using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot.Utils;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class RLBSoftCreditAccessDecider : LeaderPilotAccessDecider
{
    public override string AccessDecisionId => ApplicationId.RLBSoftCredit;

    public RLBSoftCreditAccessDecider(IApplicationAuthorizerDecisionStore decisionStore, PilotCheckFactory pilotCheckFactory,
        ILogger<RLBSoftCreditAccessDecider> logger) : base(decisionStore, pilotCheckFactory, logger)
    {

    }

    protected override async Task<(bool isInPilot, bool shouldPersist)> DetermineAccessDecision(AccessDecisionContainer accessContainer)
    {
        return (true, true);
    }
}
