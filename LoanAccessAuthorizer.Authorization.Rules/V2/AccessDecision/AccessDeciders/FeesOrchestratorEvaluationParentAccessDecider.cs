using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Common.AccessPopulations;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;
using Property.Domain.Entities.Properties;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;
/// <summary>
/// Parent access decider for Fees Orchestrator Evaluation
/// Created to allow us to reset the features for FOE pilot without changing the FOE pilot decision
/// </summary>
public class FeesOrchestratorEvaluationParentAccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.FeesOrchestratorEvaluationParent;

    private readonly IPilotCheck _pilotCheck;
    private readonly IAccessPopulation _accessPopulation;

    public FeesOrchestratorEvaluationParentAccessDecider(PilotCheckFactory pilotCheckFactory, AccessPopulationFactory accessPopulationFactory)
    {
        _pilotCheck = pilotCheckFactory(PilotCheckType.Normal, AccessDecisionId);
        _accessPopulation = accessPopulationFactory.GetAccessPopulation(ApplicationId.FeesOrchestratorEvaluation);
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var exclusions = new List<Exclusion>();
        var pilotCheckResultTask = _pilotCheck.IsInPilot(container);
        var isInPopulationTask = _accessPopulation.IsInPopulation();
        var loanLocationTexasTask = LoanIsForTexas(container);
        var pilotCheckResult = await pilotCheckResultTask;
        var isInPopulation = await isInPopulationTask;
        var loanLocationTexas = await loanLocationTexasTask;
        //FOE Pilot decision 
        if (loanLocationTexas || !pilotCheckResult.IsInPilot || !isInPopulation)
        {
            exclusions.Add(new Exclusion
            {
                Reason = ExclusionReason.NotInPilot,
                ExcludedAt = DateTime.UtcNow,
            });
        }
        return new ExclusionDecision(exclusions, persist: true);
    }

    private static async Task<bool> LoanIsForTexas(AccessDecisionContainer container)
    {
        var subjectProperty = await container.StateContainer.Get<SubjectProperty>();
        return Property.Domain.Entities.Enums.State.TX.Equals(subjectProperty?.Address?.State);
    }
}
