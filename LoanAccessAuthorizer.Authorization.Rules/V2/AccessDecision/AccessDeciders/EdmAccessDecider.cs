using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.Models;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class EdmAccessDecider : PilotAccessDecider
{
    public override string AccessDecisionId => ApplicationId.EDM;
    private static readonly DateTime _testAllCompanyStartDateTime = DateTime.Parse("2020-11-12T00:00:00");
    private static readonly DateTime _betaAllCompanyStartDateTime = DateTime.Parse("2020-11-12T00:00:00");
    private static readonly DateTime _prodAllCompanyStartDateTime = DateTime.Parse("2020-11-19T05:00:00Z");
    private readonly DateTime _allCompanyAccessStart;

    public EdmAccessDecider(
        IApplicationAuthorizerDecisionStore applicationAuthorizerDecisionStore,
        PilotCheckFactory pilotCheckFactory,
        ILogger<EdmAccessDecider> logger,
        EnvironmentInfo environmentInfo
        ) : base(applicationAuthorizerDecisionStore, pilotCheckFactory, logger)
    {
        _allCompanyAccessStart = GetStartDate(environmentInfo);
    }

    protected override async Task<(bool isInPilot, bool shouldPersist)> DetermineAccessDecision(
        AccessDecisionContainer container)
    {
        var rlApiLoan = await container.StateContainer.Get<RocketLogicLoanDetails>();

        if (rlApiLoan?.IsRocketLogicApiLoan ?? false)
        {
            return (true, true);
        }

        var initialContactDate = await container.StateContainer.Get<DateTime?>(ExplicitStateProviderIds.ApplicationOrInitialContactDate);

        if (initialContactDate == null)
        {
            return (false, false);
        }

        if (initialContactDate >= _allCompanyAccessStart)
        {
            return (true, true);
        }

        var loanOfficerCommonId = await container.StateContainer.Get<UserId>(ExplicitStateProviderIds.LoanOfficerCommonId);

        var edmPilotDecision = PilotCheck.Pilots.Any(pilot =>
            initialContactDate >= pilot.date && pilot.group.ContainsCommonId(loanOfficerCommonId.Id)
        );
        var shouldPersist = loanOfficerCommonId != null;

        return (edmPilotDecision, shouldPersist);
    }

    private static DateTime GetStartDate(EnvironmentInfo environment)
    {
        return environment.EnvironmentName switch {
            "test" => _testAllCompanyStartDateTime,
            "beta" => _betaAllCompanyStartDateTime,
            "prod" => _prodAllCompanyStartDateTime,
            _ => _prodAllCompanyStartDateTime
        };
    }
}
