using LoanAccessAuthorizer.Common.AccessPopulations;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class RocketDocsWireInstructionsAccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.RocketDocsWireInstructions;

    private readonly IAccessPopulation _accessPopulation;

    private readonly ILogger<RocketDocsWireInstructionsAccessDecider> _logger;

    private static readonly HashSet<string> UnsupportedStates =
    [
        "NY", "NC", "GA", "DE", "IL", "MA"
    ];

    public RocketDocsWireInstructionsAccessDecider(AccessPopulationFactory accessPopulationFactory,
        ILogger<RocketDocsWireInstructionsAccessDecider> logger)
    {
        _accessPopulation = accessPopulationFactory.GetAccessPopulation(AccessDecisionId);
        _logger = logger;
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        if (!await _accessPopulation.IsInPopulation())
        {
            return new ExclusionDecision(persist: true)
            {
                new()
                {
                    Reason = ExclusionReason.NotInPilot,
                    ExcludedAt = DateTime.Now
                }
            };
        }

        var exclusionDecision = new ExclusionDecision(persist: true);

        var isNotSupportedStateTask = IsNotSupportedState(container);
        var isTitleCompanySelectedByLenderTask = IsTitleCompanySelectedByLender(container);

        var isNotSupportedState = await isNotSupportedStateTask;
        var isTitleCompanySelectedByLender = await isTitleCompanySelectedByLenderTask;

        var deciderType = GetType();

        if (isNotSupportedState)
        {
            exclusionDecision.Add(ExclusionReason.UnsupportedState, deciderType);
        }

        if (isTitleCompanySelectedByLender)
        {
            exclusionDecision.Add(ExclusionReason.FOCTitleCompany, deciderType);
        }

        _logger.LogInformation("Wire Instructions pilot {decision}", !exclusionDecision.IsExcluded);

        return exclusionDecision;
    }

    private static async Task<bool> IsTitleCompanySelectedByLender(AccessDecisionContainer container)
    {
        var keyLoanInfoTask = container.StateContainer.Get<AmpKeyLoanInfo>();
        var ampTitleCompanyTask = container.StateContainer.Get<AmpTitleCompany>();

        var keyLoanInfo = await keyLoanInfoTask;
        var ampTitleCompany = await ampTitleCompanyTask;

        if (string.Equals("Refinance", keyLoanInfo.LoanPurpose, StringComparison.CurrentCultureIgnoreCase))
        {
            return string.Equals(ampTitleCompany.ClientChosenTitle, "No", StringComparison.CurrentCultureIgnoreCase);
        }

        return ampTitleCompany.FOCTitle;
    }

    private static async Task<bool> IsNotSupportedState(AccessDecisionContainer container)
    {
        var ampAddress = await container.StateContainer.Get<OpenAmpSubjectProperty>();
        return UnsupportedStates.Contains(ampAddress.State);
    }
}
