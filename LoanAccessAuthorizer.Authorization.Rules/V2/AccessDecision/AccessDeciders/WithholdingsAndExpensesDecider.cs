using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class WithholdingsAndExpensesDecider : IExclusionDecider
{
    private const ExclusionReason DecisionReason = ExclusionReason.NotInPilot;

    private const string ParentApplicationId = ApplicationId.PersonalInformation;

    private static readonly Type DeciderType = typeof(WithholdingsAndExpensesDecider);

    private static readonly DateTime MinInclusivePilotDate =
        new DateTime(2023, 12, 21, 0, 0, 0, DateTimeKind.Unspecified);

    public string AccessDecisionId => ApplicationId.WithholdingsAndExpenses;

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var accessResult = await container.GetAccessDecision(ParentApplicationId);
        if (!accessResult.AccessDecision)
        {
            return new ExclusionDecision(persist: true)
            {
                { DecisionReason, DeciderType, $"Not in {ParentApplicationId} pilot" }
            };
        }

        var pilotDate = await container.StateContainer.Get<DateTime?>(
            ExplicitStateProviderIds.ApplicationOrInitialContactDate
        );

        if (pilotDate is null)
        {
            return new ExclusionDecision(persist: false)
            {
                { DecisionReason, DeciderType, $"ApplicationOrInitialContactDate was null" }
            };
        }

        if (pilotDate < MinInclusivePilotDate)
        {
            return new ExclusionDecision(persist: true)
            {
                { DecisionReason, DeciderType, $"ApplicationOrInitialContactDate too old" }
            };
        }

        return new ExclusionDecision(persist: true);
    }
}
