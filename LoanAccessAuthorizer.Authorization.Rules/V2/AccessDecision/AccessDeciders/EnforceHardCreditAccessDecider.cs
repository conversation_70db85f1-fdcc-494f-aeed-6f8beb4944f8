using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot.Utils;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Domain.Models;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class EnforceHardCreditAccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.EnforceHardCredit;
    private readonly ILogger<EnforceHardCreditAccessDecider> _logger;

    private readonly IPilotCheck _pilotCheck;

    public EnforceHardCreditAccessDecider(
        PilotCheckFactory pilotCheckFactory,
        ILogger<EnforceHardCreditAccessDecider> logger
    )
    {
        _pilotCheck = pilotCheckFactory(PilotCheckType.Leader, AccessDecisionId);
        _logger = logger;
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var loanOfficerCommonIdTask = container.StateContainer.Get<UserId>(
            ExplicitStateProviderIds.LoanOfficerCommonId
        );

        var pilotDeterminantDateTask = RLBUtil.GetLoanDateForPilotComparisonWithDefault(container, DateTime.UtcNow, _logger);

        await Task.WhenAll(loanOfficerCommonIdTask, pilotDeterminantDateTask);

        var (isInPilot, hasLoanData) = await _pilotCheck.IsInPilot(
            container,
            loanOfficerCommonIdTask.Result,
            pilotDeterminantDateTask.Result
        );

        var exclusion = new ExclusionDecision(hasLoanData);
        if (!isInPilot)
        {
            exclusion.Add(ExclusionReason.NotInPilot, GetType(), "Not in pilot");
        }

        return exclusion;
    }
}
