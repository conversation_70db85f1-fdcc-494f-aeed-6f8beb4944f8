using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Common.AccessPopulations;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;
using LoanAccessAuthorizer.Domain.Product.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class UnifiedDataLayerAssetsAccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.UnifiedDataLayerAssets;
    private const string UdlAssetsInvestmentAccounts = "UdlAssetsInvestmentAccounts";
    private const string UdlAdditionalAssets = "UdlAdditionalAssets";
    private const string UdlNonTraditionalAssets = "UdlNonTraditionalAssets";

    private static readonly string[] InvestmentAccountAssetFeatures = ["_401K", "_403B","_457Plan",
        "IndividualRetirementArrangement", "Annuity", "Keogh", "ThriftSavingsPlan", "SimplifiedEmployeePension",
        "_529CollegeSavingsPlan", "MutualFund", "LifeInsurance", "TrustAccount", "BrokerageAccount"];

    private static readonly string[] AdditionalAssetFeatures = ["BridgeLoan", "CertificateOfDeposit",
        "GiftOfCash", "GiftOfEquity", "Grant", "MoneyMarket", "PledgedAssetAccount",
        "ProceedsFromSaleOfNonRealEstateAsset", "ProceedsFromSecuredLoan", "_1031Exchange"];

    private static readonly string[] NonTraditionalAssets = ["CreditCardRewardPoints",
        "EstimatedNetProceedsFromSaleOfOwnedProperty"];

    private readonly IPilotCheck _pilotCheck;
    private readonly IAccessPopulation _accessPopulation;
    private readonly IAccessPopulation _udlAssetsInvestmentAccountsAccessPopulation;
    private readonly IAccessPopulation _udlAdditionalAssetsAccessPopulation;
    private readonly IAccessPopulation _udlNonTraditionalAssetsAccessPopulation;

    public UnifiedDataLayerAssetsAccessDecider(PilotCheckFactory pilotCheckFactory,
        AccessPopulationFactory accessPopulationFactory)
    {
        _pilotCheck = pilotCheckFactory(PilotCheckType.Leader, AccessDecisionId);
        _accessPopulation = accessPopulationFactory.GetAccessPopulation(AccessDecisionId);
        _udlAssetsInvestmentAccountsAccessPopulation =
            accessPopulationFactory.GetAccessPopulation(UdlAssetsInvestmentAccounts);
        _udlAdditionalAssetsAccessPopulation =
            accessPopulationFactory.GetAccessPopulation(UdlAdditionalAssets);
        _udlNonTraditionalAssetsAccessPopulation =
            accessPopulationFactory.GetAccessPopulation(UdlNonTraditionalAssets);
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var isTpoLoanTask = container.StateContainer.Get<bool>(ExplicitStateProviderIds.IsTpoLoan);
        var productInfoTask = container.StateContainer.Get<ProductInfo>();

        var isTpoLoan = await isTpoLoanTask;
        if (isTpoLoan)
        {
            return new ExclusionDecision(persist: true)
            {
                new()
                {
                    Reason = ExclusionReason.ThirdPartyOrigination,
                    ExcludedAt = DateTime.Now,
                }
            };
        }

        var productInfo = await productInfoTask;
        if (productInfo?.ProductSettings?.Any(x => string.Equals(x.HELOC2ndType, "Piggyback", StringComparison.OrdinalIgnoreCase)) == true)
        {
            return new ExclusionDecision(persist: true)
            {
                new()
                {
                    Reason = ExclusionReason.PiggyBackLoan,
                    ExcludedAt = DateTime.Now,
                }
            };
        }

        var accessPopulationTask = _accessPopulation.IsInPopulation();
        var udlAssetsInvestmentAccountsAccessPopulationTask = _udlAssetsInvestmentAccountsAccessPopulation.IsInPopulation();
        var udlAdditionalAssetsAccessPopulationTask = _udlAdditionalAssetsAccessPopulation.IsInPopulation();
        var udlNonTraditionalAssetsAccessPopulationTask = _udlNonTraditionalAssetsAccessPopulation.IsInPopulation();
        var pilotResult = await _pilotCheck.IsInPilot(container);
        var accessPopulation = await accessPopulationTask;
        var udlAssetsInvestmentAccountsAccessPopulation = await udlAssetsInvestmentAccountsAccessPopulationTask;
        var udlAdditionalAssetsAccessPopulation = await udlAdditionalAssetsAccessPopulationTask;
        var udlNonTraditionalAssetsAccessPopulation = await udlNonTraditionalAssetsAccessPopulationTask;

        if (!accessPopulation || !pilotResult.IsInPilot)
        {
            return new ExclusionDecision(persist: true)
            {
                new()
                {
                    Reason = ExclusionReason.NotInPilot,
                    ExcludedAt = DateTime.Now,
                }
            };
        }

        if (!udlAssetsInvestmentAccountsAccessPopulation)
        {
            pilotResult.Features = pilotResult.Features.Except(InvestmentAccountAssetFeatures);
        }

        if (!udlAdditionalAssetsAccessPopulation)
        {
            pilotResult.Features = pilotResult.Features.Except(AdditionalAssetFeatures);
        }

        if (!udlNonTraditionalAssetsAccessPopulation)
        {
            pilotResult.Features = pilotResult.Features.Except(NonTraditionalAssets);
        }

        var rocketLogicAssetsAccessResult = await container.GetAccessDecision(ApplicationId.RocketLogicAssets);
        var overlapFeatures = rocketLogicAssetsAccessResult.AccessDecision
            ? pilotResult.Features.Intersect(rocketLogicAssetsAccessResult.Features)
            : [];

        if (rocketLogicAssetsAccessResult.AccessDecision == true &&
            overlapFeatures.Any())
        {
            pilotResult.Features = pilotResult.Features.Except(overlapFeatures);
        }

        return new ExclusionDecision(persist: true, pilotResult.Features);
    }
}
