using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;
public class EnforceSoftPullRLXPAccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.EnforceSoftPullRLXP;
    private readonly IPilotCheck _pilotCheck;

    public EnforceSoftPullRLXPAccessDecider(PilotCheckFactory pilotCheckFactory)
    {
        _pilotCheck = pilotCheckFactory(PilotCheckType.Leader, AccessDecisionId);
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var (isInPilot, hasLoanData) = await _pilotCheck.IsInPilot(container);

        var exclusionDecision = new ExclusionDecision(hasLoanData && !isInPilot);

        if (!isInPilot)
        {
            exclusionDecision.Add(ExclusionReason.NotInPilot, GetType());
        }

        return exclusionDecision;
    }
}
