using DecisionServices.QualificationGroupMapper;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.Exceptions;
using LoanAccessAuthorizer.Domain.LoanData;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using LoanAccessAuthorizer.Domain.Utilities;
using LoanAccessAuthorizer.PropertyInsuranceQualifier.Models;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class PIQUIAccessDecider : IAccessDecider
{
    private readonly IApplicationAuthorizerDecisionStore _applicationAuthorizerDecisionStore;
    private readonly ILogger _logger;
    private readonly ILoanDataClient _loanDataClient;
    public string AccessDecisionId => ApplicationId.PIQ;
    private const int HomeOwnersInsuranceImportTrackingItemNumber = 903;
    private readonly bool isProductionEnvironment;

    private readonly ISet<string> _allowedPropertyType =
        new HashSet<string>(StringComparer.CurrentCultureIgnoreCase)
        {
            "Single Family",
            "PUD"
        };

    public PIQUIAccessDecider(IApplicationAuthorizerDecisionStore applicationAuthorizerDecisionStore,
        ILogger<PIQUIAccessDecider> logger, ILoanDataClient loanDataClient, EnvironmentInfo environmentInfo)
    {
        _applicationAuthorizerDecisionStore = applicationAuthorizerDecisionStore;
        _logger = logger;
        _loanDataClient = loanDataClient ?? throw new ArgumentNullException(nameof(loanDataClient));
        isProductionEnvironment = environmentInfo.GetEnvironmentName() == EnvironmentName.Prod;
    }

    public async Task<bool> GetAccessDecision(AccessDecisionContainer accessContainer)
    {
        var initialLoanData = await accessContainer.StateContainer.Get<InitialLoanState>();

        var storedDecision =
            await _applicationAuthorizerDecisionStore.GetDecision(initialLoanData.LoanNumber, AccessDecisionId);

        if (storedDecision.HasValue)
        {
            _logger.LogInformation($"Loan number {initialLoanData.LoanNumber} returning from store:{storedDecision.Value}");
            return storedDecision.Value;
        }

        var accessDecision = await DetermineAccessDecision(accessContainer);
        if (accessDecision.shouldPersist)
        {
            _logger.LogInformation(
                $"Loan number {initialLoanData.LoanNumber} returning from decision:{accessDecision.isInPilot} and persisting");
            return await StoreAndLogDecision(initialLoanData.LoanNumber, accessDecision.isInPilot);
        }

        _logger.LogInformation(
            $"Loan number {initialLoanData.LoanNumber} returning from decision:{accessDecision.isInPilot} and not persisting");
        return accessDecision.isInPilot;
    }

    private async Task<(bool isInPilot, bool shouldPersist)> DetermineAccessDecision(AccessDecisionContainer accessContainer)
    {
        var initialLoanData = await accessContainer.StateContainer.Get<InitialLoanState>();

        var response = await accessContainer.StateContainer.Get<InsurancePoliciesResponse>();
        if (response == null)
        {
            _logger.LogInformation(
                $"Loan number {initialLoanData.LoanNumber} failed to retrieve insurance policy");
            return (false, false);
        }

        var hoiPolicy = response.InsurancePolicies.FirstOrDefault(policy => policy.PolicyType == PolicyType.Homeowners);
        if (hoiPolicy == null)
        {
            _logger.LogInformation(
                $"Loan number {initialLoanData.LoanNumber} failed find home insurance policy");
            return (false, true);
        }

        var tpo = await accessContainer.StateContainer.Get<ThirdPartyOrigination>();

        var shouldExcludeCorrespondentLoan = tpo?.IsCorrespondentLoan ?? true;
        if (shouldExcludeCorrespondentLoan)
        {
            _logger.LogInformation(
                $"Loan number {initialLoanData.LoanNumber} is a correspondent loan");
            return (false, true);
        }

        var subjectProperty = await accessContainer.StateContainer.Get<OpenAmpSubjectProperty>();
        if (!_allowedPropertyType.Contains(subjectProperty?.PropertyType))
        {
            _logger.LogInformation(
                $"Loan number {initialLoanData.LoanNumber} is subject property {subjectProperty?.PropertyType}");
            return (false, true);
        }

        var loanDetails = await accessContainer.StateContainer.Get<AmpLoanDetails>();
        var loanPurpose = loanDetails?.LoanPurpose;
        var subjectCurrentLien = await accessContainer.StateContainer.Get<SubjectCurrentLien>();

        if (isProductionEnvironment && loanPurpose == "Refinance" && (subjectCurrentLien?.IsCurrentQlClient ?? true))
        {
            _logger.LogInformation(
                $"Loan number {initialLoanData.LoanNumber} is RM to RM");
            return (false, true);
        }

        try
        {
            var homeOwnersInsuranceTrackingItemHistory =
                await _loanDataClient.GetTrackingItemStatusHistory(initialLoanData.LoanNumber,
                    HomeOwnersInsuranceImportTrackingItemNumber);
            if (!homeOwnersInsuranceTrackingItemHistory.Any())
            {
                _logger.LogInformation(
                    $"Loan number {initialLoanData.LoanNumber} failed to find 903");
                return (false, true);
            }
        }
        catch (RequestNotFoundException)
        {
            _logger.LogInformation("TrackingItem record not found for {loanNumber} and {trackingItemNumber}. Defaulting the access to False", initialLoanData, HomeOwnersInsuranceImportTrackingItemNumber);
            return (false, true);
        }

        var qualificationGroups = await accessContainer.StateContainer.Get<QualificationGroupSet>();
        var isJumbo = qualificationGroups.Contains(QualificationGroup.Jumbo.ToString());
        if (isJumbo)
        {
            _logger.LogInformation(
                $"Loan number {initialLoanData.LoanNumber} is jumbo");
            return (false, true);
        }

        return (true, true);
    }

    private async Task<bool> StoreAndLogDecision(string loanNumber, bool access)
    {
        await _applicationAuthorizerDecisionStore.StoreDecision(loanNumber, AccessDecisionId, access);
        _logger.LogInformation("{App} Pilot decision ({decision}) persisted", AccessDecisionId, access);

        return access;
    }
}
