using LoanAccessAuthorizer.Authorization.Rules.Features;
using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Common.AccessPopulations;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;
public class FeesOrchestratorEvaluationAccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.FeesOrchestratorEvaluation;

    private readonly IPilotCheck _pilotCheck;
    private readonly IAccessPopulation _accessPopulationForOrchestration;
    private readonly IAccessPopulation _accessPopulationForFeeDataOverride;
    private readonly IAccessPopulation _accessPopulationForRLFees;

    public FeesOrchestratorEvaluationAccessDecider(PilotCheckFactory pilotCheckFactory, AccessPopulationFactory accessPopulationFactory)
    {
        _pilotCheck = pilotCheckFactory(PilotCheckType.Normal, AccessDecisionId);
        _accessPopulationForOrchestration = accessPopulationFactory.GetAccessPopulation(FeatureName.FeeRequestOrchestration);
        _accessPopulationForFeeDataOverride = accessPopulationFactory.GetAccessPopulation(FeatureName.FeeDataOverride);
        _accessPopulationForRLFees = accessPopulationFactory.GetAccessPopulation(FeatureName.RLFees);
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var exclusions = new List<Exclusion>();
        var pilotDataTask = _pilotCheck.IsInPilot(container);
        var pilotCheckResultTask = container.GetAccessDecision(ApplicationId.FeesOrchestratorEvaluationParent);
        var pilotDataResult = await pilotDataTask;
        var pilotCheckResult = await pilotCheckResultTask;
        var features = pilotDataResult.Features;
        //FOE Pilot decision made by parent access decider
        if (!pilotCheckResult.AccessDecision || !pilotDataResult.IsInPilot)
        {
            exclusions.Add(new Exclusion
            {
                Reason = ExclusionReason.NotInPilot,
                ExcludedAt = DateTime.UtcNow,
            });
        }
        //Make feature inclusion decisions only if loan is in the pilot, features will be automatically ignored if not in pilot
        else
        {
            features = await GetFeaturesForPilot(container, features);
        }
        return new ExclusionDecision(exclusions, features, persist: true);
    }

    /// <summary>
    /// Executes access decisions for all features for FOE pilot
    /// </summary>
    /// <param name="container"></param>
    /// <param name="pilotFeatures">The list of valid features for the pilot</param>
    /// <returns>The features the pilot will be given access to</returns>
    private async Task<IEnumerable<string>> GetFeaturesForPilot(AccessDecisionContainer container, IEnumerable<string> pilotFeatures)
    {
        var rocketLogicAccessDeciderTask = container.GetAccessDecision(ApplicationId.RCLockout);
        var isInOrchestrationFeaturePopulationTask = _accessPopulationForOrchestration.IsInPopulation();
        var isInFeeDataOverrideFeaturePopulationTask = _accessPopulationForFeeDataOverride.IsInPopulation();
        var isInRlFeesFeaturePopulationTask = _accessPopulationForRLFees.IsInPopulation();
        var rocketLogicAccessDecider = await rocketLogicAccessDeciderTask;
        var isInOrchestrationFeaturePopulation = await isInOrchestrationFeaturePopulationTask;
        var isInFeeDataOverrideFeaturePopulation = await isInFeeDataOverrideFeaturePopulationTask;
        var isInRlFeesFeaturePopulation = await isInRlFeesFeaturePopulationTask;
        var features = pilotFeatures.ToHashSet();
        //Remove FeeRequestOrchestration feature if not in rocket logic or orchestration feature population
        if (!rocketLogicAccessDecider.AccessDecision || !isInOrchestrationFeaturePopulation)
        {
            features.Remove(FeatureName.FeeRequestOrchestration);
        }

        if (!rocketLogicAccessDecider.AccessDecision || !isInFeeDataOverrideFeaturePopulation)
        {
            features.Remove(FeatureName.FeeDataOverride);
        }

        if (!rocketLogicAccessDecider.AccessDecision || !isInRlFeesFeaturePopulation)
        {
            features.Remove(FeatureName.RLFees);
            features.Remove(FeatureName.RLFeesCategoryOrigination);
        }

        return features;
    }
}
