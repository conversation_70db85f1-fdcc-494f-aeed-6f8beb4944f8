using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class POQRMToRMAccessDecider : IAccessDecider
{
    private readonly IApplicationAuthorizerDecisionStore _applicationAuthorizerDecisionStore;
    private readonly ILogger _logger;
    public string AccessDecisionId => ApplicationId.POQRMToRM;

    public POQRMToRMAccessDecider(
        IApplicationAuthorizerDecisionStore applicationAuthorizerDecisionStore,
        ILogger<POQRMToRMAccessDecider> logger)
    {
        _applicationAuthorizerDecisionStore = applicationAuthorizerDecisionStore;
        _logger = logger;
    }

    public async Task<bool> GetAccessDecision(AccessDecisionContainer accessContainer)
    {
        var initialLoanData = await accessContainer.StateContainer.Get<InitialLoanState>();

        var storedDecision =
            await _applicationAuthorizerDecisionStore.GetDecision(initialLoanData.LoanNumber, AccessDecisionId);

        if (storedDecision.HasValue)
        {
            return storedDecision.Value;
        }
        var decision = await DetermineAccessDecision(accessContainer);

        return decision ? await StoreAndLogDecision(initialLoanData.LoanNumber, decision) : decision;
    }

    private async Task<bool> DetermineAccessDecision(AccessDecisionContainer accessContainer)
    {
        return true;
    }

    private async Task<bool> StoreAndLogDecision(string loanNumber, bool access)
    {
        await _applicationAuthorizerDecisionStore.StoreDecision(loanNumber, AccessDecisionId, access);
        _logger.LogInformation("{App} Pilot decision ({decision}) persisted for {loanNumber}", AccessDecisionId, access, loanNumber);

        return access;
    }

    public Task<bool> CanClearDecision(AccessDecisionContainer accessContainer) => Task.FromResult(true);
}
