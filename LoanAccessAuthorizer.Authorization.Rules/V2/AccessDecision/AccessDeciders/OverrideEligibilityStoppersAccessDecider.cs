using LoanAccessAuthorizer.Authorization.Rules.Features;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;
using LoanAccessAuthorizer.Domain.Product.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class OverrideEligibilityStoppersAccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.OverrideEligibilityStoppers;
    private readonly IFeatureEnabledConfigProvider _featureEnabledConfigProvider;
    private readonly Type _exclusionDeciderType = typeof(OverrideEligibilityStoppersAccessDecider);
    private const string ClosedEndSecondProductParamName = "86";

    public OverrideEligibilityStoppersAccessDecider(
        IFeatureEnabledConfigProvider featureEnabledConfigProvider
    )
    {
        _featureEnabledConfigProvider = featureEnabledConfigProvider;
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var persistDecision = false;
        var exclusions = new List<(ExclusionReason Type, string Comment)>();

        var isFeatureEnabled = _featureEnabledConfigProvider.IsFeatureEnabled(
            FeatureName.OverrideEligibilityStoppers
        );
        if (!isFeatureEnabled)
        {
            persistDecision = true;
            exclusions.Add(new(ExclusionReason.NotInPilot, "This Features is not turned ON"));
        }

        var isInRateLockServiceV2Pilot = await container.GetAccessDecision(
            ApplicationId.RateLockServiceV2
        );
        if (!isInRateLockServiceV2Pilot)
        {
            persistDecision = true;
            exclusions.Add(
                new(ExclusionReason.NotInPilot, "Must also be in RateLockServiceV2 pilot.")
            );
        }

        var productInfo = await container.StateContainer.Get<ProductInfo>();
        if (
            !productInfo.ProductParameters.Any(
                pp =>
                    string.Equals(
                        pp.ParameterName,
                        ClosedEndSecondProductParamName,
                        StringComparison.OrdinalIgnoreCase
                    )
            )
        )
        {
            exclusions.Add(
                new(ExclusionReason.NotInPilot, "Only ClosedEndSecond products are in pilot.")
            );
        }

        if (exclusions.Count > 0)
        {
            var decision = new ExclusionDecision(persistDecision);
            foreach (var exclusion in exclusions)
            {
                decision.Add(exclusion.Type, _exclusionDeciderType, exclusion.Comment);
            }

            return decision;
        }

        return new ExclusionDecision(false);
    }
}
