using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class EnforcePaystubFolderMilestoneAccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.EnforcePaystubFolderMilestone;

    private readonly IPilotCheck _pilotCheck;

    public EnforcePaystubFolderMilestoneAccessDecider(
        PilotCheckFactory pilotCheckFactory
    )
    {
        _pilotCheck = pilotCheckFactory(PilotCheckType.Leader, AccessDecisionId);
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var exclusionDecision = new ExclusionDecision(true);

        var (accessDecision, _) = await _pilotCheck.IsInPilot(container);

        if (!accessDecision)
        {
            exclusionDecision.Add(new Exclusion
            {
                Comment = $"default fallback exclusion provider by {GetType().Name}",
                ExcludedAt = DateTime.Now,
                Reason = ExclusionReason.DefaultDeciderExclusion,
            });
        }

        return exclusionDecision;
    }
}
