using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class RocketDocsAccessDecider : PilotAccessDecider
{
    public override string AccessDecisionId => ApplicationId.RocketDocs;
    public RocketDocsAccessDecider(IApplicationAuthorizerDecisionStore applicationAuthorizerDecisionStore,
        PilotCheckFactory pilotCheckFactory, ILogger<RocketDocsAccessDecider> logger) :
        base(applicationAuthorizerDecisionStore, pilotCheckFactory, logger)
    {
    }

}
