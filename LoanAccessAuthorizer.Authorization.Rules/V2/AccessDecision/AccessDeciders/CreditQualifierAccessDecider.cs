using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.Extensions;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class CreditQualifierAccessDecider : PilotAccessDecider, IClearDecision
{
    public override string AccessDecisionId => ApplicationId.Credit;

    public Task<bool> CanClearDecision(AccessDecisionContainer accessContainer) =>
        accessContainer.StateContainer.ApplicationHasTrueCachedDecision(
            _decisionStore,
            AccessDecisionId
        );

    public CreditQualifierAccessDecider(
        IApplicationAuthorizerDecisionStore applicationAuthorizerDecisionStore,
        PilotCheckFactory pilotCheckFactory,
        ILogger<CreditQualifierAccessDecider> logger
    )
        : base(applicationAuthorizerDecisionStore, pilotCheckFactory, logger) { }

    protected override async Task<(bool isInPilot, bool shouldPersist)> DetermineAccessDecision(
        AccessDecisionContainer accessContainer
    )
    {
        var isInPilot = await Task.WhenAll(
            IsRocketLogicBankingLoan(accessContainer),
            IsRlApiLoan(accessContainer)
        );

        if (isInPilot.Any(inPilot => inPilot))
        {
            return (true, true);
        }

        return await base.DetermineAccessDecision(accessContainer);
    }

    private static async Task<bool> IsRocketLogicBankingLoan(
        AccessDecisionContainer accessContainer
    )
    {
        return (
            await accessContainer.StateContainer.IsRocketLogicBankingLoan()
        ).isRocketLogicBankingLoan;
    }

    private static async Task<bool> IsRlApiLoan(AccessDecisionContainer accessDecisionContainer)
    {
        var rlApiLoan = await accessDecisionContainer.StateContainer.Get<RocketLogicLoanDetails>();
        return rlApiLoan?.IsRocketLogicApiLoan ?? false;
    }
}
