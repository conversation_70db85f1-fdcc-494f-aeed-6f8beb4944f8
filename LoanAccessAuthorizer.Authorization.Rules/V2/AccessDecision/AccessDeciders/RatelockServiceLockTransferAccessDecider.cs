using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class RatelockServiceLockTransferAccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.RatelockServiceLockTransfer;
    private readonly IPilotCheck _pilotCheck;
    private readonly Type _exclusionDeciderType = typeof(RatelockServiceLockTransferAccessDecider);

    public RatelockServiceLockTransferAccessDecider(PilotCheckFactory pilotCheckFactory)
    {
        _pilotCheck = pilotCheckFactory(PilotCheckType.Leader, AccessDecisionId);
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var exclusionDescision = new ExclusionDecision(true);

        var (accessDecision, _) = await _pilotCheck.IsInPilot(container);

        if (!accessDecision)
        {
            exclusionDescision.Add(ExclusionReason.NotInPilot, _exclusionDeciderType, "Loan is not part of this pilot.");
            return exclusionDescision;
        }

        var isInRateLockServiceV2Pilot = await container.GetAccessDecision(ApplicationId.RateLockServiceV2);
        if (!isInRateLockServiceV2Pilot)
        {
            exclusionDescision.Add(ExclusionReason.NotInPilot, _exclusionDeciderType, "Must also be in RateLockServiceV2 pilot.");
        }

        if (exclusionDescision.IsExcluded)
        {
            return exclusionDescision;
        }

        return new ExclusionDecision(false);
    }
}
