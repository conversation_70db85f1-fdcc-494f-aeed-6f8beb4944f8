using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using Microsoft.Extensions.Logging;
using LoanAccessAuthorizer.Domain.LeadData.Models;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class RocketLogicBankingAccessDecider : IExclusionDecider
{
    private readonly IApplicationAuthorizerDecisionStore _decisionStore;
    private readonly ILogger<RocketLogicBankingAccessDecider> _logger;

    public RocketLogicBankingAccessDecider(
        IApplicationAuthorizerDecisionStore decisionStore,
        ILogger<RocketLogicBankingAccessDecider> logger
    )
    {
        _decisionStore = decisionStore;
        _logger = logger;
    }

    public string AccessDecisionId => ApplicationId.RocketLogicBanking;

    public async Task<ExclusionDecision> GetExclusionDecision(
        AccessDecisionContainer accessContainer
    )
    {
        var (rlbLoanDetails, rlbLeadDetails) = await GetRlbDetails(accessContainer);

        //always check openAMP for loans that are RMA because we will always have rocket logic loan details for those.
        if (rlbLoanDetails != null && !IsRmaLoan(rlbLeadDetails?.ApplicationStatus))
        {
            var isRlbLoanInAmpArchive = await accessContainer.StateContainer.Get<bool>(
                ExplicitStateProviderIds.IsLoanArchivedInAmp
            );
            if (isRlbLoanInAmpArchive)
            {
                return new ExclusionDecision(persist: true)
                {
                    new Exclusion
                    {
                        Reason = ExclusionReason.InAmpArchive,
                        ExcludedAt = DateTime.Now
                    }
                };
            }

            return new ExclusionDecision(persist: false);
        }

        var legacyStoredDecision = await GetLegacyStoredDecision(accessContainer);
        if (legacyStoredDecision is false)
        {
            _logger.LogInformation(
                "Stored Decision for {AccessDecisionId} is false",
                AccessDecisionId
            );
            return new ExclusionDecision(persist: true)
            {
                new Exclusion { Reason = ExclusionReason.NotInPilot, ExcludedAt = DateTime.Now }
            };
        }

        //if loan is imported by RL-B, then it should be allowed to be worked in RL-B.
        //This is to support LIS loans.
        if (rlbLoanDetails?.IsLoanImported ?? false)
        {
            return new ExclusionDecision(persist: false);
        }

        //Check amp if even if stored decision is true (as loan might be created in amp after last time we checked)
        _logger.LogInformation("Checking if loan is in AMP.");

        var isLoanInAmp = await accessContainer.StateContainer.Get<bool>(
            ExplicitStateProviderIds.IsLoanInAmp
        );
        var isOriginatedByRLB = isLoanInAmp
            ? await accessContainer.StateContainer.Get<bool>(
                ExplicitStateProviderIds.IsLoanOriginatedByRLB
            )
            : true;

        if (!isOriginatedByRLB)
        {
            return new ExclusionDecision(persist: true)
            {
                new Exclusion
                {
                    Reason = ExclusionReason.OriginatedByAmp,
                    ExcludedAt = DateTime.Now
                }
            };
        }

        _logger.LogInformation("Checking if loan is in AMP archived.");
        var isLoanArchived = await accessContainer.StateContainer.Get<bool>(
            ExplicitStateProviderIds.IsLoanArchivedInAmp
        );
        if (isLoanArchived)
        {
            return new ExclusionDecision(persist: true)
            {
                new Exclusion { Reason = ExclusionReason.InAmpArchive, ExcludedAt = DateTime.Now }
            };
        }

        return new ExclusionDecision(persist: false);
    }

    private async Task<bool?> GetLegacyStoredDecision(AccessDecisionContainer accessContainer)
    {
        var initialLoanData = await accessContainer.StateContainer.Get<InitialLoanState>();
        var storedDecision = await _decisionStore.GetDecision(
            initialLoanData.LoanNumber,
            AccessDecisionId
        );

        return storedDecision;
    }

    private static async Task<(
        RocketLogicBankingLoanDetails,
        RocketLogicBankingLeadDetails
    )> GetRlbDetails(AccessDecisionContainer accessContainer)
    {
        var rlbLoanDetailsTask =
            accessContainer.StateContainer.Get<RocketLogicBankingLoanDetails>();
        var rlbLeadDetailsTask =
            accessContainer.StateContainer.Get<RocketLogicBankingLeadDetails>();

        await Task.WhenAll(rlbLoanDetailsTask, rlbLeadDetailsTask);

        return (rlbLoanDetailsTask.Result, rlbLeadDetailsTask.Result);
    }

    private static bool IsRmaLoan(ApplicationStatus? applicationStatus)
    {
        return applicationStatus switch
        {
            ApplicationStatus.ApplicationInitializationPending => true,
            ApplicationStatus.ApplicationInitialized => true,
            ApplicationStatus.ApplicationInitializationFailed => true,
            _ => false
        };
    }
}
