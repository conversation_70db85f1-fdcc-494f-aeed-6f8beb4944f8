using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class RacAccessDecider : IExclusionDecider
{
    private readonly IApplicationAuthorizerDecisionStore _decisionStore;

    public RacAccessDecider(IApplicationAuthorizerDecisionStore decisionStore)
    {
        _decisionStore = decisionStore ?? throw new ArgumentNullException(nameof(decisionStore));
    }

    public string AccessDecisionId => ApplicationId.RequiredAssetsCalculator;

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer accessContainer)
    {
        var initialLoanData = await accessContainer.StateContainer.Get<InitialLoanState>();
        var loanNumber = initialLoanData.LoanNumber;

        var storedDecision = await _decisionStore.GetDecision(loanNumber, AccessDecisionId);
        if (storedDecision.HasValue)
        {
            var legacyDecision = new ExclusionDecision(persist: true);

            if (!storedDecision.Value)
            {
                legacyDecision.Add(new Exclusion
                {
                    Reason = ExclusionReason.NotInPilot,
                    ExcludedAt = DateTime.UtcNow,
                    Comment = "Migrating legacy pilot decision"
                });
            }

            return legacyDecision;
        }

        return new ExclusionDecision(persist: true);
    }
}
