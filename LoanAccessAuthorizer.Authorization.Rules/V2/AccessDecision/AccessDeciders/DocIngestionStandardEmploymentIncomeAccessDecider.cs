using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class DocIngestionStandardEmploymentIncomeAccessDecider : IAccessDecider
{
    private readonly IApplicationAuthorizerDecisionStore _applicationAuthorizerDecisionStore;
    private readonly ILogger _logger;

    public string AccessDecisionId => ApplicationId.DocIngestionStandardEmploymentIncome;

    public DocIngestionStandardEmploymentIncomeAccessDecider(IApplicationAuthorizerDecisionStore applicationAuthorizerDecisionStore,
         ILogger<DocIngestionStandardEmploymentIncomeAccessDecider> logger)
    {
        _applicationAuthorizerDecisionStore = applicationAuthorizerDecisionStore;
        _logger = logger;
    }

    public async Task<bool> GetAccessDecision(AccessDecisionContainer accessContainer)
    {
        var initialLoanData = await accessContainer.StateContainer.Get<InitialLoanState>();

        var storedDecision = await _applicationAuthorizerDecisionStore.GetDecision(initialLoanData.LoanNumber, AccessDecisionId);

        if (storedDecision.HasValue)
        {
            return storedDecision.Value;
        }

        var isInDocIngestionW2PilotTask = accessContainer.GetAccessDecision(ApplicationId.DocIngestionW2);
        var isInDocIngestionPaystubPilotTask = accessContainer.GetAccessDecision(ApplicationId.DocIngestionPaystub);
        var isInDocIngestionThirdPartyPilotTask = accessContainer.GetAccessDecision(ApplicationId.DocIngestionThirdParty);

        var pilotResults = await Task.WhenAll(isInDocIngestionW2PilotTask, isInDocIngestionPaystubPilotTask, isInDocIngestionThirdPartyPilotTask);
        var isInPilot = pilotResults.Any(result => result);

        return await StoreAndLogDecision(initialLoanData.LoanNumber, isInPilot);
    }

    private async Task<bool> StoreAndLogDecision(string loanNumber, bool access)
    {
        await _applicationAuthorizerDecisionStore.StoreDecision(loanNumber, AccessDecisionId, access);
        _logger.LogInformation("{App} Pilot decision ({decision}) persisted", AccessDecisionId, access);

        return access;
    }
}
