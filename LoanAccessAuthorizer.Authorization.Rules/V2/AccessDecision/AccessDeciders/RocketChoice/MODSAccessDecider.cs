using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.Extensions;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Exceptions;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.RocketChoice;

public class MODSAccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.MODS;

    private readonly IPilotCheck _pilotCheck;
    private readonly IPilotCheck _extendModsCheck;
    private readonly Type _exclusionDeciderType = typeof(MODSAccessDecider);
    private readonly ILogger<MODSAccessDecider> _logger;
    private readonly IApplicationAuthorizerDecisionStore _decisionStore;

    public MODSAccessDecider(PilotCheckFactory pilotCheckFactory, IApplicationAuthorizerDecisionStore applicationAuthorizerDecisionStore,
        ILogger<MODSAccessDecider> logger)
    {
        _pilotCheck = pilotCheckFactory(PilotCheckType.Leader, ApplicationId.MODS);
        _extendModsCheck = pilotCheckFactory(PilotCheckType.Leader, ApplicationId.ExtendMODS);
        _logger = logger;
        _decisionStore = applicationAuthorizerDecisionStore;
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var baseDecision = new ExclusionDecision(persist: false);
        var pilotDecisionTask = IsInPilotCheck(container);
        var leadMetadataTask = container.StateContainer.Get<RocketLogicBankingLeadDetails>();
        var isRlbReadOnlyTask = container.StateContainer.Get<bool?>(ExplicitStateProviderIds.IsRlbReadOnly);
        var storedLegacyDecision = await GetStoredLegacyDecision(container);
        if (storedLegacyDecision == false)
        {
            return new ExclusionDecision(persist: true)
            {
                { ExclusionReason.NotInPilot, GetType(), "Legacy Decision"}
            };
        }
        var pilotDecision = await pilotDecisionTask;
        if (pilotDecision == false)
        {
            baseDecision.ExtendOrAdd(
                ExclusionReason.NotInPilot,
                _exclusionDeciderType,
                "This loan is not in the MODS pilot."
            );
        }

        var leadMetadata = await leadMetadataTask;
        var isRlbReadOnly = await isRlbReadOnlyTask;
        var completedInRlb = leadMetadata?.RlbCompleted;

        // If the loan is completed in RLB, or it doesn't exist in RLB
        if (completedInRlb is not false || isRlbReadOnly is not false)
        {
            var (accessDecision, _) = await _extendModsCheck.IsInPilot(container);
            if (!accessDecision)
            {
                baseDecision.ExtendOrAdd(
                    ExclusionReason.UnsupportedState,
                    _exclusionDeciderType,
                    "The MODS Pilot only supports loans in Rocket Logic Banking."
                 );
            }
        }

        return baseDecision;
    }

    private async Task<bool?> IsInPilotCheck(AccessDecisionContainer accessDecisionContainer)
    {
        bool isInPilot;
        try
        {
            var isRcLockoutLoanTask = accessDecisionContainer.GetAccessDecision(ApplicationId.RCLockout);
            (isInPilot, _) = await _pilotCheck.IsInPilot(accessDecisionContainer);
            isInPilot = isInPilot && await isRcLockoutLoanTask;
        }
        catch (LoanDoesNotExistInAmpException)
        {
            return true;
        }

        return isInPilot;
    }

    private async Task<bool?> GetStoredLegacyDecision(AccessDecisionContainer accessContainer)
    {
        var initialLoanState = await accessContainer.StateContainer.Get<InitialLoanState>();
        return await _decisionStore.GetDecision(initialLoanState.LoanNumber, AccessDecisionId);
    }
}
