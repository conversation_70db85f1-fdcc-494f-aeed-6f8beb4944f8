using System.ComponentModel;
using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.Models;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;
using LoanAccessAuthorizer.Domain.TeamMemberData;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.RocketChoice;

public class RLPPostRateLockAccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.RLPPostRateLock;
    private readonly ILeaderHierarchyProvider _leaderHierarchyProvider;
    private readonly IPilotCheck _pilotCheck;

    public RLPPostRateLockAccessDecider(
        IApplicationAuthorizerDecisionStore applicationAuthorizerDecisionStore,
        PilotCheckFactory pilotCheckFactory, ILogger<RLPPostRateLockAccessDecider> logger,
        ILeaderHierarchyProvider leaderHierarchyProvider
    )
    {
        _leaderHierarchyProvider = leaderHierarchyProvider;
        _pilotCheck = pilotCheckFactory(PilotCheckType.Leader, AccessDecisionId);
    }

    public async Task<ExclusionDecision> GetExclusionDecision(
        AccessDecisionContainer accessContainer)
    {
        var exclusionDecision = new ExclusionDecision(false);
        var (accessDecision, _) = await _pilotCheck.IsInPilot(
            accessContainer
        );
        if (!accessDecision)
        {
            var loanOfficerCommonId = await accessContainer.StateContainer.Get<UserId>(ExplicitStateProviderIds.LoanOfficerCommonId);
            var leaderHierarchy = await _leaderHierarchyProvider.GetLeaderHierarchy(loanOfficerCommonId);
            exclusionDecision = new ExclusionDecision(true)
            {
                {
                    ExclusionReason.NotInPilot, GetType(), "No valid pilot config, ids checked: " + $"{loanOfficerCommonId}, {string.Join(',', leaderHierarchy.LeaderCommonIds)}"
                }
            };
        }

        return exclusionDecision;
    }
}
