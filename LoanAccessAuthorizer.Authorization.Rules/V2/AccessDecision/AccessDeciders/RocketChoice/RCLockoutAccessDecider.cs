using System.Collections.Immutable;
using System.Globalization;
using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.Extensions;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Common.Constants;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using LoanAccessAuthorizer.Domain.Models;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;
using LoanAccessAuthorizer.Domain.TeamMemberData;
using LoanAccessAuthorizer.RocketLogicBanking;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.RocketChoice;

public class RCLockoutAccessDecider : IExclusionDecider, IClearDecision
{
    public string AccessDecisionId => ApplicationId.RCLockout;
    private readonly IApplicationAuthorizerDecisionStore _decisionStore;
    private readonly ILeaderHierarchyProvider _leaderHierarchyProvider;
    private readonly IPilotCheck _pilotCheck;
    private readonly RLBankingServiceClient _rlBankingServiceClient;

    private const string RelocationLoan = "Relocation loans are unsupported";
    private const string AmeripriseHelocLoan = "Ameriprise Heloc loans are unsupported";

    public RCLockoutAccessDecider(
        IApplicationAuthorizerDecisionStore decisionStore,
        PilotCheckFactory pilotCheckFactory,
        ILeaderHierarchyProvider leaderHierarchyProvider,
        RLBankingServiceClient rLBankingServiceClient
    )
    {
        _decisionStore = decisionStore;
        _leaderHierarchyProvider = leaderHierarchyProvider;
        _pilotCheck = pilotCheckFactory(PilotCheckType.Leader, AccessDecisionId);
        _rlBankingServiceClient = rLBankingServiceClient;
    }

    private static readonly ImmutableHashSet<string> excludedLoanStatuses =
        ImmutableHashSet.Create(StringComparer.CurrentCultureIgnoreCase, new[]
        {
            LoanStatus.FolderReceived.ToString(CultureInfo.CurrentCulture.NumberFormat),
            LoanStatus.Denied.ToString(CultureInfo.CurrentCulture.NumberFormat),
            LoanStatus.Withdrawn.ToString(CultureInfo.CurrentCulture.NumberFormat),
            LoanStatus.LoanRescinded.ToString(CultureInfo.CurrentCulture.NumberFormat),
            LoanStatus.FileClosedForIncompleteness.ToString(
                CultureInfo.CurrentCulture.NumberFormat
            ),
            LoanStatus.ApplicationApprovedNotAccepted.ToString(
                CultureInfo.CurrentCulture.NumberFormat
            ),
            LoanStatus.LoggedIntoAccounting.ToString(CultureInfo.CurrentCulture.NumberFormat)
        });

    private static readonly ImmutableHashSet<string> excludedChannels =
        ImmutableHashSet.Create(StringComparer.CurrentCultureIgnoreCase, new[]
        {
            "Schwab",
            "Cadillac",
            "Relocation",
        });

    /// <summary>
    /// If a loan officer has not been assigned,
    /// some services will provide a default, fallback common ID.
    /// In this case, when returning a <c>NotInPilot</c>, <c>"No valid pilot config"</c> exclusion,
    /// we will not persist the decision so it may be rerun once a loan officer has been assigned.
    /// </summary>
    private static readonly HashSet<string> _ephemeralServiceLoanOfficerCommonIds = new()
    {
        CommonIdConstants.DefaultCommonId
    };

    public Task<bool> CanClearDecision(AccessDecisionContainer accessContainer) =>
        accessContainer.StateContainer.ApplicationHasTrueCachedDecision(
            _decisionStore,
            AccessDecisionId
        );

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var ampKeyLoanInfo = await container.StateContainer.Get<AmpKeyLoanInfo>();

        if (excludedChannels.Contains(ampKeyLoanInfo.LoanChannel))
        {
            return new ExclusionDecision(persist: true)
            {
                { ExclusionReason.UnsupportedLoanChannel, GetType() }
            };
        }

        var initialLoanState = await container.StateContainer.Get<InitialLoanState>();
        var lisDecisionTask = container.GetAccessDecision(ApplicationId.LoanImporterService);
        var loanImportCompleteTask = container.StateContainer.Get<bool>(
            ExplicitStateProviderIds.IsLoanInRocketChoice
        );
        var rlbLeadMetadataTask = _rlBankingServiceClient.GetLead(initialLoanState.LoanNumber);
        var rlbLeadDetailsTask = container.StateContainer.Get<RocketLogicBankingLeadDetails>();
        var isAmeripriseHelocTask = container.StateContainer.Get<bool>(ExplicitStateProviderIds.IsHelocAmeripriseLoan);

        var lisDecision = await lisDecisionTask;
        var loanImportComplete = await loanImportCompleteTask;
        var rlbLeadMetadata = await rlbLeadMetadataTask;
        var rlbLead = await rlbLeadDetailsTask;
        var isAmeripriseHelocLoan = await isAmeripriseHelocTask;

        if (isAmeripriseHelocLoan)
        {
            return new ExclusionDecision(persist: true)
            {
                { ExclusionReason.AmeripriseHelocLoan, GetType(), AmeripriseHelocLoan }
            };
        }
        if (rlbLead.IsAssumption)
        {
            return new ExclusionDecision(persist: true)
            {
                { ExclusionReason.AssumptionLoan, GetType() }
            };
        }
        if (lisDecision && !loanImportComplete)
        {
            return new ExclusionDecision(persist: false);
        }

        if (rlbLead?.IsRelocation ?? false)
        {
            return new ExclusionDecision(persist: true)
            {
                { ExclusionReason.RelocationLoan, GetType(), RelocationLoan }
            };
        }

        var storedLegacyDecision = await _decisionStore.GetDecision(initialLoanState.LoanNumber, AccessDecisionId);

        if (storedLegacyDecision == false)
        {
            return new ExclusionDecision(persist: true)
            {
                { ExclusionReason.NotInPilot, GetType(), "Legacy Decision"}
            };
        }

        var (accessDecision, hasLoanData) = await _pilotCheck.IsInPilot(container);

        if (!accessDecision)
        {
            var loanOfficerCommonId = await container.StateContainer.Get<UserId>(ExplicitStateProviderIds.LoanOfficerCommonId);
            var leaderHierarchy = await _leaderHierarchyProvider.GetLeaderHierarchy(loanOfficerCommonId);
            var hasValidLoanOfficerCommonId = loanOfficerCommonId != null &&
                loanOfficerCommonId.Type == UserIdType.CommonId &&
                !string.IsNullOrWhiteSpace(loanOfficerCommonId.Id) &&
                !_ephemeralServiceLoanOfficerCommonIds.Contains(loanOfficerCommonId.Id);
            return new ExclusionDecision(persist: hasValidLoanOfficerCommonId && hasLoanData)
            {
                { ExclusionReason.NotInPilot, GetType(), $"No valid pilot config, ids checked" +
                $" {loanOfficerCommonId},{string.Join(',', leaderHierarchy.LeaderCommonIds)}"}
            };
        }

        if (!(rlbLeadMetadata?.LeadStatusDetails?.IsActive ?? true))
        {
            return new ExclusionDecision(persist: false)
            {
                { ExclusionReason.UnsupportedPricingScenario, GetType(), "Lead status is inactive" },
            };
        }

        var exclusionDecision = await GetExclusionDecisionInternal(container, ampKeyLoanInfo);
        return exclusionDecision.Any()
            ? new ExclusionDecision(exclusionDecision)
            : new ExclusionDecision(persist: false);
    }

    private static async Task<List<Exclusion>> GetExclusionDecisionInternal(
        AccessDecisionContainer accessContainer, AmpKeyLoanInfo ampKeyLoanInfo
    )
    {
        var exclusionDecisions = new List<Exclusion>();
        var loanStatusesTask = accessContainer.StateContainer.Get<AmpLoanStatusCollection>();
        var loanDetailsTask = accessContainer.StateContainer.Get<AmpLoanDetails>();
        var studentLoanCashoutTask = accessContainer.StateContainer.Get<AmpStudentLoanCashout>();
        var scheduleOfRealEstateOwnedTask =
            accessContainer.StateContainer.Get<AmpScheduleOfRealEstateOwnedCollection>();
        var hasSubordinateLienTask = accessContainer.StateContainer.Get<bool>(ExplicitStateProviderIds.HasSubordinateFinancing);

        var postRateLockDecisionTask = accessContainer.GetAccessDecision(
            ApplicationId.RLPPostRateLock
        );
        var isOriginatedByRlbTask =
            accessContainer.StateContainer.Get<bool>(ExplicitStateProviderIds.IsLoanOriginatedByRLB);

        var loanStatuses = await loanStatusesTask;
        var loanDetails = await loanDetailsTask;
        var scheduleOfRealEstateOwned = await scheduleOfRealEstateOwnedTask;
        var studentLoanCashout = await studentLoanCashoutTask;
        var isOriginatedByRlb = await isOriginatedByRlbTask;
        var postRateLockDecision = await postRateLockDecisionTask;
        var hasSubordinateLien = await hasSubordinateLienTask;

        var currentExcludedStatuses = loanStatuses
            .Where(status => excludedLoanStatuses.Contains(status.LoanStatusId))
            .Select(status => status.LoanStatusName).ToList();

        if (currentExcludedStatuses.Any())
        {
            var comment = $"Excluded statuses: {string.Join(", ", currentExcludedStatuses)}";
            exclusionDecisions.Add(AddExclusion(ExclusionReason.UnsupportedPricingScenario, comment));
        }

        if (ampKeyLoanInfo.IsQLMSLoan ?? true)
        {
            exclusionDecisions.Add(AddExclusion(ExclusionReason.ThirdPartyOrigination));
        }

        if (
            loanDetails?.LeadTypeCode?.Contains("Assump", StringComparison.CurrentCultureIgnoreCase)
            ?? false
        )
        {
            exclusionDecisions.Add(AddExclusion(ExclusionReason.AssumptionLoan));
        }

        if (loanDetails.RateLockIndicator && !postRateLockDecision)
        {
            exclusionDecisions.Add(AddExclusion(ExclusionReason.RateLocked));
        }

        if (studentLoanCashout?.IsStudentLoanCashout == true)
        {
            exclusionDecisions.Add(AddExclusion(ExclusionReason.StudentLoanCashout));
        }

        if (!isOriginatedByRlb)
        {
            exclusionDecisions.Add(AddExclusion(ExclusionReason.NotInPilot, "Loan not originated by RLB"));
        }

        if (
            scheduleOfRealEstateOwned.Any(
                realEstateOwned =>
                    (realEstateOwned.Divorce ?? false)
                    || string.Equals(realEstateOwned.LienPosition, "Divorce Settlement", StringComparison.OrdinalIgnoreCase)
                    || (realEstateOwned.Inheritance ?? false)
                    || string.Equals(realEstateOwned.LienPosition, "Inherited Property Buyout", StringComparison.OrdinalIgnoreCase)
            )
        )
        {
            exclusionDecisions.Add(AddExclusion(ExclusionReason.EquityBuyoutLien));
        }

        if (hasSubordinateLien)
        {
            exclusionDecisions.Add(AddExclusion(ExclusionReason.SubordinateFinancing));
        }

        return exclusionDecisions;
    }

    private static Exclusion AddExclusion(ExclusionReason exclusionReason) =>
        new() { Reason = exclusionReason, ExcludedAt = DateTime.UtcNow };

    private static Exclusion AddExclusion(ExclusionReason exclusionReason, string comment) =>
        new() { Reason = exclusionReason, ExcludedAt = DateTime.UtcNow, Comment = comment };
}
