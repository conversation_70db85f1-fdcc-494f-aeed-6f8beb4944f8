using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using LoanAccessAuthorizer.Domain.Models;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;
using LoanAccessAuthorizer.Domain.TeamMemberData;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.RocketChoice;

public class PricingLockoutAccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.PricingLockout;
    private readonly IApplicationAuthorizerDecisionStore _decisionStore;
    private readonly ILeaderHierarchyProvider _leaderHierarchyProvider;
    private readonly IPilotCheck _pilotCheck;

    public PricingLockoutAccessDecider(
        IApplicationAuthorizerDecisionStore applicationAuthorizerDecisionStore,
        PilotCheckFactory pilotCheckFactory,
        ILogger<PricingLockoutAccessDecider> logger,
        ILeaderHierarchyProvider leaderHierarchyProvider
    )
    {
        _decisionStore = applicationAuthorizerDecisionStore;
        _leaderHierarchyProvider = leaderHierarchyProvider;
        _pilotCheck = pilotCheckFactory(PilotCheckType.Leader, AccessDecisionId);
    }

    public async Task<ExclusionDecision> GetExclusionDecision(
        AccessDecisionContainer accessContainer)
    {
        var exclusionDecision = new ExclusionDecision(false);

        var isRcLockoutLoanTask = accessContainer.GetAccessDecision(ApplicationId.RCLockout);
        var accessDecisionTask = GetAccessDecision(accessContainer);

        var isRcLockoutLoan = await isRcLockoutLoanTask;
        var accessDecision = await accessDecisionTask;

        var decision = isRcLockoutLoan && accessDecision;
        if (!decision)
        {
            var loanOfficerCommonId = await accessContainer.StateContainer.Get<UserId>(ExplicitStateProviderIds.LoanOfficerCommonId);
            var leaderHierarchy = await _leaderHierarchyProvider.GetLeaderHierarchy(loanOfficerCommonId);
            exclusionDecision = new ExclusionDecision(true)
            {
                {
                    ExclusionReason.NotInPilot, GetType(), "No valid pilot config, ids checked: " + $"{loanOfficerCommonId},{string.Join(',', leaderHierarchy.LeaderCommonIds)}"
                }
            };
        }

        return exclusionDecision;
    }

    private async Task<bool> GetAccessDecision(AccessDecisionContainer accessContainer)
    {
        var (accessDecision, _) = await _pilotCheck.IsInPilot(accessContainer);
        return accessDecision;
    }
}
