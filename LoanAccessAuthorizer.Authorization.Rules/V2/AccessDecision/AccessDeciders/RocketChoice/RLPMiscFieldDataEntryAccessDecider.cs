using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.RocketChoice;

public class RLPMiscFieldDataEntryAccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.RLPMiscFieldDataEntry;
    private readonly IApplicationAuthorizerDecisionStore _decisionStore;
    private readonly IPilotCheck _pilotCheck;

    public RLPMiscFieldDataEntryAccessDecider(IApplicationAuthorizerDecisionStore applicationAuthorizerDecisionStore,
        PilotCheckFactory pilotCheckFactory, ILogger<RLPMiscFieldDataEntryAccessDecider> logger)
    {
        _decisionStore = applicationAuthorizerDecisionStore;
        _pilotCheck = pilotCheckFactory(PilotCheckType.Leader, AccessDecisionId);
    }

    public async Task<ExclusionDecision> GetExclusionDecision(
        AccessDecisionContainer accessContainer)
    {
        var isRcLockoutLoanTask = accessContainer.GetAccessDecision(ApplicationId.RCLockout);
        var accessDecisionTask = GetAccessDecision(accessContainer);

        var isRcLockoutLoan = await isRcLockoutLoanTask;
        var accessDecision = await accessDecisionTask;

        var decision = isRcLockoutLoan && accessDecision;
        if (!decision)
        {
            return new ExclusionDecision(true)
            {
                { ExclusionReason.NotInPilot, GetType(), "Not in pilot" }
            };
        }

        return new ExclusionDecision(false);
    }

    private async Task<bool> GetAccessDecision(AccessDecisionContainer accessContainer)
    {
        var (accessDecision, _) = await _pilotCheck.IsInPilot(accessContainer);
        return accessDecision;
    }
}