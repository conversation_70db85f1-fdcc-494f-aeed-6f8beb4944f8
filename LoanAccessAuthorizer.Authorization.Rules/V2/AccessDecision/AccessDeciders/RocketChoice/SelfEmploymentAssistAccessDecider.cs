﻿namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.RocketChoice;

public class SelfEmploymentAssistAccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.SelfEmploymentAssist;

    public Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        return Task.FromResult(new ExclusionDecision());
    }
}
