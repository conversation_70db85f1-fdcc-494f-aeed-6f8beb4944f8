﻿namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.RocketChoice;

public class RCFormatExceptionsAccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.RCFormatExceptions;
    public Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer accessContainer)
    {
        return Task.FromResult(new ExclusionDecision(true));
    }
}
