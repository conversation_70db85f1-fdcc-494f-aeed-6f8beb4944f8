using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.RocketChoice;

public class PEAccessDecider : LeaderPilotAccessDecider
{
    protected override bool ShouldPersist => true;
    public override string AccessDecisionId => ApplicationId.PricingElasticity;

    public PEAccessDecider(IApplicationAuthorizerDecisionStore applicationAuthorizerDecisionStore,
        PilotCheckFactory pilotCheckFactory, ILogger<PEAccessDecider> logger)
        : base(applicationAuthorizerDecisionStore, pilotCheckFactory, logger)
    {
    }

    protected override async Task<(bool isInPilot, bool shouldPersist)> DetermineAccessDecision(
        AccessDecisionContainer accessContainer)
    {
        var pilotDecisionTask = await base.DetermineAccessDecision(accessContainer);

        if (pilotDecisionTask.isInPilot)
        {
            return pilotDecisionTask;
        }

        return (false, false);
    }
}
