using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.RocketChoice;

public class RCQuickEditAccessDecider : LeaderPilotAccessDecider
{
    protected override bool ShouldPersist => false;
    public override string AccessDecisionId => ApplicationId.RCQuickEdit;

    protected override async Task<(bool isInPilot, bool shouldPersist)> DetermineAccessDecision(AccessDecisionContainer accessContainer)
    {
        var pilotDecisionTask = await base.DetermineAccessDecision(accessContainer);
        if (!pilotDecisionTask.isInPilot)
        {
            return pilotDecisionTask;
        }

        var isLoanInRocketChoiceTask =
             accessContainer.StateContainer.Get<bool>(ExplicitStateProviderIds.IsLoanInRocketChoice);
        var isRcLockoutLoanTask = accessContainer.GetAccessDecision(ApplicationId.RCLockout);

        var isLoanInRocketChoice = await isLoanInRocketChoiceTask;
        var isRcLockoutLoan = await isRcLockoutLoanTask;

        return (isLoanInRocketChoice && isRcLockoutLoan, false);
    }


    public RCQuickEditAccessDecider(IApplicationAuthorizerDecisionStore decisionStore,
        PilotCheckFactory pilotCheckFactory, ILogger<RCQuickEditAccessDecider> logger)
        : base(decisionStore, pilotCheckFactory, logger)
    {

    }
}
