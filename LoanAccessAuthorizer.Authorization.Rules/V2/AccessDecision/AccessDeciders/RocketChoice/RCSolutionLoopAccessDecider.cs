using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.RocketChoice;

public class RCSolutionLoopAccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.RCSolutionLoop;
    private readonly IPilotCheck _pilotCheck;
    private readonly Type _exclusionDeciderType = typeof(RCSolutionLoopAccessDecider);
    
    public RCSolutionLoopAccessDecider(PilotCheckFactory pilotCheckFactory)
    {
        _pilotCheck = pilotCheckFactory(PilotCheckType.Leader, AccessDecisionId);
    }
    
    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var exclusionDecision = new ExclusionDecision(false);
        var isLoanInAmp = await container.StateContainer.Get<bool>(ExplicitStateProviderIds.IsLoanInAmp);
        if (!isLoanInAmp)
        {
            return new ExclusionDecision(false)
            {
                { ExclusionReason.NotInPilot, _exclusionDeciderType, "Not in amp" }
            };
        }

        var accessDecisionTask = GetAccessDecision(container);
        var isRcLockoutLoanTask = container.GetAccessDecision(ApplicationId.RCLockout);

        await Task.WhenAll(accessDecisionTask, isRcLockoutLoanTask);
        var decisions = new bool[] { accessDecisionTask.Result, isRcLockoutLoanTask.Result };
        if (decisions.Any(decision => !decision))
        {
            exclusionDecision = new ExclusionDecision(true)
            {
                { ExclusionReason.NotInPilot, _exclusionDeciderType, "Not in pilot" }
            };
        }
        
        return exclusionDecision;
    }
    
    private async Task<bool> GetAccessDecision(AccessDecisionContainer accessContainer)
    {
        var (accessDecision, _) = await _pilotCheck.IsInPilot(accessContainer);
        return accessDecision;
    }
}
