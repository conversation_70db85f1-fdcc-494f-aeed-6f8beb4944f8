using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class WaiveLowerAppDepositDecider : IExclusionDecider
{
    private readonly IPilotCheck _pilotCheck;

    public string AccessDecisionId => ApplicationId.WaiveLowerAppDeposit;

    public WaiveLowerAppDepositDecider(PilotCheckFactory pilotCheckFactory)
    {
        _pilotCheck = pilotCheckFactory(PilotCheckType.Leader, AccessDecisionId);
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var (isInPilot, hasLoandata) = await _pilotCheck.IsInPilot(container);

        var exclusion = new ExclusionDecision(hasLoandata);

        if (!isInPilot)
        {
            exclusion.Add(
                ExclusionReason.NotInPilot,
                GetType(),
                "Assigned loan officer not in pilot group."
            );
        }

        return exclusion;
    }
}
