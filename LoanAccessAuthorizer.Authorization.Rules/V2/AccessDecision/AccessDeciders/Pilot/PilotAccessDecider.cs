using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.Exceptions;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.Models;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot;

[Obsolete("Implement IExclusionDecider and use IPilotCheckFactory for all new pilots")]
public abstract class PilotAccessDecider : IAccessDecider
{
    protected virtual bool ShouldPersist => true;
    protected readonly IApplicationAuthorizerDecisionStore _decisionStore;
    protected readonly ILogger _logger;
    protected readonly PilotCheckFactory _pilotCheckFactory;
    private readonly Lazy<IPilotCheck> _pilotCheck;

    protected IPilotCheck PilotCheck => _pilotCheck.Value;

    protected PilotAccessDecider(
        IApplicationAuthorizerDecisionStore decisionStore,
        PilotCheckFactory pilotCheckFactory,
        ILogger logger
    )
    {
        if (decisionStore == null && ShouldPersist)
        {
            throw new AccessDeciderConfigurationException(
                this,
                "decisionStore cannot be null if ShouldPersist is true"
            );
        }

        _decisionStore = decisionStore;
        _logger = logger;
        _pilotCheckFactory = pilotCheckFactory;
        _pilotCheck = new Lazy<IPilotCheck>(CreatePilotCheck);
    }

    public abstract string AccessDecisionId { get; }

    public virtual async Task<bool> GetAccessDecision(AccessDecisionContainer accessContainer)
    {
        if (!ShouldPersist)
        {
            return (await DetermineAccessDecision(accessContainer)).isInPilot;
        }

        var initialLoanData = await accessContainer.StateContainer.Get<InitialLoanState>();

        var storedDecision = await _decisionStore.GetDecision(
            initialLoanData.LoanNumber,
            AccessDecisionId
        );

        if (storedDecision.HasValue)
        {
            return storedDecision.Value;
        }

        var (isInPilot, shouldPersistDecision) = await DetermineAccessDecision(accessContainer);

        if (shouldPersistDecision)
        {
            _logger.LogInformation(
                "{App} Pilot decision {decision} persisted",
                AccessDecisionId,
                isInPilot
            );
            await _decisionStore.StoreDecision(
                initialLoanData.LoanNumber,
                AccessDecisionId,
                isInPilot
            );
        }

        return isInPilot;
    }

    protected virtual async Task<(bool isInPilot, bool shouldPersist)> DetermineAccessDecision(
        AccessDecisionContainer container
    )
    {
        var loanOfficerCommonId = container.StateContainer.Get<UserId>(
            ExplicitStateProviderIds.LoanOfficerCommonId
        );
        var pilotDeterminantDate = GetLoanDateForPilotComparison(container);

        await Task.WhenAll(loanOfficerCommonId, pilotDeterminantDate);

        return await PilotCheck.IsInPilot(
            container,
            loanOfficerCommonId.Result,
            pilotDeterminantDate.Result
        );
    }

    public virtual Task<DateTime?> GetLoanDateForPilotComparison(AccessDecisionContainer container)
    {
        return container.StateContainer.Get<DateTime?>(
            ExplicitStateProviderIds.ApplicationOrInitialContactDate
        );
    }

    protected virtual IPilotCheck CreatePilotCheck() =>
        _pilotCheckFactory(PilotCheckType.Normal, AccessDecisionId);
}
