using System.Collections.Concurrent;
using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using Microsoft.Extensions.Options;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot.PilotConfigProvider;

public sealed class ConfigManagerPilotConfigProvider : IPilotConfigProvider, IDisposable
{
    private readonly ConcurrentDictionary<string, IEnumerable<PilotConfiguration>> _pilots;
    private IDisposable _optionsOnChange;

    public ConfigManagerPilotConfigProvider(IOptionsMonitor<Dictionary<string, PilotConfiguration>> pilotConfigurations,
        IEnumerable<string> appNames)
    {
        var pilots = appNames.Select(appName =>
        {
            var pilotConfigs = pilotConfigurations.Get(appName);
            return new KeyValuePair<string, IEnumerable<PilotConfiguration>>(appName, pilotConfigs.Values);
        });
        _pilots = new ConcurrentDictionary<string, IEnumerable<PilotConfiguration>>(pilots);

        _optionsOnChange = pilotConfigurations.OnChange(OnPilotsChanged);
    }

    public void Dispose()
    {
        if (_optionsOnChange is not null)
        {
            _optionsOnChange.Dispose();
            _optionsOnChange = null;
        }
    }

    public IEnumerable<PilotConfiguration> GetPilots(string appName)
    {
        if (_pilots.TryGetValue(appName, out var pilots))
        {
            return pilots;
        }

        return Enumerable.Empty<PilotConfiguration>();
    }

    private void OnPilotsChanged(Dictionary<string, PilotConfiguration> pilotConfigs, string appName)
    {
        _pilots.AddOrUpdate(appName, pilotConfigs.Values, (_, _) => pilotConfigs.Values);
    }
}
