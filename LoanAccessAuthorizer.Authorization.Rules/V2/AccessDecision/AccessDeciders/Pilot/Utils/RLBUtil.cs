﻿using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Exceptions;
using LoanAccessAuthorizer.Domain.Exceptions;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot.Utils;

public static class RLBUtil
{
    public static async Task<DateTime?> GetLoanDateForPilotComparisonWithDefault(AccessDecisionContainer container, DateTime? defaultLoanDate, ILogger logger)
    {
        if (defaultLoanDate == null)
        {
            defaultLoanDate = (DateTime?)DateTime.UtcNow;
        }

        try
        {
            var initialContactDate = await container.StateContainer.Get<DateTime?>(ExplicitStateProviderIds.ApplicationOrInitialContactDate);
            return initialContactDate ?? defaultLoanDate;
        }
        catch (Exception e) when (e is RequestNotFoundException || e is LoanDoesNotExistInAmpException)
        {
            logger?.LogWarning("Loan is not in AMP. Defaulting application or initial contact date");
            return defaultLoanDate;
        }
        catch (Exception e)
        {
            logger?.LogError(e, "Failed to get application or initial contact date. Defaulting to minimum date value");
            return DateTime.MinValue;
        }
    }
}
