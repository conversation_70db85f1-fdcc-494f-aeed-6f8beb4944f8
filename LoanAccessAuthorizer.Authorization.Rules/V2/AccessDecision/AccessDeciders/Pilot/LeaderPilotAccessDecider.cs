using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot;

[Obsolete("Implement IExclusionDecider and use IPilotCheckFactory for all new pilots")]
public abstract class LeaderPilotAccessDecider : PilotAccessDecider
{
    protected LeaderPilotAccessDecider(IApplicationAuthorizerDecisionStore decisionStore,
        PilotCheckFactory pilotCheckFactory, ILogger logger)
        : base(decisionStore, pilotCheckFactory, logger)
    {
    }

    protected override IPilotCheck CreatePilotCheck() => _pilotCheckFactory(PilotCheckType.Leader, AccessDecisionId);
}
