using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Domain;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;
using LoanAccessAuthorizer.Domain.Utilities;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class CQCreditReportDetailsAccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.CQCreditReportDetails;
    private DateTime ProdStartDate = new DateTime(2022, 11, 10);
    private DateTime LowersStartDate = new DateTime(2022, 11, 02);
    private readonly DateTime _startDate;

    public CQCreditReportDetailsAccessDecider(EnvironmentInfo env)
    {
        _startDate = EnvironmentInfoUtil.GetEnvironmentName(env) switch
        {
            EnvironmentName.Prod => ProdStartDate,
            _ => LowersStartDate
        };
    }

    public async Task<ExclusionDecision> GetExclusionDecision(
        AccessDecisionContainer accessContainer
    )
    {
        var exclusion = new ExclusionDecision(false);

        var isCqLoanTask = accessContainer.GetAccessDecision(ApplicationId.Credit);
        var applicationDateTask = accessContainer.StateContainer.Get<DateTime?>(
            ExplicitStateProviderIds.ApplicationOrInitialContactDate
        );

        var isCqLoan = await isCqLoanTask;
        var applicationDate = await applicationDateTask;

        var decision = isCqLoan && applicationDate > _startDate;
        if (!decision)
        {
            exclusion.Add(ExclusionReason.NotInPilot, GetType(), "User not in pilot");
        }

        return exclusion;
    }
}
