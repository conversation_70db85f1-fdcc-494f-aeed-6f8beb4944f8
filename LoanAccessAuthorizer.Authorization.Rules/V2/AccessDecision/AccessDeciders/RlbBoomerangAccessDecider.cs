using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot.Utils;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Domain.Models;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

    public class RlbBoomerangAccessDecider : IExclusionDecider
    {
        private readonly IPilotCheck _pilotCheck;
        public string AccessDecisionId => ApplicationId.RlbBoomerang;
        private readonly ILogger<RlbBoomerangAccessDecider> _logger;

        public RlbBoomerangAccessDecider(PilotCheckFactory pilotCheckFactory, ILogger<RlbBoomerangAccessDecider> logger)
        {
            _logger = logger;
            _pilotCheck = pilotCheckFactory(PilotCheckType.Leader, AccessDecisionId);
        }

        public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
        {
            var initialContactDateTask = RLBUtil.GetLoanDateForPilotComparisonWithDefault(
                container,
                DateTime.UtcNow,
                _logger
            );
            var loanOfficerCommonIdTask = container.StateContainer.Get<UserId>(
                ExplicitStateProviderIds.LoanOfficerCommonId
            );

            await Task.WhenAll(initialContactDateTask, loanOfficerCommonIdTask);

            var (isInPilot, hasLoanData) = await _pilotCheck.IsInPilot(
                container,
                loanOfficerCommonIdTask.Result,
                initialContactDateTask.Result
            );

            var exclusion = new ExclusionDecision(hasLoanData);

            if (!isInPilot)
            {
                exclusion.Add(ExclusionReason.NotInPilot, GetType(), "Assigned loan officer not in pilot group.");
            }

            return exclusion;
        }
    }
