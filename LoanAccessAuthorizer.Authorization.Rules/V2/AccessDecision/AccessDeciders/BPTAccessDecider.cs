using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

class BPTAccessDecider : LeaderPilotAccessDecider
{
    protected override bool ShouldPersist => true;
    public override string AccessDecisionId => ApplicationId.BankerPresentationTool;

    public BPTAccessDecider(IApplicationAuthorizerDecisionStore applicationAuthorizerDecisionStore,
        PilotCheckFactory pilotCheckFactory, ILogger<BPTAccessDecider> logger)
        : base(applicationAuthorizerDecisionStore, pilotCheckFactory, logger)
    {
    }

    protected override async Task<(bool isInPilot, bool shouldPersist)> DetermineAccessDecision(
        AccessDecisionContainer accessContainer)
    {
        var pilotDecisionTask = await base.DetermineAccessDecision(accessContainer);

        if (pilotDecisionTask.isInPilot)
        {
            return pilotDecisionTask;
        }

        return (false, false);
    }
}
