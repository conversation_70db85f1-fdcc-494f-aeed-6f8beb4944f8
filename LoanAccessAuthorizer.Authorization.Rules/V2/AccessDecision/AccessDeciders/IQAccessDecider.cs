using LoanAccessAuthorizer.Authorization.Rules.Configuration;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class IQAccessDecider : IExclusionDecider, IClearDecision
{
    private const string AssetsAsIncomeFeatureName = "AssetsAsIncome";

    private readonly IQOptions _options;
    private readonly ILogger<IQAccessDecider> _logger;

    public IQAccessDecider(IOptionsSnapshot<IQOptions> options, ILogger<IQAccessDecider> logger)
    {
        _options = options.Value;
        _logger = logger;
    }

    public string AccessDecisionId => ApplicationId.Income;

    public Task<bool> CanClearDecision(AccessDecisionContainer accessContainer) => Task.FromResult(false);

    private async Task<IQDecisionDetails> CalculateNewExclusionDecision(AccessDecisionContainer container)
    {
        var state = container.StateContainer;

        // All loans are always in IQ pilot. We just need to decide if AssetsAsIncome is enabled for the loan.
        var details = new IQDecisionDetails { AssetsAsIncomeEnabledOn = _options.AssetsAsIncomeEnabledOn };

        if (details.AssetsAsIncomeEnabledOn is null)
        {
            // The feature itself is not configured, so no loan can have it.
            return details;
        }

        details.IsLoanInAmp = await state.Get<bool>(ExplicitStateProviderIds.IsLoanInAmp);
        if (details.IsLoanInAmp == false)
        {
            // Consider loans not in AMP to be new, so AssetsAsIncome should be enabled.
            details.Features.Add(AssetsAsIncomeFeatureName);
            return details;
        }

        details.LoanPilotDate = await state.Get<DateTime?>(ExplicitStateProviderIds.AmpInitialContactDate);
        if (details.LoanPilotDate is null)
        {
            // Consider AMP loans without a pilot date as new, so AssetsAsIncome should be enabled.
            details.Features.Add(AssetsAsIncomeFeatureName);
            return details;
        }

        if (details.LoanPilotDate >= details.AssetsAsIncomeEnabledOn)
        {
            // Existing loans in AMP with pilot date on or after our configured date should have the feature enabled.
            details.Features.Add(AssetsAsIncomeFeatureName);
            return details;
        }

        // Otherwise, old existing loans do not have the AssetsAsIncome feature enabled.
        return details;
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var details = await CalculateNewExclusionDecision(container);
        _logger.LogInformation(
            "Decision determined: {AccessDecisionId} {@DecisionDetails}",
            AccessDecisionId,
            details
        );

        return new ExclusionDecision(persist: true, features: details.Features);
    }
}
