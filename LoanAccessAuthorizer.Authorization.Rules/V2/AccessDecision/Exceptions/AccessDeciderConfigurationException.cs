﻿namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.Exceptions;

public class AccessDeciderConfigurationException : Exception
{
    public AccessDeciderConfigurationException(IAccessDecider decider, string additionalMessage = "") : base(BuildErrorMessage(decider, additionalMessage))
    {
    }

    private static string BuildErrorMessage(IAccessDecider decider, string additionalMessage)
    {
        return
            $"Configuration Error, AccessDecider: {decider.AccessDecisionId}, type: {decider.GetType().FullName}" 
            + (additionalMessage == null ? "" : $", message {additionalMessage}");
    }
}
