namespace LoanAccessAuthorizer.Authorization.Rules.V2.State;

public static class ExplicitStateProviderIds
{
    public const string ApplicationOrInitialContactDate = "applicationOrInitialContactDate";
    public const string IsLoanInAmp = "isLoanInAmp";
    public const string LoanOfficerCommonId = "loanOfficerCommonId";
    public const string LoanChannel = "loanChannel";
    public const string QualificationGroups = "qualificationGroups";
    public const string IsLoanArchivedInAmp = "isLoanArchivedInAmp";
    public const string IsLoanInRocketChoice = "isLoanInRocketChoice";
    public const string IsProductOnLoan = "isProductOnLoan";
    public const string IsLoanOriginatedByRLB = "isLoanOriginatedByRLB";
    public const string HasSubordinateFinancing = "hasSubordinateFinancing";
    public const string SellerAddresses = "sellerAddresses";
    public const string IsRlbReadOnly = "isRlbReadOnly";
    public const string IsRLPSaveToAmpComplete = "isRLPSaveToAmpComplete";
    public const string AmpApplicationNumber = "AmpApplicationNumber";
    public const string IsLoanCopiedInAmp = "isLoanCopiedInAmp";
    public const string IsTeamMemberClockedIn = "isTeamMemberClockedIn";
    public const string AmpInitialContactDate = "AmpInitialContactDate";
    public const string IsTpoLoan = "isTpoLoan";
    public const string IsSchwabLoan = "IsSchwabLoan";
    public const string IsSchwabClient = "IsSchwabClient";
    public const string IsRmaLoan = "IsRmaLoan";
    public const string IsHelocAmeripriseLoan = "IsHelocAmeripriseLoan";
    public const string IsFranchiseLoan = "IsFranchiseLoan";
    public const string IsFocTitleCompany = "IsFocTitleCompany";
}
