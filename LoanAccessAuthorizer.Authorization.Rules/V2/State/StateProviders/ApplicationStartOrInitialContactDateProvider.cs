using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class ApplicationStartOrInitialContactDateProvider
    : ExplicitlyIdentifiedStateProvider<DateTime?>
{
    private readonly ILogger<ApplicationStartOrInitialContactDateProvider> _logger;

    public override string Identifier => ExplicitStateProviderIds.ApplicationOrInitialContactDate;

    public ApplicationStartOrInitialContactDateProvider(
        ILogger<ApplicationStartOrInitialContactDateProvider> logger
    )
    {
        _logger = logger;
    }

    protected override async Task<DateTime?> GetTypedState(StateContainer stateContainer)
    {
        var rlLoanDetails = await stateContainer.Get<RocketLogicLoanDetails>();

        if (rlLoanDetails?.CreatedDateTime != null)
        {
            return rlLoanDetails.CreatedDateTime.Value.DateTime;
        }

        var initialContactDate = await stateContainer.Get<DateTime?>(ExplicitStateProviderIds.AmpInitialContactDate);

        if (initialContactDate != null)
        {
            return Convert.ToDateTime(initialContactDate);
        }

        _logger.LogWarning("No initial contact date present");
        return null;
    }
}
