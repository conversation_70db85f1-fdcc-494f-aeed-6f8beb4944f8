#nullable enable

using DecisionServices.Core.Cache;
using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Authorization.Rules.Features;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.LoanData;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class AmpApplicationNumberProvider : ExplicitlyIdentifiedStateProvider<string?>,
    IInvalidatableState
{
    private readonly ICache _cache;
    private readonly ILoanDataClient _loanDataClient;
    private readonly IFeatureEnabledConfigProvider _featureFlagConfigProvider;
    private readonly ILogger<AmpApplicationNumberProvider> _logger;

    internal static readonly TimeSpan _loanInAmpTtl = TimeSpan.FromHours(12);
    internal static readonly TimeSpan _loanNotInAmpTtl = TimeSpan.FromSeconds(5);

    internal const string _loanNotInAmpMarker = "NotInAmp";

    public string FriendlyIdentifier { get; } = "applicationNumber";
    public override string Identifier { get; } = ExplicitStateProviderIds.AmpApplicationNumber;

    public AmpApplicationNumberProvider(ICache cache, ILoanDataClient loanDataClient,
        IFeatureEnabledConfigProvider featureFlagConfigProvider,
        ILogger<AmpApplicationNumberProvider> logger)
    {
        _cache = cache;
        _loanDataClient = loanDataClient;
        _featureFlagConfigProvider = featureFlagConfigProvider;
        _logger = logger;
    }

    protected override async Task<string?> GetTypedState(StateContainer stateContainer)
    {
        var initialState = await stateContainer.Get<InitialLoanState>();

        var cacheKey = BuildCacheKey(initialState.LoanNumber);
        var applicationNumber = await _cache.Get(cacheKey);
        if (!string.IsNullOrEmpty(applicationNumber))
        {
            if (applicationNumber == _loanNotInAmpMarker)
            {
                return null;
            }
            return applicationNumber;
        }

        applicationNumber = await _loanDataClient.GetApplicationNumber(initialState.LoanNumber);

        var isLoanInAmp = !string.IsNullOrEmpty(applicationNumber);
        var ttl = isLoanInAmp ? _loanInAmpTtl : _loanNotInAmpTtl;
        var cachedValue = isLoanInAmp ?
            applicationNumber :
            _loanNotInAmpMarker;

        try
        {
            await _cache.Set(cacheKey, cachedValue, ttl);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error caching application number for {loanIdentifier}",
                initialState.LoanNumber);
        }

        return applicationNumber;
    }

    public Task ClearCache(string cacheId)
    {
        var cacheKey = BuildCacheKey(cacheId);
        return _cache.Delete(cacheKey);
    }

    private CacheKey BuildCacheKey(string loanIdentifier) => new(loanIdentifier, FriendlyIdentifier);
}
