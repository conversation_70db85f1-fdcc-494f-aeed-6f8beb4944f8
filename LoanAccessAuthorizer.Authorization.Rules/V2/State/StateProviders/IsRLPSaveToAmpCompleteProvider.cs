using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.LoanData;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class IsRLPSaveToAmpCompleteProvider : ExplicitlyIdentifiedStateProvider<bool>
{
    private readonly ILoanDataClient _loanDataClient;

    public override string Identifier => ExplicitStateProviderIds.IsRLPSaveToAmpComplete;

    public IsRLPSaveToAmpCompleteProvider(ILoanDataClient loanDataClient)
    {
        _loanDataClient = loanDataClient;
    }

    protected override async Task<bool> GetTypedState(StateContainer stateContainer)
    {
        var initialState = await stateContainer.Get<InitialLoanState>();
        var importControl = await IsLoanInAmpUtil.ExecuteIfLoanInAmp
            (stateContainer, () => _loanDataClient.GetImportControl(initialState.LoanNumber, "RC"));
        return !importControl?.ProcessRunning ?? false;
    }
}
