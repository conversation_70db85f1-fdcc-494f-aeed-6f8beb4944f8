using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.MODS;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class IsLoanInRocketChoiceProvider : ExplicitlyIdentifiedStateProvider<bool>
{
    private readonly IModsServiceClient _client;
    public override string Identifier => ExplicitStateProviderIds.IsLoanInRocketChoice;
    
    public IsLoanInRocketChoiceProvider(IModsServiceClient client)
    {
        _client = client;
    }
    protected override async Task<bool> GetTypedState(StateContainer stateContainer)
    {
        var initialState = await stateContainer.Get<InitialLoanState>();
        return await _client.GetMortgageOriginationDataRecordExists(initialState.LoanNumber);
    }
}