using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Domain.LoanData;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class HasSubordinateFinancingProvider : ExplicitlyIdentifiedStateProvider<bool>
{
    public override string Identifier => ExplicitStateProviderIds.HasSubordinateFinancing;

    private readonly ILoanDataClient _loanDataClient;

    public HasSubordinateFinancingProvider(ILoanDataClient loanDataClient)
    {
        _loanDataClient = loanDataClient;
    }
    protected override async Task<bool> GetTypedState(StateContainer stateContainer)
    {
        var initialState = await stateContainer.Get<InitialLoanState>();
        var isLoanInAmp = await stateContainer.Get<bool>(ExplicitStateProviderIds.IsLoanInAmp);
        if (!isLoanInAmp) return false;
        
        var subordinateLiens = await _loanDataClient.GetSubordinateLiens(initialState.LoanNumber);
        return subordinateLiens?.Any() ?? false;
    }
}