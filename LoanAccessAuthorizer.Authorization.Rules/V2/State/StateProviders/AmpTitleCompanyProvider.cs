using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.LoanData;
using LoanAccessAuthorizer.Domain.LoanData.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class AmpTitleCompanyProvider : AutoIdentifiedStateProvider<AmpTitleCompany>
{
    private readonly ILoanDataClient _loanDataClient;

    public AmpTitleCompanyProvider(ILoanDataClient loanDataClient)
    {
        _loanDataClient = loanDataClient;
    }

    protected override async Task<AmpTitleCompany> GetTypedState(StateContainer stateContainer)
    {
        var initialState = await stateContainer.Get<InitialLoanState>();
        return await _loanDataClient.GetTitleCompany(initialState.LoanNumber);
    }
}
