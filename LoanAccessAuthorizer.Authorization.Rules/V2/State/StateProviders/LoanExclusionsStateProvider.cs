﻿using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Exclusions;
using LoanAccessAuthorizer.Exclusions.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class LoanExclusionsStateProvider : AutoIdentifiedStateProvider<ExclusionInformation>
{
    private readonly IExclusionsRepository _exclusionsRepository;

    public LoanExclusionsStateProvider(IExclusionsRepository exclusionsRepository)
    {
        _exclusionsRepository = exclusionsRepository;
    }

    protected override async Task<ExclusionInformation> GetTypedState(StateContainer stateContainer)
    {
        var initialState = await stateContainer.Get<InitialLoanState>();
        return await _exclusionsRepository.GetExclusions(initialState.LoanNumber);
    }
}
