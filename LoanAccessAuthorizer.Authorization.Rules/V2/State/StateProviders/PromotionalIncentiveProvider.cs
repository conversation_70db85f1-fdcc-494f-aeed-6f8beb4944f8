using DecisionServices.Core.Cache;
using DecisionServices.Core.StateProvider;
using LoanAccessAuthorizer.Domain.Configuration;
using LoanAccessAuthorizer.Domain.LoanData;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using Microsoft.Extensions.Options;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class PromotionalIncentiveProvider : LoanCachedAutoStateProvider<AmpPromotionalIncentive>
{
    private readonly ILoanDataClient _loanDataClient;


    public override string FriendlyIdentifier => "PromotionalIncentive";
    private static readonly TimeSpan _ttl = TimeSpan.FromMinutes(5);


    public PromotionalIncentiveProvider(ICache cache, CachePolicyFactory cachePolicyFactory,
        ILoanDataClient loanDataClient,
        IOptionsSnapshot<GlobalCacheLockingConfiguration> config)
        : base(cache, CreateCachePolicy(cachePolicyFactory, config.Value, _ttl))
    {
        _loanDataClient = loanDataClient;
    }

    protected override async Task<AmpPromotionalIncentive> FetchValue(StateContainer stateContainer, string loanNumber)
    {
        return await IsLoanInAmpUtil.ExecuteIfLoanInAmpOrThrow(stateContainer,
            () => _loanDataClient.GetPromotionalIncentive(loanNumber));
    }
}
