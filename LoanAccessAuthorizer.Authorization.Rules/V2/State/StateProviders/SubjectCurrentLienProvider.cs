﻿using DecisionServices.Core.Cache;
using DecisionServices.Core.StateProvider;
using LoanAccessAuthorizer.Domain.Configuration;
using LoanAccessAuthorizer.Domain.LoanData;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using Microsoft.Extensions.Options;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class SubjectCurrentLienProvider : LoanCachedAutoStateProvider<SubjectCurrentLien>
{
    private readonly ILoanDataClient _loanDataClient;

    public override string FriendlyIdentifier => "SubjectCurrentLien";
    private static readonly TimeSpan _ttl = TimeSpan.FromHours(6);

    public SubjectCurrentLienProvider(ICache cache, CachePolicyFactory cachePolicyFactory,
        ILoanDataClient loanDataClient, IOptionsSnapshot<GlobalCacheLockingConfiguration> config)
        : base(cache, CreateCachePolicy(cachePolicyFactory, config.Value, _ttl))
    {
        _loanDataClient = loanDataClient;
    }

    protected override async Task<SubjectCurrentLien> FetchValue(StateContainer stateContainer, string loanNumber)
    {
        return await _loanDataClient.GetSubjectCurrentLien(loanNumber);
    }
}
