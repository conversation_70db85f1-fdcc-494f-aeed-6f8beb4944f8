using DecisionServices.Core.Cache;
using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.Models;
using LoanAccessAuthorizer.Domain.TimeTrackingCheck;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class IsClockedInStateProvider : ExplicitlyIdentifiedStateProvider<bool>, IInvalidatableState
{
    private readonly ICache _cache;
    private readonly CachePolicy<bool?> _cachePolicy;
    private readonly ITimeTrackingCheckClient _timeTrackingCheckClient;
    public string FriendlyIdentifier => "isTeamMemberClockedIn";
    public override string Identifier => ExplicitStateProviderIds.IsTeamMemberClockedIn;

    public IsClockedInStateProvider(
        CachePolicyFactory cachePolicyFactory,
        ITimeTrackingCheckClient timeTrackingCheckClient,
        ICache cache)
    {
        _timeTrackingCheckClient = timeTrackingCheckClient;
        _cachePolicy = cachePolicyFactory.CreatePolicy<bool?>(TimeSpan.FromHours(1));
        _cache = cache;
    }

    protected override async Task<bool> GetTypedState(StateContainer stateContainer)
    {
        var initialState = await stateContainer.Get<InitialLoanState>();
        var id = initialState.CallerId;
        if (id.Type != UserIdType.CommonId)
        {
            return true;
        }
        var key = BuildCacheKey(id.Id);
        var isClockedIn = await _cachePolicy.Execute(() => IsTeamMemberClockedIn(id.Id), key,
            isClockedIn => isClockedIn is true);
        // If the call fails, we still let them in, but we don't cache the result
        return isClockedIn ?? true;
    }

    private async Task<bool?> IsTeamMemberClockedIn(string commonId)
    {
        try
        {
            var response = await _timeTrackingCheckClient.GetTeamMemberClockedInStatus(commonId);
            var isClockedIn = response.EventType.Equals(EventType.ClockIn);
            return isClockedIn;
        }
        catch
        {
            return null;
        }
    }

    public Task ClearCache(string cacheId)
    {
        var cacheKey = BuildCacheKey(cacheId);
        return _cache.Delete(cacheKey);
    }

    private CacheKey BuildCacheKey(string commonId) => new(commonId, FriendlyIdentifier);
}
