#nullable enable

using System.Collections.ObjectModel;
using AmpJobRunner.Models.Response;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;

public class AmpJobsCollection : Collection<JobDetails>
{
    public AmpJobsCollection()
        : this(new List<JobDetails>())
    {
    }

    public AmpJobsCollection(IList<JobDetails> jobs)
        : base(jobs)
    {
    }
}
