#nullable enable

using System.Collections;
using DocEntityRelationship.Shared.Model;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;

public class DocumentEntityStructuredRelationships : IEnumerable<StructuredRelationship>
{
    private readonly ILookup<string, StructuredRelationship> _documentRelationships;

    public DocumentEntityStructuredRelationships(IEnumerable<StructuredRelationship> relationships)
    {
        _documentRelationships = relationships.ToLookup(relationship => relationship.DocumentId);
    }

    public IEnumerable<Relationship> GetDocumentRelationships(string documentId) =>
        _documentRelationships[documentId];

    public IEnumerable<Relationship> GetDocumentsRelationships(IEnumerable<string> documentIds)
    {
        return documentIds.Select(GetDocumentRelationships)
            .SelectMany(relationship => relationship);
    }

    public IEnumerator<StructuredRelationship> GetEnumerator() =>
        _documentRelationships.SelectMany(group => group).GetEnumerator();
    IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();
}
