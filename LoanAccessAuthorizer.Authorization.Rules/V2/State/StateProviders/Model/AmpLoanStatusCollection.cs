using System.Collections.ObjectModel;
using LoanAccessAuthorizer.Domain.LoanData.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;

public class AmpLoanStatusCollection : Collection<AmpLoanStatus>
{
    public AmpLoanStatusCollection(IEnumerable<AmpLoanStatus> collection)
        : base(collection?.ToList() ?? new List<AmpLoanStatus>())
    { }

    public AmpLoanStatusCollection() : base([]) { }
}
