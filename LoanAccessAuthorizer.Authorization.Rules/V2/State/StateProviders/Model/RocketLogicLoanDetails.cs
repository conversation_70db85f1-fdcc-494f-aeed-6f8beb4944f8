using LoanAccessAuthorizer.RocketLogicApi.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;

public class RocketLogicLoanDetails
{
    public DateTimeOffset? CreatedDateTime { get; set; }
    public string CreatedByAppId { get; set; }
    public bool IsRocketLogicApiLoan { get; set; }
    public LoanPurpose? LoanPurpose { get; set; }
    public string RocketLogicLoanId { get; set; }
}
