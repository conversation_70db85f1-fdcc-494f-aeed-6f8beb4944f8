using System.Collections;
using Ivc.DocumentStorage.Models.V1.Responses;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;

public class RocketLogicDocuments : IEnumerable<GetDocumentMetadataResponse>
{
    private readonly ILookup<string, GetDocumentMetadataResponse> _documents;

    public RocketLogicDocuments(IEnumerable<GetDocumentMetadataResponse> documents)
    {
        _documents = documents.ToLookup(GetDocumentType);
    }

    public IEnumerable<GetDocumentMetadataResponse> GetDocumentsOfType(string docType)
        => _documents[docType];

    public IEnumerator<GetDocumentMetadataResponse> GetEnumerator()
        => _documents.SelectMany(group => group).GetEnumerator();

    IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();

    private static string GetDocumentType(GetDocumentMetadataResponse doc)
    {
        return doc.DocumentType?.Overall?.Value ?? string.Empty;
    }
}
