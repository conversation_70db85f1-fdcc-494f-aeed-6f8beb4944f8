using System.Collections.ObjectModel;
using LoanAccessAuthorizer.Domain.LoanData.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;

public class OwnedPropertiesCollection : Collection<OwnedProperty>
{
    public OwnedPropertiesCollection(IEnumerable<OwnedProperty> collection)
        : base(collection?.ToList() ?? new List<OwnedProperty>())
    { }

    public OwnedPropertiesCollection() : base([]) { }
}
