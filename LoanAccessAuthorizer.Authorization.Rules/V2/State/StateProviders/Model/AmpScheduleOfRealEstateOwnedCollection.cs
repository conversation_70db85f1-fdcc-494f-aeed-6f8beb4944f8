using LoanAccessAuthorizer.Domain.LoanData.Models;
using System.Collections.ObjectModel;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;

public class AmpScheduleOfRealEstateOwnedCollection : Collection<ScheduleOfRealEstateOwned>
{
    public AmpScheduleOfRealEstateOwnedCollection(IEnumerable<ScheduleOfRealEstateOwned> collection)
        : base(collection?.ToList() ?? new List<ScheduleOfRealEstateOwned>()) { }

    public AmpScheduleOfRealEstateOwnedCollection() : base([]) { }
}
