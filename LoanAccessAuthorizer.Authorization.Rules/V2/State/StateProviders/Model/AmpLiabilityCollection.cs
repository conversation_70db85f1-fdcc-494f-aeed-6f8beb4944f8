using System.Collections.ObjectModel;
using LoanAccessAuthorizer.Domain.LoanData.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;

public class AmpLiabilityCollection : Collection<AmpLiability>
{
    public AmpLiabilityCollection(IEnumerable<AmpLiability> collection)
        : base(collection?.ToList() ?? new List<AmpLiability>()) { }
    public AmpLiabilityCollection() : base([]) { }
}
