namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;

public class RocketLogicUnderwritingVisitedTrailSet : HashSet<string>
{
    public RocketLogicUnderwritingVisitedTrailSet(IEnumerable<string> collection)
        : base(collection ?? Enumerable.Empty<string>(), StringComparer.InvariantCultureIgnoreCase)
    { }

    public RocketLogicUnderwritingVisitedTrailSet(): base([]) { }

    public bool UnderwriteFinished { get; set; }
}
