using DecisionServices.Core.Cache;
using DecisionServices.Core.StateProvider;
using InRule.Repository;
using LoanAccessAuthorizer.Domain.Configuration;
using LoanAccessAuthorizer.Domain.LoanData;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using Microsoft.Extensions.Options;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class AmpUnderwritingSystemInfoProvider : LoanCachedAutoStateProvider<AmpUnderwritingSystemInfo>
{
    private readonly ILoanDataClient _loanDataClient;
    public override string FriendlyIdentifier => "AmpUnderwritingSystemInfo";

    public AmpUnderwritingSystemInfoProvider(ICache cache, CachePolicyFactory cachePolicyFactory,
        ILoanDataClient loanDataClient,
        IOptionsSnapshot<GlobalCacheLockingConfiguration> config)
        : base(cache, CreateCachePolicy(cachePolicyFactory, config.Value, null))
    {
        _loanDataClient = loanDataClient;
    }

    protected override async Task<AmpUnderwritingSystemInfo> FetchValue(StateContainer stateContainer, string loanNumber)
    {
        return await IsLoanInAmpUtil.ExecuteIfLoanInAmpOrThrow(stateContainer,
            () => _loanDataClient.GetUnderwritingInformation(loanNumber));
    }
}
