using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Domain.Product.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class IsProductOnLoanProvider : ExplicitlyIdentifiedStateProvider<bool>
{
    public override string Identifier => ExplicitStateProviderIds.IsProductOnLoan;
    protected override async Task<bool> GetTypedState(StateContainer stateContainer)
    {
        var productInfo = await stateContainer.Get<ProductInfo>();
        return productInfo.ProductParameters?.Any() == true 
               || productInfo.ProductSettings?.Any() == true;
    }
}