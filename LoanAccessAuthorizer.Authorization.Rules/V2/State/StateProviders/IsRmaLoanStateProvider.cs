using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Domain.LoanData.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

internal class IsRmaLoanStateProvider : ExplicitlyIdentifiedStateProvider<bool?>
{
    public override string Identifier => ExplicitStateProviderIds.IsRmaLoan;
    private static readonly HashSet<string> RmaLeadTypeCodes =
    [
        "RLHAP",
        "RMAMEXHAP",
        "RMAMICAHAP",
        "RMCDMHAP",
        "RMKARMAHAP",
        "RMCOBHA<PERSON>",
        "RMEX<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
        "RMTUR<PERSON>HA<PERSON>",
        "RMMSHA<PERSON>",
        "RMREA<PERSON><PERSON><PERSON><PERSON>",
        "RMST<PERSON><PERSON><PERSON>",
        "RMS<PERSON><PERSON><PERSON>",
        "RMSFHA<PERSON>",
        "RMTRUEBILLHAP",
        "BNKREFERBL<PERSON>",
        "BN<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON><PERSON>",
        "<PERSON>ADH<PERSON><PERSON><PERSON>",
        "<PERSON>AD<PERSON><PERSON><PERSON>HA<PERSON>",
        "FR<PERSON>",
        "OHRMAH<PERSON><PERSON><PERSON>"
    ];

    protected override async Task<bool?> GetTypedState(StateContainer stateContainer)
    {
        var leadDetails = await stateContainer.Get<RocketLogicBankingLeadDetails>();
        if (leadDetails == null)
        {
            return null;
        }

        return leadDetails.LeadSourceSystem == LeadSourceSystem.RocketMortgage
            && RmaLeadTypeCodes.Contains(leadDetails.LeadTypeCode);
    }
}
