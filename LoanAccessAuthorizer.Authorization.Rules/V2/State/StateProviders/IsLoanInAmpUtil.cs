using DecisionServices.Core.StateProvider;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Exceptions;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public static class IsLoanInAmpUtil
{
    public static async Task<T> ExecuteIfLoanInAmpOrThrow<T>(StateContainer container, Func<Task<T>> func)
    {
        var isLoanInAmp = await container.Get<bool>(ExplicitStateProviderIds.IsLoanInAmp);

        if (isLoanInAmp)
        {
            return await func();
        }

        var isArchivedLoan = await container.Get<bool>(ExplicitStateProviderIds.IsLoanArchivedInAmp);
        
        throw new LoanDoesNotExistInAmpException("Loan not found in AMP", isArchivedLoan);
    }

    public static Func<Task<T>> WrapExecuteIfLoanInAmpOrThrow<T>(StateContainer container, Func<Task<T>> func)
    {
        return () => ExecuteIfLoanInAmpOrThrow(container, func);
    }

    public static async Task<T> ExecuteIfLoanInAmp<T>(StateContainer container, Func<Task<T>> func)
    {
        var isLoanInAmp = await container.Get<bool>(ExplicitStateProviderIds.IsLoanInAmp);

        if (isLoanInAmp)
        {
            return await func();
        }

        return default;
    }
}
