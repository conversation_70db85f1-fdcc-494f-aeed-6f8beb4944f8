using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Domain.LoanData.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class IsFocTitleCompanyProvider : ExplicitlyIdentifiedStateProvider<bool>
{
    public override string Identifier => ExplicitStateProviderIds.IsFocTitleCompany;

    protected override async Task<bool> GetTypedState(StateContainer stateContainer)
    {
        var keyLoanInfoTask = stateContainer.Get<AmpKeyLoanInfo>();
        var ampTitleCompanyTask = stateContainer.Get<AmpTitleCompany>();

        var keyLoanInfo = await keyLoanInfoTask;
        var ampTitleCompany = await ampTitleCompanyTask;

        if (string.Equals("Refinance", keyLoanInfo.LoanPurpose, StringComparison.CurrentCultureIgnoreCase))
        {
            return string.Equals(ampTitleCompany.ClientChosenTitle, "No", StringComparison.CurrentCultureIgnoreCase);
        }

        return ampTitleCompany.FOCTitle;
    }
}
