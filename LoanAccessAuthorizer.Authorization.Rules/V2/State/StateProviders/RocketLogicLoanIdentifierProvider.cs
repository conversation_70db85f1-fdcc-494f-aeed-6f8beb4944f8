using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.RocketLogicApi;
using LoanAccessAuthorizer.RocketLogicApi.Models;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class RocketLogicLoanIdentifierProvider : AutoIdentifiedStateProvider<RocketLogicLoanIdentifier>
{
    private readonly IRocketLogicApiClient _client;
    private readonly ILogger<RocketLogicLoanIdentifierProvider> _logger;

    public RocketLogicLoanIdentifierProvider(
        IRocketLogicApiClient client,
        ILogger<RocketLogicLoanIdentifierProvider> logger)
    {
        _client = client;
        _logger = logger;
    }

    protected override async Task<RocketLogicLoanIdentifier> GetTypedState(StateContainer stateContainer)
    {
        var initialState = await stateContainer.Get<InitialLoanState>();
        try
        {
            return await _client.TranslateLoanNumber(initialState.LoanNumber);
        }
        catch (Exception e)
        {
            _logger.LogInformation("Failed to translate loan number to rocket logic loan ID. Error Message: {ErrorMessage}", e.Message);
            return new RocketLogicLoanIdentifier
            {
                Identifier = null
            };
        }
    }
}
