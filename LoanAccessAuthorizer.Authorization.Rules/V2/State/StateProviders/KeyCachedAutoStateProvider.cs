using DecisionServices.Core.Cache;
using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Domain.Configuration;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public abstract class KeyCachedAutoStateProvider<TKey, TValue> : CachedAutoStateProvider<TValue>
    where TValue : class
{
    protected KeyCachedAutoStateProvider(ICache cache, CachePolicy<TValue> cachePolicy)
        : base(cache, cachePolicy)
    {
    }

    protected override async Task<string> GetCacheId(StateContainer stateContainer)
    {
        return (await <PERSON><PERSON>ey(stateContainer)).ToString();
    }

    protected abstract Task<TKey> GetKey(StateContainer stateContainer);

    protected static CachePolicy<TValue> CreateCachePolicy(
        CachePolicyFactory cachePolicyFactory,
        bool isEnabled,
        TimeSpan? ttl,
        int? lockExpirationMilliseconds,
        int? retryMilliseconds,
        int? waitMilliseconds,
        int? lockNotAcquiredRetries,
        int defaultLockExpiration = 1000,
        int defaultRetryTime = 10,
        int defaultWaitTime = 500,
        int defaultLockNotAcquiredRetries = 5)
    {
        if (isEnabled)
        {
            var lockExpiration = TimeSpan.FromMilliseconds(lockExpirationMilliseconds ?? defaultLockExpiration);
            var retryTime = TimeSpan.FromMilliseconds(retryMilliseconds ?? defaultRetryTime);
            var waitTime = TimeSpan.FromMilliseconds(waitMilliseconds ?? defaultWaitTime);
            var notAcquiredRetries = lockNotAcquiredRetries ?? defaultLockNotAcquiredRetries;

            return cachePolicyFactory.CreatePolicy<TValue>(lockExpiration,
                retryTime, waitTime, ttl, notAcquiredRetries);
        }

        return cachePolicyFactory.CreatePolicy<TValue>(ttl);
    }

    protected static CachePolicy<TValue> CreateCachePolicy(
        CachePolicyFactory cachePolicyFactory,
        GlobalCacheLockingConfiguration config,
        TimeSpan? ttl,
        int defaultLockExpiration = 1000,
        int defaultRetryTime = 10,
        int defaultWaitTime = 500,
        int defaultLockNotAcquiredRetries = 5)
    {
        return CreateCachePolicy(
            cachePolicyFactory,
            config.IsEnabled,
            ttl,
            config.LockExpirationMilliseconds,
            config.RetryMilliseconds,
            config.WaitMilliseconds,
            config.LockNotAcquiredRetries,
            defaultLockExpiration,
            defaultRetryTime,
            defaultWaitTime,
            defaultLockNotAcquiredRetries);
    }
}
