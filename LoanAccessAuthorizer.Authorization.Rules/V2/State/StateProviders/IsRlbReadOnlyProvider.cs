using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Exclusions.Models;
using LoanAccessAuthorizer.RocketLogicBanking;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class IsRlbReadOnlyProvider : ExplicitlyIdentifiedStateProvider<bool?>
{
    private readonly RLBankingProvider _rlBankingProvider;

    public override string Identifier => ExplicitStateProviderIds.IsRlbReadOnly;

    public IsRlbReadOnlyProvider(RLBankingProvider rlBankingProvider)
    {
        _rlBankingProvider = rlBankingProvider;
    }

    protected override async Task<bool?> GetTypedState(StateContainer stateContainer)
    {
        var initialLoanState = await stateContainer.Get<InitialLoanState>();

        var isReadOnlyTask = _rlBankingProvider.IsReadOnly(initialLoanState.LoanNumber);
        var exclusionInfoTask = stateContainer.Get<ExclusionInformation>();

        var isReadOnly = await isReadOnlyTask;
        if (isReadOnly is null)
        {
            return null;
        }

        var exclusionInfo = await exclusionInfoTask;
        return isReadOnly.Value || exclusionInfo.HasExclusions(ApplicationId.RlbBackHalfAmpLockout);
    }
}
