﻿using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.MODS;
using LoanAccessAuthorizer.MODS.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class ModsOriginationDataProvider : AutoIdentifiedStateProvider<ModsResponse>
{
    private readonly IModsServiceClient _client;

    public ModsOriginationDataProvider(IModsServiceClient client)
    {
        _client = client;
    }

    protected override async Task<ModsResponse> GetTypedState(StateContainer stateContainer)
    {
        var initialState = await stateContainer.Get<InitialLoanState>();
        var originationData = await _client.GetMortgageOriginationData(initialState.LoanNumber);

        return originationData;
    }
}
