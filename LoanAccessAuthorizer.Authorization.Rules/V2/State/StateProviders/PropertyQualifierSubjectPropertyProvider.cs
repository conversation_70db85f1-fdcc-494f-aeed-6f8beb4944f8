using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.Exceptions;
using LoanAccessAuthorizer.PropertyQualifier;
using Property.Domain.Entities.Properties;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class PropertyQualifierSubjectPropertyProvider : AutoIdentifiedStateProvider<SubjectProperty>
{
    private readonly PropertyQualifierServiceClient _propertyQualifierServiceClient;

    public PropertyQualifierSubjectPropertyProvider(PropertyQualifierServiceClient propertyQualifierServiceClient)
    {
        _propertyQualifierServiceClient = propertyQualifierServiceClient;
    }

    protected override async Task<SubjectProperty> GetTypedState(StateContainer stateContainer)
    {
        var initialLoanState = await stateContainer.Get<InitialLoanState>();
        try
        {
            return await _propertyQualifierServiceClient.GetSubjectProperty(initialLoanState.LoanNumber);
        }
        catch (RequestNotFoundException)
        {
            return null;
        }
    }
}
