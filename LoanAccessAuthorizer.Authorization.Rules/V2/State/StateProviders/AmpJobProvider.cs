#nullable enable

using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.AmpJobRunner;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.Exceptions;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class AmpJobProvider : AutoIdentifiedStateProvider<AmpJobsCollection>
{
    private readonly AmpJobRunnerServiceClient _jobRunnerClient;

    public AmpJobProvider(AmpJobRunnerServiceClient jobRunnerClient)
    {
        _jobRunnerClient = jobRunnerClient;
    }

    protected override async Task<AmpJobsCollection> GetTypedState(StateContainer stateContainer)
    {
        var initialState = await stateContainer.Get<InitialLoanState>();

        try
        {
            var jobs = await _jobRunnerClient.GetJobs(initialState.LoanNumber);

            return new AmpJobsCollection(jobs.ToList());
        }
        catch(RequestNotFoundException)
        {
            return new AmpJobsCollection();
        }
    }
}
