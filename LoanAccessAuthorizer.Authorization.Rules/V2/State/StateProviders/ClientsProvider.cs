using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.RocketLogicApi;
using LoanAccessAuthorizer.RocketLogicApi.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class ClientsProvider : AutoIdentifiedStateProvider<IEnumerable<RlApiClient>>
{
    private readonly IRocketLogicApiClient _client;

    public ClientsProvider(IRocketLogicApiClient client)
    {
        _client = client;
    }

    protected override async Task<IEnumerable<RlApiClient>> GetTypedState(StateContainer stateContainer)
    {
        var rocketLogicLoanId = await stateContainer.Get<RocketLogicLoanIdentifier>();
        if (rocketLogicLoanId is { Identifier: null })
        {
            return [];
        }

        var clients = await _client.GetClients(rocketLogicLoanId.Identifier);

        return clients;
    }
}
