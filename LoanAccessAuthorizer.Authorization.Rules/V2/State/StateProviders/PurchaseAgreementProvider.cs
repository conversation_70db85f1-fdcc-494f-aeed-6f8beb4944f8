using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.ParOrchestrator;
using ParOrch.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class PurchaseAgreementProvider : AutoIdentifiedStateProvider<CalculatedParData>
{
    private readonly ParOrchestratorClient _parOrchestratorClient;

    public PurchaseAgreementProvider(ParOrchestratorClient parOrchestratorClient)
    {
        _parOrchestratorClient = parOrchestratorClient;
    }

    protected override async Task<CalculatedParData> GetTypedState(StateContainer stateContainer)
    {
        var initialState = await stateContainer.Get<InitialLoanState>();
        return await _parOrchestratorClient.GetPurchaseAgreement(initialState.LoanNumber);
    }
}
