using DecisionServices.Core.Cache;
using DecisionServices.Core.StateProvider;
using InRule.Repository;
using LoanAccessAuthorizer.Domain.Configuration;
using LoanAccessAuthorizer.Domain.LoanData;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using Microsoft.Extensions.Options;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class OpenAmpStudentLoanCashoutProvider : LoanCachedAutoStateProvider<AmpStudentLoanCashout>
{
    private readonly ILoanDataClient _loanDataClient;
    public override string FriendlyIdentifier => "OpenAmpStudentLoanCashout";
    private static readonly TimeSpan _ttl = TimeSpan.FromMinutes(5);

    public OpenAmpStudentLoanCashoutProvider(ICache cache, CachePolicyFactory cachePolicyFactory,
               ILoanDataClient loanDataClient,
               IOptionsSnapshot<GlobalCacheLockingConfiguration> config)
        : base(cache, CreateCachePolicy(cachePolicyFactory, config.Value, _ttl))
    {
        _loanDataClient = loanDataClient;
    }

    protected override async Task<AmpStudentLoanCashout> FetchValue(StateContainer stateContainer, string loanNumber)
    {
        var ampLoanDetails = await stateContainer.Get<AmpLoanDetails>();
        return string.Equals(ampLoanDetails?.LoanPurpose, "Refinance", StringComparison.CurrentCultureIgnoreCase)
            ? await _loanDataClient.GetStudentLoanCashout(loanNumber) : new AmpStudentLoanCashout
            {
                IsStudentLoanCashout = false
            };
    }
}
