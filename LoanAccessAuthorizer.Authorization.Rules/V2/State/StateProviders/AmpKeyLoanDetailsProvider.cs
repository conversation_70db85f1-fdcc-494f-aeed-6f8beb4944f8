using DecisionServices.Core.Cache;
using DecisionServices.Core.StateProvider;
using LoanAccessAuthorizer.Domain.Configuration;
using LoanAccessAuthorizer.Domain.LoanData;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using Microsoft.Extensions.Options;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class AmpKeyLoanDetailsProvider : LoanCachedAutoStateProvider<AmpKeyLoanInfo>
{
    private readonly ILoanDataClient _loanDataClient;
    public override string FriendlyIdentifier => "LoanDetails";
    private static readonly TimeSpan _ttl = TimeSpan.FromMinutes(5);

    public AmpKeyLoanDetailsProvider(ICache cache, CachePolicyFactory cachePolicyFactory,
        ILoanDataClient loanDataClient,
        IOptionsSnapshot<GlobalCacheLockingConfiguration> config)
        : base(cache, CreateCachePolicy(cachePolicyFactory, config.Value, _ttl))
    {
        _loanDataClient = loanDataClient;
    }

    protected override bool ShouldCache(AmpKeyLoanInfo loanDetails) =>
        !string.IsNullOrWhiteSpace(loanDetails.ProductCode);

    protected override async Task<AmpKeyLoanInfo> FetchValue(StateContainer stateContainer, string loanNumber)

    {
        return await IsLoanInAmpUtil.ExecuteIfLoanInAmpOrThrow(stateContainer,
            () => _loanDataClient.GetKeyLoanInfo(loanNumber));
    }
}
