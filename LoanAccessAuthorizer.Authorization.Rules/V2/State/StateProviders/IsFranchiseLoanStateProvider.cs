using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Domain.LoanData.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class IsFranchiseLoanStateProvider : ExplicitlyIdentifiedStateProvider<bool>
{
    public override string Identifier => ExplicitStateProviderIds.IsFranchiseLoan;

    protected override async Task<bool> GetTypedState(StateContainer stateContainer)
    {
        var ampLoanDetailsTask = stateContainer.Get<AmpLoanDetails>();
        var ampPromotionalIncentive = await stateContainer.Get<AmpPromotionalIncentive>();
        var ampLoanDetails = await ampLoanDetailsTask;
        return ampLoanDetails?.LeadTypeCode?.StartsWith("FLO", StringComparison.OrdinalIgnoreCase) == true ||
               ampPromotionalIncentive?.ResponseToAdvertising?.StartsWith("Franchise Banking", StringComparison.OrdinalIgnoreCase) == true;
    }
}
