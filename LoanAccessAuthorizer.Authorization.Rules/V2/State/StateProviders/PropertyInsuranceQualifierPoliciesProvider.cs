using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.PropertyInsuranceQualifier;
using LoanAccessAuthorizer.PropertyInsuranceQualifier.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class PropertyInsuranceQualifierPoliciesProvider : AutoIdentifiedStateProvider<InsurancePoliciesResponse>
{
    private readonly PropertyInsuranceQualifierServiceClient _serviceClient;

    public PropertyInsuranceQualifierPoliciesProvider(PropertyInsuranceQualifierServiceClient serviceClient)
    {
        _serviceClient = serviceClient;
    }

    protected override async Task<InsurancePoliciesResponse> GetTypedState(StateContainer stateContainer)
    {
        var initialState = await stateContainer.Get<InitialLoanState>();

        return await _serviceClient.GetInsurancePolicies(initialState.LoanNumber);
    }
}
