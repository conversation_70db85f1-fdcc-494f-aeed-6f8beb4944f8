#nullable enable

using DecisionServices.Core.Cache;
using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain;
using LoanAccessAuthorizer.Domain.LoanData;
using LoanAccessAuthorizer.Domain.Utilities;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class IsLoanCopiedInAmpProvider : ExplicitlyIdentifiedStateProvider<bool?>, IInvalidatableState
{
    private readonly ICache _cache;
    private readonly ILoanDataClient _loanDataClient;
    private readonly EnvironmentInfo _environmentInfo;
    private readonly ILogger<IsLoanCopiedInAmpProvider> _logger;

    internal static readonly TimeSpan _loanInAmpTtl = TimeSpan.FromHours(12);

    public string FriendlyIdentifier { get; } = "IsLoanCopiedInAmp";

    public override string Identifier { get; } = ExplicitStateProviderIds.IsLoanCopiedInAmp;

    public IsLoanCopiedInAmpProvider(
        ICache cache,
        ILoanDataClient loanDataClient,
        EnvironmentInfo environmentInfo,
        ILogger<IsLoanCopiedInAmpProvider> logger
    )
    {
        _cache = cache;
        _loanDataClient = loanDataClient;
        _environmentInfo = environmentInfo;
        _logger = logger;
    }

    protected override async Task<bool?> GetTypedState(StateContainer stateContainer)
    {
        // Loans never are copied to prod, so don't bother checking.
        // Also, checking could be expensive, and so not suitable for prod.
        bool? isCopied;
        if (_environmentInfo.GetEnvironmentName() == Domain.EnvironmentName.Prod)
        {
            isCopied = false;
            return isCopied;
        }

        // Check cache for a not-null value before doing other work.
        var initialState = await stateContainer.Get<InitialLoanState>();
        var cacheKey = BuildCacheKey(initialState.LoanNumber);
        var cachedValue = await _cache.Get(cacheKey);
        if (!string.IsNullOrWhiteSpace(cachedValue))
        {
            return bool.TryParse(cachedValue, out var result) && result;
        }

        // If we can't get the App Number, the loan isn't in amp yet, so we don't know enough if when it is created if
        // it will be by copying or by creating new.
        var appNumber = await stateContainer.Get<string?>(ExplicitStateProviderIds.AmpApplicationNumber);
        if (string.IsNullOrWhiteSpace(appNumber))
        {
            return null;
        }

        isCopied = await _loanDataClient.GetLoanEditExists(appNumber, "Loan Copied");
        await CacheValue(cacheKey, isCopied, initialState.LoanNumber);
        return isCopied;
    }

    private async Task CacheValue(CacheKey cacheKey, bool? isCopied, string loanNumber)
    {
        if (isCopied is null)
        {
            return;
        }

        var cachedValue = isCopied.ToString();
        try
        {
            await _cache.Set(cacheKey, cachedValue, _loanInAmpTtl);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error caching isLoanCopiedInAmp for {loanIdentifier}", loanNumber);
        }
    }

    public Task ClearCache(string cacheId)
    {
        var cacheKey = BuildCacheKey(cacheId);
        return _cache.Delete(cacheKey);
    }

    private CacheKey BuildCacheKey(string loanIdentifier) => new(loanIdentifier, FriendlyIdentifier);
}
