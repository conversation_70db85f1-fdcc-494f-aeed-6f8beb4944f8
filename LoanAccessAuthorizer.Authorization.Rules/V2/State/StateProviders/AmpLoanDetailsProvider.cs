using DecisionServices.Core.Cache;
using DecisionServices.Core.StateProvider;
using LoanAccessAuthorizer.Authorization.Rules.Configuration;
using LoanAccessAuthorizer.Authorization.Rules.Features;
using LoanAccessAuthorizer.Domain.Configuration;
using LoanAccessAuthorizer.Domain.LoanData;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using Microsoft.Extensions.Options;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class AmpLoanDetailsProvider : LoanCachedAutoStateProvider<AmpLoanDetails>
{
    private readonly ILoanDataClient _loanDataClient;
    private static readonly TimeSpan _ttl = TimeSpan.FromMinutes(1);

    public override string FriendlyIdentifier => "AmpLoanDetails";

    public AmpLoanDetailsProvider(ICache cache, CachePolicyFactory cachePolicyFactory,
        ILoanDataClient loanDataClient,
        IOptionsSnapshot<GlobalCacheLockingConfiguration> globalConfig,
        IOptionsSnapshot<AmpLoanDetailsProviderConfig> ampConfig)
        : base(cache, CreateCachePolicy(cachePolicyFactory, globalConfig.Value, ampConfig.Value, _ttl))
    {
        _loanDataClient = loanDataClient;
    }

    protected static CachePolicy<AmpLoanDetails> CreateCachePolicy(
        CachePolicyFactory cachePolicyFactory,
        GlobalCacheLockingConfiguration globalConfig,
        AmpLoanDetailsProviderConfig ampConfig,
        TimeSpan? ttl = null)
    {
        if (globalConfig.IsEnabled)
        {
            return CreateCachePolicy(
                cachePolicyFactory,
                globalConfig,
                ttl);
        }
        else
        {
            // If global cache locking is not enabled, use the Amp-specific configuration
            return CreateCachePolicy(
                cachePolicyFactory,
                ampConfig.EnableCacheLocking,
                ttl,
                ampConfig.LockExpirationMilliseconds,
                ampConfig.RetryMilliseconds,
                ampConfig.WaitMilliseconds,
                ampConfig.LockNotAcquiredRetries);
        }
    }

    protected override Task<AmpLoanDetails> FetchValue(StateContainer stateContainer, string loanNumber) =>
        IsLoanInAmpUtil.ExecuteIfLoanInAmp(stateContainer, () => _loanDataClient.GetLoanDetails(loanNumber));
}
