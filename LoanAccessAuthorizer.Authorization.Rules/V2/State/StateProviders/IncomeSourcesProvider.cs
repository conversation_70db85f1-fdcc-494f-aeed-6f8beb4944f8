﻿using LoanAccessAuthorizer.IncomeQualifier.Models;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.IncomeQualifier;
using DecisionServices.Core.StateProvider.StateProviders;
using DecisionServices.Core.StateProvider;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class IncomeSourcesProvider : AutoIdentifiedStateProvider<IncomeSourcesWrapper>
{
    private readonly IncomeQualifierServiceClient _iqServiceClient;

    public IncomeSourcesProvider(IncomeQualifierServiceClient iqServiceClient)
    {
        _iqServiceClient = iqServiceClient;
    }

    protected override async Task<IncomeSourcesWrapper> GetTypedState(StateContainer stateContainer)
    {
        var initialState = await stateContainer.Get<InitialLoanState>();

        return await _iqServiceClient.GetIncomeSources(initialState.LoanNumber);
    }
}
