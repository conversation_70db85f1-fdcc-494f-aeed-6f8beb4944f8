using DecisionServices.Core.Cache;
using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.LoanData;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Linq;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class IsLoanArchivedProvider : ExplicitlyIdentifiedStateProvider<bool>
{
    private readonly CachePolicy<(bool shouldCache, bool isArchived)> _cachePolicy;
    private readonly IArchivedLoanDataClient _archivedLoanDataClient;
    private readonly IConfiguration _configuration;
    private readonly ILogger<IsLoanArchivedProvider> _logger;
    private readonly bool _simulateArchivedLoans;
    private readonly IEnumerable<string> _simulatedArchivedLoanNumbers;

    public override string Identifier => ExplicitStateProviderIds.IsLoanArchivedInAmp;

    public IsLoanArchivedProvider(
        CachePolicyFactory cachePolicyFactory, 
        IArchivedLoanDataClient loanDataClient,
        IConfiguration configuration,
        ILogger<IsLoanArchivedProvider> logger)
    {
        _cachePolicy = cachePolicyFactory.CreatePolicy<(bool shouldCache, bool result)>(TimeSpan.FromDays(30));
        _archivedLoanDataClient = loanDataClient;
        _configuration = configuration;
        _logger = logger;
        
        // Get configuration for simulated archived loans
        _simulateArchivedLoans = _configuration.GetValue<bool>("TestFeatures:SimulateArchivedLoans", false);
        _simulatedArchivedLoanNumbers = _configuration.GetSection("TestFeatures:SimulatedArchivedLoanNumbers").Get<IEnumerable<string>>() ?? Array.Empty<string>();
        
        if (_simulateArchivedLoans && _simulatedArchivedLoanNumbers.Any())
        {
            _logger.LogInformation("Archived loan simulation enabled for loans: {LoanNumbers}", 
                string.Join(", ", _simulatedArchivedLoanNumbers));
        }
    }

    protected override async Task<bool> GetTypedState(StateContainer stateContainer)
    {
        var initialState = await stateContainer.Get<InitialLoanState>();
        var loanNumber = initialState.LoanNumber;
        var cacheKey = new CacheKey(loanNumber, Identifier);
        
        return (await _cachePolicy.Execute(() => IsLoanArchivedInAmp(loanNumber), cacheKey, 
                resultTuple => resultTuple.shouldCache ))
            .isArchived;
    }

    private async Task<(bool shouldCache, bool isArchived)> IsLoanArchivedInAmp(string loanNumber)
    {
        // Check if this loan should be simulated as archived based on configuration
        if (_simulateArchivedLoans && _simulatedArchivedLoanNumbers?.Contains(loanNumber) == true)
        {
            _logger.LogInformation("Simulating archived loan for loan number: {LoanNumber}", loanNumber);
            return (true, true);
        }
        
        try
        {
            var archivedAppNumber = await _archivedLoanDataClient.GetArchivedApplicationNumber(loanNumber);
            return (true, !string.IsNullOrEmpty(archivedAppNumber));
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error checking if loan {LoanNumber} is archived", loanNumber);
            return (false, false);
        }
    }
}
