using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using LoanAccessAuthorizer.Domain.LoanData.Models.Contacts;
using LoanAccessAuthorizer.Domain.Models;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class LoanOfficerCommonIdProvider : ExplicitlyIdentifiedStateProvider<UserId>
{
    private readonly ILogger<LoanOfficerCommonIdProvider> _logger;
    public override string Identifier => ExplicitStateProviderIds.LoanOfficerCommonId;

    public LoanOfficerCommonIdProvider(ILogger<LoanOfficerCommonIdProvider> logger)
    {
        _logger = logger;
    }

    protected override async Task<UserId> GetTypedState(StateContainer stateContainer)
    {
        var loanOfficerCommonId = await GetLoanOfficerCommonId(stateContainer);

        if (loanOfficerCommonId == null)
        {
            _logger.LogWarning("No loan officer has been assigned");
        }

        return UserId.FromCommonId(loanOfficerCommonId);
    }

    private async Task<string> GetLoanOfficerCommonId(StateContainer stateContainer)
    {
        string loanOfficerCommonId = null;

        var isInAmp = await stateContainer.Get<bool>(ExplicitStateProviderIds.IsLoanInAmp);
        if (isInAmp)
        {
            loanOfficerCommonId = await GetBankerCommonIdFromAmp(stateContainer);
        }

        if (loanOfficerCommonId is null)
        {
            var rlbLead = await stateContainer.Get<RocketLogicBankingLeadDetails>();
            loanOfficerCommonId = rlbLead?.AssignedToCommonId;
        }

        return loanOfficerCommonId;
    }

    private async Task<string> GetBankerCommonIdFromAmp(StateContainer stateContainer)
    {
        try
        {
            var contacts = await stateContainer.Get<LoanContacts>();
            return contacts.MortgageBanker?.CommonId ?? contacts.BankerOfRecord?.CommonId;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Erorr retrieving loan officer from Amp");
            return null;
        }
    }
}
