#nullable enable

using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.LoanData;
using LoanAccessAuthorizer.Domain.LoanData.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class AmpSellerAddressesProvider : ExplicitlyIdentifiedStateProvider<IEnumerable<AmpAddress>>
{
    private readonly ILoanDataClient _loanDataClient;

    internal const string SellerAddressType = "Seller";

    public override string Identifier => ExplicitStateProviderIds.SellerAddresses;

    public AmpSellerAddressesProvider(ILoanDataClient loanDataClient)
    {
        _loanDataClient = loanDataClient;
    }

    protected override async Task<IEnumerable<AmpAddress>> GetTypedState(StateContainer stateContainer)
    {
        var initialState = await stateContainer.Get<InitialLoanState>();
        return await _loanDataClient.GetAddresses(initialState.LoanNumber, SellerAddressType);
    }
}
