using DecisionServices.Core.Cache;
using DecisionServices.Core.StateProvider;
using LoanAccessAuthorizer.Domain.Configuration;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using LoanAccessAuthorizer.RocketLogicBanking;
using LoanAccessAuthorizer.RocketLogicBanking.Models;
using Microsoft.Extensions.Options;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class RocketLogicLeadDetailsProvider
    : LoanCachedAutoStateProvider<RocketLogicBankingLeadDetails>
{
    private readonly RLBankingProvider _rlBankingProvider;
    public override string FriendlyIdentifier => "RocketLogicLeadDetails";

    public RocketLogicLeadDetailsProvider(
        ICache cache,
        CachePolicyFactory cachePolicyFactory,
        RLBankingProvider rlBankingProvider,
        IOptionsSnapshot<GlobalCacheLockingConfiguration> config)
        : base(cache, CreateCachePolicy(cachePolicyFactory, config.Value, null))
    {
        _rlBankingProvider = rlBankingProvider;
    }

    protected override bool ShouldCache(RocketLogicBankingLeadDetails leadDetails) =>
        leadDetails?.InitialLoanOriginationSystem == InitialLoanOriginationSystem.LOLA;

    protected override async Task<RocketLogicBankingLeadDetails> FetchValue(
        StateContainer stateContainer,
        string loanNumber
    )
    {
        var rlbLead = await _rlBankingProvider.GetLead(loanNumber);

        if (rlbLead == null)
        {
            return null;
        }

        return new RocketLogicBankingLeadDetails
        {
            LoanNumber = loanNumber,
            IsUnsupportedLeadType = rlbLead.IsUnsupportedLeadType,
            InitialLoanOriginationSystem = rlbLead.IsReadOnly
                ? InitialLoanOriginationSystem.LOLA
                : InitialLoanOriginationSystem.RocketLogic,
            LeadSourceSystem = ConvertLeadSourceSystem(rlbLead.LeadSourceSystem),
            AssignedToCommonId = rlbLead.AssignedToCommonId,
            ApplicationStatus = rlbLead.LoanStatus,
            LoanPurpose = rlbLead.LoanPurpose,
            IsTeamMemberLoan = rlbLead.IsTeamMemberLoan,
            RlbCompleted = rlbLead.RlbCompleted,
            LeadSourceCategory = rlbLead.LeadSourceCategory,
            IsSchwabOverride = rlbLead.IsSchwabOverride,
            IsAmeripriseLead = rlbLead.IsAmeripriseLead,
            IsRelocation = rlbLead.IsRelocationLead,
            IsAssumption = rlbLead.IsAssumptionLead,
            LeadTypeCode = rlbLead.LeadTypeCode
        };
    }

    private LeadSourceSystem ConvertLeadSourceSystem(
        RLBankingServiceLeadSourceSystem? rlbLeadSourceSystem
    )
    {
        switch (rlbLeadSourceSystem)
        {
            case RLBankingServiceLeadSourceSystem.RocketMortgage:
                return LeadSourceSystem.RocketMortgage;
            case RLBankingServiceLeadSourceSystem.RocketPro:
                return LeadSourceSystem.RocketPro;
            case RLBankingServiceLeadSourceSystem.RocketProTpo:
                return LeadSourceSystem.RocketProTpo;
            default:
                return LeadSourceSystem.Other;
        }
    }
}
