using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.LoanData;
using LoanAccessAuthorizer.Domain.LoanData.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class AmpLoanPurchaseDetailProvider : AutoIdentifiedStateProvider<AmpLoanPurchaseDetail>
{
    private readonly ILoanDataClient _loanDataClient;

    public AmpLoanPurchaseDetailProvider(ILoanDataClient loanDataClient)
    {
        _loanDataClient = loanDataClient;
    }

    protected override async Task<AmpLoanPurchaseDetail> GetTypedState(StateContainer stateContainer)
    {
        var initialState = await stateContainer.Get<InitialLoanState>();
        return await _loanDataClient.GetLoanPurchaseDetail(initialState.LoanNumber);
    }
}
