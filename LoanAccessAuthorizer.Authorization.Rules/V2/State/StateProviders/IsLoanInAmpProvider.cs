#nullable enable

using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class IsLoanInAmpProvider : ExplicitlyIdentifiedStateProvider<bool>
{
    public override string Identifier => ExplicitStateProviderIds.IsLoanInAmp;

    protected override async Task<bool> GetTypedState(StateContainer stateContainer)
    {
        var applicationNumber = await stateContainer.Get<string>(ExplicitStateProviderIds.AmpApplicationNumber);
        return !string.IsNullOrEmpty(applicationNumber);
    }
}
