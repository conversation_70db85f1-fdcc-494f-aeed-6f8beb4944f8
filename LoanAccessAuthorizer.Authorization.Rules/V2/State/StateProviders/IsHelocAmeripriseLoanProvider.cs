using DecisionServices.Core.Cache;
using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.Exceptions;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using LoanAccessAuthorizer.RocketLogicApi;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public partial class IsHelocAmeripriseLoan : ExplicitlyIdentifiedStateProvider<bool>
{
    private readonly IRocketLogicApiClient _client;
    public override string Identifier => ExplicitStateProviderIds.IsHelocAmeripriseLoan;

    public IsHelocAmeripriseLoan(ICache cache, IRocketLogicApiClient client,
        CachePolicyFactory cachePolicyFactory)
    {
        _client = client;
    }

    protected override async Task<bool> GetTypedState(StateContainer stateContainer)
    {
        try
        {
            var leadDetails = await stateContainer.Get<RocketLogicBankingLeadDetails>();
            if (leadDetails?.IsAmeripriseLead ?? false)
            {
                var initialState = await stateContainer.Get<InitialLoanState>();
                var rlApiLoan = await _client.GetLoan(initialState.LoanNumber);
                return rlApiLoan?.HelocDetails is not null;
            }
            return false;
        }
        catch (RequestNotFoundException)
        {
            return false;
        }
    }
}
