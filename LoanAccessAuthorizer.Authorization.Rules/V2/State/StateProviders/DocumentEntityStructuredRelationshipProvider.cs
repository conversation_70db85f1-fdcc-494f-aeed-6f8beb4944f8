#nullable enable

using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using DocEntityRelationship.Shared.DataAccess;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class DocumentEntityStructuredRelationshipProvider : AutoIdentifiedStateProvider<DocumentEntityStructuredRelationships>
{
    private readonly IDocEntityRelationshipClient _dersClient;

    public DocumentEntityStructuredRelationshipProvider(IDocEntityRelationshipClient dersClient)
    {
        _dersClient = dersClient;
    }

    protected override async Task<DocumentEntityStructuredRelationships> GetTypedState(StateContainer stateContainer)
    {
        var initialState = await stateContainer.Get<InitialLoanState>();
        var relationships = await _dersClient.GetStructuredRelationships(initialState.LoanNumber);

        return new DocumentEntityStructuredRelationships(relationships);
    }
}
