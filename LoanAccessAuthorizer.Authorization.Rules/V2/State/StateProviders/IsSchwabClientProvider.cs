using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.Exceptions;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using LoanAccessAuthorizer.RocketLogicBanking;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class IsSchwabClientProvider : ExplicitlyIdentifiedStateProvider<bool>
{
    private readonly RLBankingProvider _rlBankingProvider;
    private readonly ILogger<IsSchwabClientProvider> _logger;

    public override string Identifier => ExplicitStateProviderIds.IsSchwabClient;

    public IsSchwabClientProvider(RLBankingProvider rlBankingProvider, ILogger<IsSchwabClientProvider> logger)
    {
        _rlBankingProvider = rlBankingProvider;
        _logger = logger;
    }

    protected override async Task<bool> GetTypedState(StateContainer stateContainer)
    {
        var leadDetails = await stateContainer.Get<RocketLogicBankingLeadDetails>();

        if (leadDetails == null)
        {
            return false;
        }

        try
        {
            var schwabClientDetails = await _rlBankingProvider.SchwabClientDetails(leadDetails.LoanNumber);
            return schwabClientDetails.Any(client => client.IsSchwabClient ?? false) && !leadDetails.IsSchwabOverride;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to retrieve Schwab Client Details for loan: {LoanNumber}", leadDetails.LoanNumber);
            return false;
        }
    }
}
