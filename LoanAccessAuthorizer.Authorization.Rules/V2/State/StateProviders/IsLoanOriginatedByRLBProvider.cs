﻿using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using System.Runtime.CompilerServices;

[assembly: InternalsVisibleTo("UnitTests.LoanAccessAuthorizer.Authorization.Rules.V2")]

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class IsLoanOriginatedByRLBProvider : ExplicitlyIdentifiedStateProvider<bool>
{
    internal const string RLBOriginatingSystem = "RLB";

    public override string Identifier => ExplicitStateProviderIds.IsLoanOriginatedByRLB;

    protected override async Task<bool> GetTypedState(StateContainer stateContainer)
    {
        var loanSource = await stateContainer.Get<AmpLoanSource>();
        return loanSource.OriginatingSystem == RLBOriginatingSystem;
    }
}
