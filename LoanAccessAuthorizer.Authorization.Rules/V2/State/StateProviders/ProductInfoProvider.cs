using DecisionServices.Core.Cache;
using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Domain.LoanData;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using LoanAccessAuthorizer.Domain.Product.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class ProductInfoProvider : CachedAutoStateProvider<ProductInfo>
{
    private readonly ILoanDataClient _loanDataClient;
    public override string FriendlyIdentifier => "ProductInfo";


    public ProductInfoProvider(ICache cache, CachePolicyFactory cachePolicyFactory,
        ILoanDataClient loanDataClient)
        : base(cache, cachePolicyFactory.CreatePolicy<ProductInfo>(TimeSpan.FromHours(6)))
    {
        _loanDataClient = loanDataClient;
    }

    protected override async Task<ProductInfo> GetTypedState(StateContainer stateContainer)
    {
        var loanDetails = await stateContainer.Get<AmpKeyLoanInfo>();
        if (string.IsNullOrEmpty(loanDetails.ProductCode))
        {
            return new ProductInfo
            {
                ProductParameters = new List<ProductParameter>(),
                ProductSettings = new List<ProductSetting>()
            };
        }

        return await base.GetTypedState(stateContainer);
    }

    protected override async Task<string> GetCacheId(StateContainer stateContainer)
    {
        var loanDetails = await stateContainer.Get<AmpKeyLoanInfo>();
        return loanDetails.ProductCode;
    }

    protected override async Task<ProductInfo> FetchValue(StateContainer stateContainer, string productCode)
    {
        return await _loanDataClient.GetProductInfo(productCode);
    }
}
