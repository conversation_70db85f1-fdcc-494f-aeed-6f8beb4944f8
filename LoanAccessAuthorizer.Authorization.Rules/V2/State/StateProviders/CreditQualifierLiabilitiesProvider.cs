using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.CreditQualifier;
using LoanAccessAuthorizer.CreditQualifier.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class CreditQualifierLiabilitiesProvider : AutoIdentifiedStateProvider<LiabilityCollection>
{
    private readonly CreditQualifierServiceClient _creditQualifierServiceClient;

    public CreditQualifierLiabilitiesProvider(CreditQualifierServiceClient creditQualifierServiceClient)
    {
        _creditQualifierServiceClient = creditQualifierServiceClient;
    }

    protected override async Task<LiabilityCollection> GetTypedState(StateContainer stateContainer)
    {
        var initialLoanState = await stateContainer.Get<InitialLoanState>();
        return await _creditQualifierServiceClient.GetLiabilities(initialLoanState.LoanNumber);
    }
}
