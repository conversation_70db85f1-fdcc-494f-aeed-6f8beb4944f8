using DecisionServices.Core.Cache;
using DecisionServices.Core.StateProvider;
using LoanAccessAuthorizer.Domain.Configuration;
using LoanAccessAuthorizer.Domain.LoanData;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using Microsoft.Extensions.Options;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class LoanSourceProvider : LoanCachedAutoStateProvider<AmpLoanSource>
{
    private readonly ILoanDataClient _loanDataClient;
    public override string FriendlyIdentifier => "LoanSource";

    public LoanSourceProvider(ICache cache, CachePolicyFactory cachePolicyFactory,
        ILoanDataClient loanDataClient,
        IOptionsSnapshot<GlobalCacheLockingConfiguration> config)
        : base(cache, CreateCachePolicy(cachePolicyFactory, config.Value, null))
    {
        _loanDataClient = loanDataClient;
    }

    protected override async Task<AmpLoanSource> FetchValue(StateContainer stateContainer, string loanNumber)
    {
        return await IsLoanInAmpUtil.ExecuteIfLoanInAmpOrThrow(stateContainer,
            () => _loanDataClient.GetLoanSource(loanNumber));
    }
}
