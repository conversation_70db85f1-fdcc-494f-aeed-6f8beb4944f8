using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using DecisionServices.QualificationGroupMapper.Factory;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using LoanAccessAuthorizer.Domain.Product.Models;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class QualificationGroupProvider : AutoIdentifiedStateProvider<QualificationGroupSet>
{
    private readonly ILogger<QualificationGroupProvider> _logger;
    private readonly QualificationFactory _qualificationFactory;

    public QualificationGroupProvider(QualificationFactory qualificationFactory, ILogger<QualificationGroupProvider> logger)
    {
        _logger = logger;
        _qualificationFactory = qualificationFactory;
    }

    protected override async Task<QualificationGroupSet> GetTypedState(StateContainer stateContainer)
    {
        var loanDetails = stateContainer.Get<AmpKeyLoanInfo>();
        var productInfo = stateContainer.Get<ProductInfo>();

        await Task.WhenAll(loanDetails, productInfo);
        var qualification = _qualificationFactory.Create(
            MapToQualificationLoanDetail(loanDetails.Result),
            MapToQualificationGroupProductSettings(productInfo.Result));
        
        var groups = new QualificationGroupSet(qualification.Groups.Select(g => g.ToString()));

        if (groups.Count == 0)
        {
            _logger.LogWarning("No qualification groups");
        }

        return groups;
    }

    private static QualificationGroupProductSettings MapToQualificationGroupProductSettings(ProductInfo productInfo)
    {
        var productSettings = productInfo.ProductSettings?.FirstOrDefault() ?? new ProductSetting();
        return new QualificationGroupProductSettings
        {
            LoanClass = productSettings.LoanClass,
            AgencyHighBalance = productSettings.AgencyHighBalance,
            FhaStreamline = productSettings.FhaStreamline,
            InterestOnlyType = productSettings.InterestOnlyType,
            Jumbo = productSettings.Jumbo,
            QmUnderwritingType = productSettings.QmUnderwritingType,
            RefiPlusProduct = productSettings.RefiPlusProduct,
            ProductParameters = MapToProductParameters(productInfo.ProductParameters)
        };
    }

    private static IDictionary<string, string> MapToProductParameters(IEnumerable<ProductParameter> productParameters)
    {
        var result = new Dictionary<string, string>();
        if(productParameters is null)
        {
            return result;
        }

        // Can't use ToDictionary() since product parameters will rarely have duplicate parameter names
        foreach(var productParameter in productParameters)
        {
            result.TryAdd(productParameter.ParameterName, productParameter.ParameterValue);
        }

        return result;
    }

    private static QualificationGroupLoanDetail MapToQualificationLoanDetail(AmpKeyLoanInfo details)
    {
        return new QualificationGroupLoanDetail
        {
            LoanPurpose = details.LoanPurpose,
            ProductCode = details.ProductCode,
            RefiPlus = details.RefiPlus.GetValueOrDefault(0),
            UnderwriterSource = details.UnderwriterSource.GetValueOrDefault(0)
        };
    }
}