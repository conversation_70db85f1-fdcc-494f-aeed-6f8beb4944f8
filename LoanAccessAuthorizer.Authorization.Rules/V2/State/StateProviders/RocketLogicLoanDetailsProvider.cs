using DecisionServices.Core.Cache;
using DecisionServices.Core.StateProvider;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.Configuration;
using LoanAccessAuthorizer.Domain.Exceptions;
using LoanAccessAuthorizer.RocketLogicApi;
using Microsoft.Extensions.Options;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public partial class RocketLogicLoanDetailsProvider : LoanCachedAutoStateProvider<RocketLogicLoanDetails>
{
    protected override bool ShouldCache(RocketLogicLoanDetails state) => state.LoanPurpose is not null;

    private static readonly ISet<string> ExcludedLoanCreatedBy =
        new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "207719", // Loan Purpose Service
            "206156", // RLB
            "206658", // RMO
            "210418"  // Mortgage Client Amp DataSync
        };
    private readonly IRocketLogicApiClient _client;

    public RocketLogicLoanDetailsProvider(ICache cache, IRocketLogicApiClient client,
        CachePolicyFactory cachePolicyFactory, IOptionsSnapshot<GlobalCacheLockingConfiguration> config)
        : base(cache, CreateCachePolicy(cachePolicyFactory, config.Value, null))
    {
        _client = client;
    }

    protected override async Task<RocketLogicLoanDetails> FetchValue(StateContainer stateContainer, string loanNumber)
    {
        try
        {
            var loan = await _client.GetLoan(loanNumber);
            return new RocketLogicLoanDetails
            {
                CreatedDateTime = loan.CreatedDateTime,
                IsRocketLogicApiLoan = !ExcludedLoanCreatedBy.Contains(loan.CreatedByAppId),
                CreatedByAppId = loan.CreatedByAppId,
                LoanPurpose = loan?.LoanPurpose,
                RocketLogicLoanId = loan.RocketLogicLoanId
            };
        }
        catch (RequestNotFoundException)
        {
            return new RocketLogicLoanDetails
            {
                IsRocketLogicApiLoan = false
            };
        }
    }
}
