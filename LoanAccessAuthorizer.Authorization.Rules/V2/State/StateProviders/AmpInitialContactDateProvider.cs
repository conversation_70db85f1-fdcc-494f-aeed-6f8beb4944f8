using System.Globalization;
using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class AmpInitialContactDateProvider
    : ExplicitlyIdentifiedStateProvider<DateTime?>
{
    private const int InitialContactId = LoanStatus.InitialContact;
    private static readonly string InitialContactIdString = InitialContactId.ToString(CultureInfo.InvariantCulture);

    public override string Identifier => ExplicitStateProviderIds.AmpInitialContactDate;

    protected override async Task<DateTime?> GetTypedState(StateContainer stateContainer)
    {
        var loanStatuses = await stateContainer.Get<AmpLoanStatusCollection>();

        var initialContactDate = loanStatuses
            .FirstOrDefault(status => status.LoanStatusId == InitialContactIdString)
            ?.LoanStatusDate;

        if (initialContactDate != null)
        {
            return Convert.ToDateTime(initialContactDate);
        }

        return null;
    }
}
