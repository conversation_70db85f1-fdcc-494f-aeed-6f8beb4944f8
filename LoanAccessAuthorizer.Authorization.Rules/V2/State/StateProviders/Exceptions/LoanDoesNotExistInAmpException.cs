using System.Net;
using System.Text.Json;
using DecisionServices.Core.Exceptions;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Exceptions;

public class LoanDoesNotExistInAmpException : StatusCodeException
{
    public bool IsLoanArchived { get; set; }
    public LoanDoesNotExistInAmpException(string message, bool archived = false, Exception inner = null)
        : base(message, HttpStatusCode.InternalServerError, inner)
    {
        IsLoanArchived = archived;
    }

    public override string GetResponseText()
    {
        var errorResponse = new ErrorMessageContainer(Message, IsLoanArchived);
        
        var serializedResponse = JsonSerializer.Serialize(errorResponse);

        return serializedResponse;
    }

    private class ErrorMessageContainer : MessageContainer
    {
        public bool IsArchived { get; set; } 
        public ErrorMessageContainer(string message, bool isArchived) : base(message)
        {
            IsArchived = isArchived;
        }
    }
}
