using DecisionServices.Core.Cache;
using DecisionServices.Core.StateProvider;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.Configuration;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public abstract class LoanCachedAutoStateProvider<T> : KeyCachedAutoStateProvider<string, T>
    where T : class
{
    protected LoanCachedAutoStateProvider(ICache cache, CachePolicy<T> cachePolicy)
        : base(cache, cachePolicy)
    {
    }

    protected override async Task<string> Get<PERSON>ey(StateContainer stateContainer)
    {
        var initialState = await stateContainer.Get<InitialLoanState>();
        return initialState.LoanNumber;
    }
}
