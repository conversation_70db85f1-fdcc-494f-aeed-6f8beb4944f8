using DecisionServices.Core.Cache;
using DecisionServices.Core.Serialization;
using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.LoanData;
using LoanAccessAuthorizer.Domain.LoanData.Models.Contacts;
using Microsoft.Extensions.Logging;
using System.Globalization;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class LoanContactProvider : AutoIdentifiedStateProvider<LoanContacts>, IInvalidatableState
{
    private readonly ICache _cache;
    private readonly ILoanDataClient _loanDataClient;
    private readonly ISerializer _serializer;
    private readonly ILogger<LoanContactProvider> _logger;
    public string FriendlyIdentifier => "LoanContacts";
    internal static readonly TimeSpan _validCommonIdTtl = TimeSpan.FromHours(5);
    internal static readonly TimeSpan _invalidCommonIdTtl = TimeSpan.FromMinutes(1);

    public LoanContactProvider(ICache cache, ILoanDataClient loanDataClient, ISerializer serializer, ILogger<LoanContactProvider> logger)
    {
        _cache = cache;
        _loanDataClient = loanDataClient;
        _serializer = serializer;
        _logger = logger;
    }

    protected override async Task<LoanContacts> GetTypedState(StateContainer stateContainer)
    {
        var initialState = await stateContainer.Get<InitialLoanState>();

        var cacheKey = BuildCacheKey(initialState.LoanNumber);
        var cachedLoanContacts = await _cache.Get(cacheKey);
        if (!string.IsNullOrEmpty(cachedLoanContacts))
        {
            return _serializer.Deserialize<LoanContacts>(cachedLoanContacts);
        }

        var loanContacts = await IsLoanInAmpUtil.ExecuteIfLoanInAmpOrThrow(stateContainer, () => _loanDataClient.GetInternalContactData(initialState.LoanNumber));

        var ttl = _validCommonIdTtl;

        if (string.IsNullOrEmpty(loanContacts.BankerOfRecord.CommonId))
        {
            var loanStatuses = await stateContainer.Get<AmpLoanStatusCollection>();
            var initialContact = LoanStatus.InitialContact.ToString(CultureInfo.InvariantCulture);
            var loanPastInitialContact = loanStatuses.Any(status => !string.Equals(status.LoanStatusId, initialContact, StringComparison.CurrentCultureIgnoreCase));
            ttl = loanPastInitialContact ? _validCommonIdTtl : _invalidCommonIdTtl;
        }

        try
        {
            await _cache.Set(cacheKey, _serializer.Serialize(loanContacts), ttl);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error caching loan contacts for {loanIdentifier}",
                initialState.LoanNumber);
        }

        return loanContacts;
    }

    public Task ClearCache(string cacheId)
    {
        var cacheKey = BuildCacheKey(cacheId);
        return _cache.Delete(cacheKey);
    }

    private CacheKey BuildCacheKey(string loanIdentifier) => new(loanIdentifier, FriendlyIdentifier);
}