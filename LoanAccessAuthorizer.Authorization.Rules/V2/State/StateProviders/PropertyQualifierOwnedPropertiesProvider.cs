using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.PropertyQualifier;
using Property.Domain.Entities.Properties;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class PropertyQualifierOwnedPropertiesProvider : AutoIdentifiedStateProvider<IEnumerable<OwnedProperty>>
{
    private readonly PropertyQualifierServiceClient _propertyQualifierServiceClient;

    public PropertyQualifierOwnedPropertiesProvider(PropertyQualifierServiceClient propertyQualifierServiceClient)
    {
        _propertyQualifierServiceClient = propertyQualifierServiceClient;
    }

    protected override async Task<IEnumerable<OwnedProperty>> GetTypedState(StateContainer stateContainer)
    {
        var initialLoanState = await stateContainer.Get<InitialLoanState>();
        return await _propertyQualifierServiceClient.GetOwnedProperties(initialLoanState.LoanNumber);
    }
}
