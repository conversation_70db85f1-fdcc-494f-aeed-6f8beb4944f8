﻿using DecisionServices.Core.Cache;
using DecisionServices.Core.StateProvider;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.Configuration;
using LoanAccessAuthorizer.Domain.LoanData;
using Microsoft.Extensions.Options;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class ScheduleOfRealEstateOwnedProvider : LoanCachedAutoStateProvider<AmpScheduleOfRealEstateOwnedCollection>
{
    private readonly ILoanDataClient _loanDataClient;
    public override string FriendlyIdentifier => "ScheduleOfRealEstateOwned";
    private static readonly TimeSpan _ttl = TimeSpan.FromMinutes(5);

    public ScheduleOfRealEstateOwnedProvider(ICache cache, CachePolicyFactory cachePolicyFactory, ILoanDataClient loanDataClient, IOptionsSnapshot<GlobalCacheLockingConfiguration> config)
        : base(cache, CreateCachePolicy(cachePolicyFactory, config.Value, _ttl))
    {
        _loanDataClient = loanDataClient;
    }

    protected override Task<AmpScheduleOfRealEstateOwnedCollection> FetchValue(StateContainer stateContainer, string loanNumber)
    {
        return IsLoanInAmpUtil.ExecuteIfLoanInAmpOrThrow(stateContainer,
            async () => new AmpScheduleOfRealEstateOwnedCollection(await _loanDataClient.GetScheduleOfRealEstateOwneds(loanNumber)));
    }
}
