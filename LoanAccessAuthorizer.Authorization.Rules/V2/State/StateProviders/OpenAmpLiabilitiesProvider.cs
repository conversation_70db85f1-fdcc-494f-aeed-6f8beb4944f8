﻿using DecisionServices.Core.Cache;
using DecisionServices.Core.StateProvider;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.Configuration;
using LoanAccessAuthorizer.Domain.LoanData;
using Microsoft.Extensions.Options;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class OpenAmpLiabilitiesProvider : LoanCachedAutoStateProvider<AmpLiabilityCollection>
{
    private readonly ILoanDataClient _loanDataClient;
    public override string FriendlyIdentifier => "OpenAmpLiabilities";
    private static readonly TimeSpan _ttl = TimeSpan.FromMinutes(5);

    public OpenAmpLiabilitiesProvider(ICache cache, CachePolicyFactory cachePolicyFactory,
        ILoanDataClient loanDataClient,
        IOptionsSnapshot<GlobalCacheLockingConfiguration> config)
        : base(cache, CreateCachePolicy(cachePolicyFactory, config.Value, _ttl))
    {
        _loanDataClient = loanDataClient;
    }

    protected override async Task<AmpLiabilityCollection> FetchValue(StateContainer stateContainer, string loanNumber)
    {
        return await IsLoanInAmpUtil.ExecuteIfLoanInAmpOrThrow(stateContainer,
            async () => new AmpLiabilityCollection(await _loanDataClient.GetLiabilities(loanNumber)));
    }
}
