using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.RocketLogicUnderwriting;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class RocketLogicUnlockedTrailsProvider : AutoIdentifiedStateProvider<RocketLogicUnderwritingVisitedTrailSet>
{
    private readonly IRocketLogicUnderwritingServiceClient _rocketLogicUnderwritingService;

    public RocketLogicUnlockedTrailsProvider(IRocketLogicUnderwritingServiceClient rocketLogicUnderwritingService)
    {
        _rocketLogicUnderwritingService = rocketLogicUnderwritingService;
    }

    protected override async Task<RocketLogicUnderwritingVisitedTrailSet> GetTypedState(StateContainer stateContainer)
    {
        var initialState = await stateContainer.Get<InitialLoanState>();
        var trailStatus = await _rocketLogicUnderwritingService.GetTrailStatus(initialState.LoanNumber);
        var visitedSet = new RocketLogicUnderwritingVisitedTrailSet(trailStatus.VisitedTrails)
        {
            UnderwriteFinished = trailStatus.Disabled || trailStatus.IsComplete
        };

        return visitedSet;
    }
}