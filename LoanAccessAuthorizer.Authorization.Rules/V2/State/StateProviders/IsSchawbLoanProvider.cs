using System.Collections.Immutable;
using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Domain.LoanData.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class IsSchawbLoanProvider : ExplicitlyIdentifiedStateProvider<bool>
{

    public override string Identifier => ExplicitStateProviderIds.IsSchwabLoan;

    private static readonly ImmutableHashSet<string> schwabChannels = ImmutableHashSet.Create(
        StringComparer.CurrentCultureIgnoreCase,
        ["Schwab", "Cadillac"]
    );

    protected override async Task<bool> GetTypedState(StateContainer stateContainer)
    {
        var isInAmp = await stateContainer.Get<bool>(ExplicitStateProviderIds.IsLoanInAmp);

        if (isInAmp)
        {
            var ampKeyLoanInfo = await stateContainer.Get<AmpKeyLoanInfo>();

            return IsSchwabChannel(ampKeyLoanInfo.LoanChannel);
        }

        var leadDetails = await stateContainer.Get<RocketLogicBankingLeadDetails>();

        if (leadDetails == null)
        {
            return false;
        }

        return IsSchwabChannel(leadDetails.LeadSourceCategory);
    }

    private static bool IsSchwabChannel(string loanChannel)
    {
        return schwabChannels.Contains(loanChannel);
    }
}
