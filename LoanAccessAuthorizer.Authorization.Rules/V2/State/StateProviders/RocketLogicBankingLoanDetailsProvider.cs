using DecisionServices.Core.Cache;
using DecisionServices.Core.StateProvider;
using InRule.Repository;
using LoanAccessAuthorizer.Domain.Configuration;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using LoanAccessAuthorizer.RocketLogicBanking;
using Microsoft.Extensions.Options;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class RocketLogicBankingLoanDetailsProvider : LoanCachedAutoStateProvider<RocketLogicBankingLoanDetails>
{
    private readonly RLBankingProvider _rlBankingProvider;
    private static readonly TimeSpan _ttl = TimeSpan.FromHours(6);

    public RocketLogicBankingLoanDetailsProvider(ICache cache, CachePolicyFactory policyFactory,
        RLBankingProvider rlBankingProvider,
        IOptionsSnapshot<GlobalCacheLockingConfiguration> config)
        : base(cache, CreateCachePolicy(policyFactory, config.Value, _ttl))
    {
        _rlBankingProvider = rlBankingProvider;
    }

    protected override bool ShouldCache(RocketLogicBankingLoanDetails loanDetails) =>
        loanDetails?.IsLoanImported == true;

    protected override async Task<RocketLogicBankingLoanDetails> FetchValue(StateContainer stateContainer, string loanNumber)
    {
        var rlbLoan = await _rlBankingProvider.GetLoan(loanNumber);
        if (rlbLoan is null)
        {
            return null;
        }

        return new RocketLogicBankingLoanDetails
        {
            LoanNumber = loanNumber,
            IsLoanImported = rlbLoan.IsLoanImported,
            ApplicationStartDate = rlbLoan.CreatedUtcDateTime,
            LoanPurpose = rlbLoan.LoanPurpose
        };
    }
}
