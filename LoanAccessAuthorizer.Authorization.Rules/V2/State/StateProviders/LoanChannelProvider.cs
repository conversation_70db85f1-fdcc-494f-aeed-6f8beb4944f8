using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class LoanChannelProvider : ExplicitlyIdentifiedStateProvider<string>
{
    private readonly ILogger<LoanChannelProvider> _logger;
    public override string Identifier => ExplicitStateProviderIds.LoanChannel;

    public LoanChannelProvider(ILogger<LoanChannelProvider> logger)
    {
        _logger = logger;
    }
    protected override async Task<string> GetTypedState(StateContainer stateContainer)
    {
        var details = await stateContainer.Get<AmpKeyLoanInfo>();

        if (details.LoanChannel == null)
        {
            _logger.LogWarning("No loan channel");
        }

        return details.LoanChannel;
    }
}