using DecisionServices.Core.StateProvider.StateProviders;
using DecisionServices.Core.StateProvider;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.LoanData.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class IsTpoLoanStateProvider : ExplicitlyIdentifiedStateProvider<bool?>
{
    public override string Identifier => ExplicitStateProviderIds.IsTpoLoan;
    private const string RocketProTpoAppId = "206605";

    protected override async Task<bool?> GetTypedState(StateContainer stateContainer)
    {
        var rlLoanDetails = await stateContainer.Get<RocketLogicLoanDetails>();
        if (rlLoanDetails?.IsRocketLogicApiLoan ?? false)
        {
            return rlLoanDetails?.CreatedByAppId == RocketProTpoAppId;
        }

        var tpo = await stateContainer.Get<ThirdPartyOrigination>();
        return tpo?.IsRocketProTPOLoan == true;
    }
}
