using DecisionServices.Core.Cache;
using DecisionServices.Core.StateProvider;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.Configuration;
using LoanAccessAuthorizer.Domain.LoanData;
using Microsoft.Extensions.Options;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class LoanStatusProvider : LoanCachedAutoStateProvider<AmpLoanStatusCollection>
{
    private readonly ILoanDataClient _loanDataClient;
    public override string FriendlyIdentifier => "LoanStatuses";
    private static readonly TimeSpan _ttl = TimeSpan.FromMinutes(5);

    public LoanStatusProvider(ICache cache, CachePolicyFactory cachePolicyFactory,
        IOptionsSnapshot<GlobalCacheLockingConfiguration> config,
        ILoanDataClient loanDataClient)
        : base(cache, CreateCachePolicy(cachePolicyFactory, config.Value, _ttl))
    {
        _loanDataClient = loanDataClient;
    }

    protected override async Task<AmpLoanStatusCollection> FetchValue(StateContainer stateContainer, string loanNumber)
    {
        return await IsLoanInAmpUtil.ExecuteIfLoanInAmpOrThrow(stateContainer,
            async () => new AmpLoanStatusCollection(await _loanDataClient.GetLoanStatus(loanNumber)));
    }
}
