using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Domain.Models;
using LoanAccessAuthorizer.Domain.TeamMemberData;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class LeaderHierarchyStateProvider : AutoIdentifiedStateProvider<LeaderHierarchy>, IInvalidatableState
{
    private readonly ILeaderHierarchyProvider _leaderHierarchyProvider;
    public string FriendlyIdentifier => _leaderHierarchyProvider.FriendlyIdentifier;

    public LeaderHierarchyStateProvider(ILeaderHierarchyProvider leaderHierarchyProvider)
    {
        _leaderHierarchyProvider = leaderHierarchyProvider;
    }

    protected override async Task<LeaderHierarchy> GetTypedState(StateContainer stateContainer)
    {
        var bankerCommonId = await stateContainer.Get<UserId>(ExplicitStateProviderIds.LoanOfficerCommonId);
        return await _leaderHierarchyProvider.GetLeaderHierarchy(bankerCommonId);
    }

    public Task ClearCache(string cacheId)
    {
        return _leaderHierarchyProvider.ClearCache(cacheId);
    }
}
