#nullable enable

using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.RocketDocs;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State;

public class RocketLogicDocumentsProvider : AutoIdentifiedStateProvider<RocketLogicDocuments>
{
    private readonly RLDocumentStorageClient _documentStorageClient;

    public RocketLogicDocumentsProvider(RLDocumentStorageClient documentStorageClient)
    {
        _documentStorageClient = documentStorageClient;
    }

    protected override async Task<RocketLogicDocuments> GetTypedState(StateContainer stateContainer)
    {
        var initialState = await stateContainer.Get<InitialLoanState>();
        var docs = await _documentStorageClient.GetDocuments(initialState.LoanNumber);

        return new RocketLogicDocuments(docs);
    }
}
