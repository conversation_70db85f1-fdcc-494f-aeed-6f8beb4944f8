using DecisionServices.Core.StateProvider;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using LoanAccessAuthorizer.Exclusions.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.Extensions;

public static class StateContainerExtensions
{
    private const string RLXP_APP_ID = "214291";

    public static async Task<(
        bool isRocketLogicBankingLoan,
        bool hasRocketLogicBankingDetails
    )> IsRocketLogicBankingLoan(this StateContainer stateContainer)
    {
        var rocketLogicBankingLeadDetailsTask = stateContainer.Get<RocketLogicBankingLeadDetails>();
        var rocketLogicBankingLoanDetailsTask = stateContainer.Get<RocketLogicBankingLoanDetails>();
        var rocketLogicApiDetailsTask = stateContainer.Get<RocketLogicLoanDetails>();

        var rocketLogicBankingLeadDetails = await rocketLogicBankingLeadDetailsTask;
        var rocketLogicBankingLoanDetails = await rocketLogicBankingLoanDetailsTask;
        var rocketLogicLoanDetails = await rocketLogicApiDetailsTask;

        var isSupportedLead = rocketLogicBankingLeadDetails?.IsUnsupportedLeadType != true;

        var isRlXpLoan = rocketLogicLoanDetails != null && rocketLogicLoanDetails.CreatedByAppId == RLXP_APP_ID;

        var isRocketLogicBankingLoan = isSupportedLead && (rocketLogicBankingLoanDetails != null || isRlXpLoan);

        var hasRocketLogicBankingDetails =
            rocketLogicBankingLeadDetails != null || rocketLogicBankingLoanDetails != null || isRlXpLoan;

        return (isRocketLogicBankingLoan, hasRocketLogicBankingDetails);
    }

    public static async Task<bool> ApplicationHasTrueCachedDecision(
        this StateContainer stateContainer,
        IApplicationAuthorizerDecisionStore decisionStore,
        string applicationId
    )
    {
        var initialLoanState = await stateContainer.Get<InitialLoanState>();

        var existingDecisionTask = decisionStore.GetDecision(
            initialLoanState.LoanNumber,
            applicationId
        );

        var existingExclusionInformationTask = stateContainer.Get<ExclusionInformation>();

        var existingDecision = await existingDecisionTask;
        var existingExclusionInformation = await existingExclusionInformationTask;

        var isExcluded = existingExclusionInformation.IsExcluded(applicationId);
        return !isExcluded || existingDecision == true;
    }
}
