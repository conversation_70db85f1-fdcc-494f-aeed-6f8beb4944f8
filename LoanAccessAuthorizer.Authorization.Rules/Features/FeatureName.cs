namespace LoanAccessAuthorizer.Authorization.Rules.Features;

public static class FeatureName
{
    public const string CrmTpoLoansAllowed = "CrmTpoLoansAllowed";
    public const string LockPurchaseLoansOutOfAmp = "LockPurchaseLoansOutOfAmp";
    public const string CheckValTrackingItemStatusHistory = "CheckValTrackingItemStatusHistory";
    public const string PersistAQDecisions = "PersistAQDecisions";
    public const string AllowAllNewConstruction = "AllowAllNewConstruction";
    public const string EDMQCloudReadOnlyAccess = "EDMQCloudReadOnlyAccess";
    public const string IQQCloudReadOnlyAccess = "IQQCloudReadOnlyAccess";
    public const string MCQCloudReadOnlyAccess = "MCQCloudReadOnlyAccess";
    public const string IsBankerLicensedCheck = "IsBankerLicensedCheck";
    public const string CheckRocketLogicADGroups = "CheckRocketLogicADGroups";
    public const string TeamMemberClockedInStatus = "TeamMemberClockedInStatus";
    public const string EnableCacheLocking = "EnableCacheLocking";
    public const string AllowRefiAccessRlXp = "AllowRefiAccessRlXp";
    public const string FeeRequestOrchestration = "FeeRequestOrchestration";
    public const string RLFeesCategoryOrigination = "RLFeesCategoryOrigination";
    public const string FeeDataOverride = "FeeDataOverride";
    public const string RLFees = "RLFees";
    public const string XpPricing = "XpPricing";
    public const string OverrideEligibilityStoppers = "OverrideEligibilityStoppers";
}
