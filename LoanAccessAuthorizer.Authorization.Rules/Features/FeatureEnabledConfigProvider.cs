using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace LoanAccessAuthorizer.Authorization.Rules.Features;

public sealed class FeatureEnabledConfigProvider : IFeatureEnabledConfigProvider
{
    private readonly ILogger<FeatureEnabledConfigProvider> _logger;
    private readonly Dictionary<string, bool> _featuresEnabled;

    public FeatureEnabledConfigProvider(ILogger<FeatureEnabledConfigProvider> logger,
        IOptionsSnapshot<Dictionary<string, bool>> featuresEnabledConfigurations)
    {
        _logger = logger;
        _featuresEnabled = featuresEnabledConfigurations?.Value;
    }

    public bool IsFeatureEnabled(string featureName)
    {
        if (_featuresEnabled.TryGetValue(featureName, out var featureEnabled))
        {
            return featureEnabled;
        }
        _logger.LogWarning("No featureEnabled config found for {featureName} defaulting to false", featureName);

        return false;
    }
}
