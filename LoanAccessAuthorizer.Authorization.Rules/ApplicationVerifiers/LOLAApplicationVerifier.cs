using LoanAccessAuthorizer.Authorization.Rules.Authorization.Model;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using LoanAccessAuthorizer.Domain.VerifierExclusions;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers;

public class LOLAApplicationVerifier : IApplicationVerifier
{
    private readonly IRocketLogicBankingAuthorizer _rocketLogicBankingAuthorizer;
    private readonly IRLBLolaAuthorizer _rLBLolaAuthorizer;

    public string ApplicationId => Rules.ApplicationId.LOLA;

    public LOLAApplicationVerifier(IRocketLogicBankingAuthorizer rocketLogicBankingAuthorizer,
        IRLBLolaAuthorizer rLBLolaAuthorizer)
    {
        _rocketLogicBankingAuthorizer = rocketLogicBankingAuthorizer;
        _rLBLolaAuthorizer = rLBLolaAuthorizer;
    }

    public Task<(bool, VerifierExclusion)> HasReadAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
    {
        // QCloud and MiCorp users can access LOLA.
        var hasAccess =user.IsInMiCorpDomain() || user.IsInQCloudDomain();
        var reason = hasAccess ? null : new VerifierExclusion();
        return Task.FromResult((hasAccess, reason));
    }

    public async Task<(bool, VerifierExclusion)> HasWriteAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
    {
        if (!user.IsInMiCorpDomain())
        {
            return (false, new VerifierExclusion
            {
                Reason = VerifierExclusionReason.NotInMiCorpDomain,
                Comment = $"{user.CommonId} is not in MiCorpDomain"
            });
        }

        var isRlb = _rocketLogicBankingAuthorizer.IsInPilot(user, accessContainer);
        var isRlbLolaOverride = _rLBLolaAuthorizer.IsInPilot(user, accessContainer);

        if (!await isRlb || await isRlbLolaOverride)
        {
            return (true, null);
        }

        var leadDetails = await accessContainer.StateContainer.Get<RocketLogicBankingLeadDetails>();
        var hasAccess = leadDetails?.InitialLoanOriginationSystem == InitialLoanOriginationSystem.LOLA;
        var reason = hasAccess ? null : new VerifierExclusion();
        return (hasAccess, reason);
    }
}
