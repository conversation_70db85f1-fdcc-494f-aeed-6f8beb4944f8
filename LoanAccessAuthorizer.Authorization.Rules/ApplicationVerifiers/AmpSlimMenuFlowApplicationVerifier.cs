using LoanAccessAuthorizer.Authorization.Rules.Authorization.Model;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using LoanAccessAuthorizer.Domain.TeamMemberData;
using LoanAccessAuthorizer.Domain.VerifierExclusions;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers;

public class AmpSlimMenuFlowApplicationVerifier : IApplicationVerifier
{
    private readonly BankerCheck _bankerCheck;
    private readonly ILogger<AmpSlimMenuFlowApplicationVerifier> _logger;

    public string ApplicationId => Rules.ApplicationId.AmpSlimMenuFlow;

    public AmpSlimMenuFlowApplicationVerifier(BankerCheck bankerCheck,
        ILogger<AmpSlimMenuFlowApplicationVerifier> logger)
    {
        _bankerCheck = bankerCheck;
        _logger = logger;
    }

    public async Task<(bool, VerifierExclusion)> HasReadAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
    {
        // This is tied to the Amp lockout: once users do get to AMP, they only get a subset of the typical Amp menu.
        // Exernal Schwab users are expected to get full access to AMP at all times.
        if (user.IsInQCloudDomain())
        {
            return (false, new VerifierExclusion
            {
                Reason = VerifierExclusionReason.NotInMiCorpDomain,
                Comment = $"{user.CommonId} is in InQCloudDomain"
            });
        }

        var isBanker = await _bankerCheck.IsBanker(user.CommonId, true);

        _logger.LogInformation("Checking read access for AmpSlimMenuFlow: {isBanker}", isBanker);
        var reason = isBanker ? null : new VerifierExclusion();
        return (isBanker, reason);
    }

    public Task<(bool, VerifierExclusion)> HasWriteAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
        // Defaulting to true because LAA returns true for write only when both read and write are true.
        => Task.FromResult((true, (VerifierExclusion)null));
}
