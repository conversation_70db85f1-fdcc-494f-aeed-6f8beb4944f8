using LoanAccessAuthorizer.Domain;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using Microsoft.Extensions.Logging;
using static LoanAccessAuthorizer.Authorization.Rules.LoanStatus;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADDomain;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADRoleNames;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers;

public class PqApplicationVerifier : RoleAccessApplicationVerifier
{
    public PqApplicationVerifier(
        EnvironmentInfo environmentInfo,
        ILogger<PqApplicationVerifier> logger
    )
        : base(
            environmentInfo,
            new()
            {
                [ADRoles.AnyMiCorp] = (LoanStatus) => true,
                [ADRoles.AnyQCloud] = (LoanStatus) => true,
            },
            new()
            {
                [(ITTeamTheLand, MiCorp)] = (loanStatus) => loanStatus is not null,
                [(PQAd<PERSON>, MiCorp)] = (loanStatus) => loanStatus is not null,
                [(IVBanking, MiCorp)] = (loanStatus) =>
                    loanStatus <= FolderReceived
                    || loanStatus.IsIn(
                        Suspended,
                        ApprovedByRocket,
                        ReferredByRocket,
                        ApprovedPendingClientConditionsOnProperty,
                        ApprovedWaitingForProperty,
                        SubmittedToUnderwriting
                    ),
                [(IVSolutionConsultant, MiCorp)] = (loanStatus) =>
                    loanStatus <= FolderReceived
                    || loanStatus.IsIn(
                        Suspended,
                        ApprovedByRocket,
                        ReferredByRocket,
                        ApprovedPendingClientConditionsOnProperty,
                        ApprovedWaitingForProperty,
                        SubmittedToUnderwriting
                    ),
                [(IVUnderwriting, MiCorp)] = (loanStatus) => loanStatus < FinalSignoff,
                [(IVVendor, MiCorp)] = (loanStatus) => loanStatus < FinalSignoff,
                [(PQCondo, MiCorp)] = (loanStatus) => loanStatus < DocsOutToSettlementAgent,
                [(PQTitleCommitment, MiCorp)] = (loanStatus) =>
                    loanStatus < DocsOutToSettlementAgent,
                [(PQPostClose, MiCorp)] = (loanStatus) => loanStatus <= FundedByInvestor,
                [(TeamPLSos, MiCorp)] = (loanStatus) => loanStatus < FinalSignoff,
                [(IVClosing, MiCorp)] = (loanStatus) => loanStatus <= DraftHonoredWarehoused,
                [(DocViewerRoleIt, MiCorp)] = (loanStatus) => loanStatus < FinalSignoff,
            },
            logger
        ) { }

    public override string ApplicationId => Rules.ApplicationId.Property;
}
