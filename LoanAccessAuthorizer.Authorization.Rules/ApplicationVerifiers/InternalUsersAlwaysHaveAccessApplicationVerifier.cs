using LoanAccessAuthorizer.Authorization.Rules.Authorization.Model;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using LoanAccessAuthorizer.Domain.VerifierExclusions;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers;

public abstract class InternalUsersAlwaysHaveAccessApplicationVerifier : IApplicationVerifier
{
    public abstract string ApplicationId { get; }

    public Task<(bool, VerifierExclusion)> HasWriteAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
    {
        // Defaulting to true because LAA returns true for write only when both read and write are true.
        return Task.FromResult((true, (VerifierExclusion)null));
    }

    public Task<(bool, VerifierExclusion)> HasReadAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
    {
        // TODO: consider making the list of domains allowed a configurable property
        // TODO: and add unit tests for each subclass.
        var hasAccess = (user.IsInMiCorpDomain());
        var reason = hasAccess ? null : new VerifierExclusion();
        return Task.FromResult((hasAccess, reason));
    }
}
