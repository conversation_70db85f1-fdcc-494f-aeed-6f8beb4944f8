using LoanAccessAuthorizer.Authorization.Rules.Authorization.Model;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using LoanAccessAuthorizer.Domain.VerifierExclusions;
using LoanAccessAuthorizer.Authorization.Rules.Features;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers.RocketChoice;

public class RlXpPricingVerifier : IApplicationVerifier
{
    private const string _xpPilotId = Rules.ApplicationId.RlXp;
    private const string _rcLockoutId = Rules.ApplicationId.RCLockout;
    public string ApplicationId => Rules.ApplicationId.RlXpPricing;

    public Task<(bool, VerifierExclusion)> HasWriteAccess(ADUser user, VerificationDecisionInputData inputData, AccessDecisionContainer accessContainer) => HasAccess(accessContainer);

    public Task<(bool, VerifierExclusion)> HasReadAccess(ADUser user, VerificationDecisionInputData inputData, AccessDecisionContainer accessContainer) => HasAccess(accessContainer);

    private static async Task<(bool, VerifierExclusion)> HasAccess(AccessDecisionContainer accessContainer)
    {
        var exclusion = new VerifierExclusion
        {
            Reason = VerifierExclusionReason.NotInPilot,
        };
        try
        {

            var rlXpPilotDecisionTask = accessContainer.GetAccessDecision(_xpPilotId);
            var rcLockoutPilotDecisionTask = accessContainer.GetAccessDecision(_rcLockoutId);

            var rlXpPilotDecision = await rlXpPilotDecisionTask;
            var rcLockoutPilotDecision = await rcLockoutPilotDecisionTask;

            var isInRlXp = rlXpPilotDecision.AccessDecision;
            var hasPricingFeature = rlXpPilotDecision?.Features?.Contains(FeatureName.XpPricing) ?? false;
            var isInRcLockout = rcLockoutPilotDecision.AccessDecision;

            if (isInRcLockout && isInRlXp && hasPricingFeature)
            {
                return (true, null);
            }

            return (false, exclusion);
        }
        catch
        {
            return (false, exclusion);
        }
    }
}
