using LoanAccessAuthorizer.Authorization.Rules.Authorization.Model;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using LoanAccessAuthorizer.Domain.VerifierExclusions;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers.RocketChoice;

public class EinsteinApplicationVerifier : IApplicationVerifier
{
    public string ApplicationId => Rules.ApplicationId.Einstein;

    public async Task<(bool, VerifierExclusion)> HasWriteAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
    {
        var hasAccess = await HasAccess(user, accessContainer);
        var reason = hasAccess ? null : new VerifierExclusion();
        return (hasAccess, reason);
    }

    public async Task<(bool, VerifierExclusion)> HasReadAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
    {
        var hasAccess = await <PERSON><PERSON><PERSON><PERSON>(user, accessContainer);
        var reason = hasAccess ? null : new VerifierExclusion();
        return (hasAccess, reason);
    }

    private static async Task<bool> HasAccess(ADUser user, AccessDecisionContainer accessContainer)
    {
        if (!user.IsInMiCorpDomain())
        {
            return false;
        }

        var isLoanInAmp = await accessContainer.StateContainer.Get<bool>(ExplicitStateProviderIds.IsLoanInAmp);
        return isLoanInAmp;
    }
}
