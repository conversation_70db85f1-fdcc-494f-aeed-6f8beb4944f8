using LoanAccessAuthorizer.Authorization.Rules.Authorization.Model;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using LoanAccessAuthorizer.Domain.VerifierExclusions;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers.RocketChoice;

public class MODSApplicationVerifier : IApplicationVerifier
{
    public string ApplicationId => Rules.ApplicationId.MODS;
    public Task<(bool, VerifierExclusion)> HasWriteAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
    {
        return Task.FromResult((false, new VerifierExclusion()));
    }

    public Task<(bool, VerifierExclusion)> HasReadAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
    {
        return Task.FromResult((false, new VerifierExclusion()));
    }
}
