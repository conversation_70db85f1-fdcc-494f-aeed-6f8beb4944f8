using LoanAccessAuthorizer.Authorization.Rules.Authorization.Model;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using LoanAccessAuthorizer.Domain.VerifierExclusions;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers.RocketChoice;

public class ClosedEndSecondsVerifier : IApplicationVerifier
{
    public string ApplicationId => Rules.ApplicationId.ClosedEndSeconds;

    public Task<(bool, VerifierExclusion)> HasWriteAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
    {
        var hasAccess = user.HasRole(ADRoleNames.ClosedEndSecondBanker, ADDomain.MiCorp);
        var reason = hasAccess ? null : new VerifierExclusion();
        return Task.FromResult((hasAccess, reason));
    }

    public Task<(bool, VerifierExclusion)> HasReadAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
    {
        var hasAccess = user.HasRole(ADRoleNames.ClosedEndSecondBanker, ADDomain.MiCorp);
        var reason = hasAccess ? null : new VerifierExclusion();
        return Task.FromResult((hasAccess, reason));
    }
}
