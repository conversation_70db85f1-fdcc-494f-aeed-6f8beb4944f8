using LoanAccessAuthorizer.Authorization.Rules.Authorization.Model;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using LoanAccessAuthorizer.Domain.VerifierExclusions;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers.RocketChoice;

public class RCFormatExceptionsApplicationVerifier : IApplicationVerifier
{
    public string ApplicationId => Rules.ApplicationId.RCFormatExceptions;

    public Task<(bool, VerifierExclusion)> HasWriteAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
    {
        (bool, VerifierExclusion) result = (true, null);
        return Task.FromResult(result);
    }

    public Task<(bool, VerifierExclusion)> HasReadAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
    {
        var hasAccess = HasAccess(user, accessContainer);
        var reason = hasAccess ? null : new VerifierExclusion();
        return Task.FromResult((hasAccess, reason));
    }

    private static bool HasAccess(ADUser user, AccessDecisionContainer accessContainer)
    {
        if (!user.IsInMiCorpDomain())
        {
            return false;
        }

        return user.Roles.Contains((ADRoleNames.ITAll, ADDomain.MiCorp));
    }
}
