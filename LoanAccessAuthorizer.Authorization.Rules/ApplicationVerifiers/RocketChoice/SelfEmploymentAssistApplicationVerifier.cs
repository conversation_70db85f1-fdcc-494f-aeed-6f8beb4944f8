using LoanAccessAuthorizer.Authorization.Rules.Authorization.Model;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using LoanAccessAuthorizer.Domain.VerifierExclusions;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers.RocketChoice;

public class SelfEmploymentAssistApplicationVerifier : IApplicationVerifier
{
    public string ApplicationId => Rules.ApplicationId.SelfEmploymentAssist;

    public async Task<(bool, VerifierExclusion)> HasWriteAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
    {
        var isFranchise = await accessContainer.StateContainer.Get<bool>(ExplicitStateProviderIds.IsFranchiseLoan);
        var hasAccess = isFranchise || user.HasRole(ADRoleNames.SelfEmployedAssistBanker, ADDomain.MiCorp);
        var reason = hasAccess ? null : new VerifierExclusion();
        return (hasAccess, reason);
    }

    public async Task<(bool, VerifierExclusion)> HasReadAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
    {
        var isFranchise = await accessContainer.StateContainer.Get<bool>(ExplicitStateProviderIds.IsFranchiseLoan);
        var hasAccess = isFranchise || user.HasRole(ADRoleNames.SelfEmployedAssistBanker, ADDomain.MiCorp);
        var reason = hasAccess ? null : new VerifierExclusion();
        return (hasAccess, reason);
    }
}
