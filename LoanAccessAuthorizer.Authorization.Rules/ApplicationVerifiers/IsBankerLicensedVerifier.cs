using LoanAccessAuthorizer.Authorization.Rules.Authorization.Model;
using LoanAccessAuthorizer.Authorization.Rules.Features;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using LoanAccessAuthorizer.Domain.Models;
using LoanAccessAuthorizer.Domain.TeamMemberData;
using LoanAccessAuthorizer.Domain.VerifierExclusions;
using LoanAccessAuthorizer.LicensingService;
using LoanAccessAuthorizer.RocketLogicApi.Models;
using Microsoft.Extensions.Logging;
using Property.Domain.Entities.Enums;
using Property.Domain.Entities.Properties;
using Serilog.Context;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers;

public class IsBankerLicensedVerifier : IApplicationVerifier
{
    private readonly IFeatureEnabledConfigProvider _featureEnabledConfigProvider;
    private readonly BankerCheck _bankerCheck;
    private readonly ILogger<IsBankerLicensedVerifier> _logger;
    private readonly ILeaderHierarchyProvider _leaderHierarchyProvider;
    private readonly LicensingServiceProvider _licensingServiceProvider;

    public IsBankerLicensedVerifier(
        IFeatureEnabledConfigProvider featureEnabledConfigProvider,
        BankerCheck bankerCheck,
        ILogger<IsBankerLicensedVerifier> logger,
        ILeaderHierarchyProvider leaderHierarchyProvider,
        LicensingServiceProvider licensingServiceProvider
    )
    {
        _featureEnabledConfigProvider = featureEnabledConfigProvider;
        _bankerCheck = bankerCheck;
        _logger = logger;
        _leaderHierarchyProvider = leaderHierarchyProvider;
        _licensingServiceProvider = licensingServiceProvider;
    }

    public string ApplicationId => Rules.ApplicationId.IsBankerLicensed;

    public async Task<(bool, VerifierExclusion)> HasReadAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
    {
        using var commonIdContext = LogContext.PushProperty("CommonId", user.CommonId);
        if (!_featureEnabledConfigProvider.IsFeatureEnabled(FeatureName.IsBankerLicensedCheck))
        {
            return (true, null);
        }

        if (!await _bankerCheck.IsBanker(user.CommonId, true))
        {
            _logger.LogInformation("Common id {CommonId} is not a banker.", user.CommonId);
            return (true, null);
        }

        var commonId = user.CommonId.ToString();
        var nmlsId = await GetBankerNmlsId(commonId);
        if (nmlsId == null)
        {
            _logger.LogInformation("NmlsId not found for common id {CommonId}", user.CommonId);
            return (true, null);
        }
        var bankerLicensedDataTask = _licensingServiceProvider.GetBankerLicenseData(nmlsId);
        var subjectPropertyStateTask = GetSubjectPropertyState(accessContainer);
        var clientStateTask = GetClientAddressState(accessContainer);
        var clientAddressState = await clientStateTask;
        var subjectPropertyState = await subjectPropertyStateTask;
        var bankerLicensedData = await bankerLicensedDataTask;

        var isSatisfiesOhioLicensingLaw =
            clientAddressState is null
            || SatisfiesOhioLicensingLaw(nmlsId, clientAddressState, bankerLicensedData);

        var isBankerLicensedInState =
            subjectPropertyState is null
            || IsBankerLicensedInState(nmlsId, subjectPropertyState.ToString(), bankerLicensedData);

        if (!isBankerLicensedInState)
        {
            // In case licensing service returns a false negative
            _logger.LogInformation(
                "Banker common id: {CommonId} licensing check failed for isBankerLicensedInState for state {subjectPropertyState}.",
                user.CommonId,
                subjectPropertyState
            );
        }

        if (!isSatisfiesOhioLicensingLaw)
        {
            // In case licensing service returns a false negative
            _logger.LogInformation(
                "{BankerCommonId} did not satisfy Ohio Licensing Law. {ClientResidentState}",
                +user.CommonId,
                clientAddressState
            );
        }
        var hasAccess = isBankerLicensedInState && isSatisfiesOhioLicensingLaw;
        var exclusion = GetExclusion(
            isBankerLicensedInState,
            isSatisfiesOhioLicensingLaw,
            subjectPropertyState,
            clientAddressState
        );
        return (hasAccess, exclusion);
    }

    private async Task<string> GetBankerNmlsId(string commonId)
    {
        return (await _leaderHierarchyProvider.GetLeaderHierarchy(UserId.FromCommonId(commonId)))?.NmlsId;
    }

    private bool IsBankerLicensedInState(
        string nmlsId,
        string stateCode,
        BankerLicenseData bankerLicenseData
    )
    {
        if (bankerLicenseData == null)
        {
            _logger.LogInformation(
                "No licensing data returned for nmlsId: {nmlsId} for state code: {stateCode} from licensing service.",
                nmlsId,
                stateCode
            );
            return false;
        }
        var bankerStateCodes = GetBankerLicenseStateCodes(bankerLicenseData);
        return bankerStateCodes.Contains(stateCode);
    }

    // Ohio law is the banker needs to be licensed in OH and the subject property state if the client lives in Ohio.
    private bool SatisfiesOhioLicensingLaw(
        string nmlsId,
        string clientState,
        BankerLicenseData bankerLicenseData
    )
    {
        if (string.Equals(clientState, "OH", StringComparison.OrdinalIgnoreCase))
        {
            return IsBankerLicensedInState(nmlsId, clientState, bankerLicenseData);
        }

        return true;
    }

    private async Task<string> GetClientAddressState(AccessDecisionContainer accessContainer)
    {
        try
        {
            var clientsTask = accessContainer.StateContainer.Get<IEnumerable<RlApiClient>>();
            var clients = await clientsTask;
            return clients
                .Select(client => client?.ResidenceInformation?.CurrentResidence?.Address?.StateCode)
                .FirstOrDefault();
        }
        catch (Exception ex)
        {
            // fail gracefull if getting client data fails
            // for example: when there is no client
            _logger.LogInformation(
                "Failed to get client data. Error Message: {ErrorMessage}",
                ex.Message
            );
            return null;
        }
    }

    private async Task<State?> GetSubjectPropertyState(AccessDecisionContainer accessContainer)
    {
        try
        {
            var pqSubjectPropertyTask = accessContainer.StateContainer.Get<SubjectProperty>();
            var pqSubjectProperty = await pqSubjectPropertyTask;
            var state = pqSubjectProperty?.Address?.State;
            return state;
        }
        catch (Exception ex)
        {
            // fail gracefully if getting subject property fails
            // for example: when there is no subject property
            _logger.LogInformation(
                "Failed to get subject property. Error Message: {ErrorMessage}",
                ex.Message
            );
            return null;
        }
    }

    private static List<string> GetBankerLicenseStateCodes(BankerLicenseData bankerLicenseData)
    {
        return bankerLicenseData
                ?.StateLicenses?.Select(stateLicense => stateLicense?.StateCode)
                ?.ToList() ?? new List<string>();
    }

    private static VerifierExclusion GetExclusion(
        bool isBankerLicensedInState,
        bool isSatisfiesOhioLicensingLaw,
        State? subjectPropertyState,
        string clientAddressState
    )
    {
        if (isBankerLicensedInState && isSatisfiesOhioLicensingLaw)
        {
            return null;
        }

        if (!isBankerLicensedInState)
        {
            return new VerifierExclusion
            {
                Reason = VerifierExclusionReason.NotLicensedInSubjectPropertyState,
                Comment = subjectPropertyState.ToString(),
            };
        }

        if (!isSatisfiesOhioLicensingLaw)
        {
            return new VerifierExclusion
            {
                Reason = VerifierExclusionReason.NotLicensedInClientAddressState,
                Comment = clientAddressState,
            };
        }

        return null;
    }

    public Task<(bool, VerifierExclusion)> HasWriteAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
        // Defaulting to true because LAA returns true for write only when both read and write are true.
        =>
        Task.FromResult((true, (VerifierExclusion)null));
}
