using LoanAccessAuthorizer.Authorization.Rules.Authorization.Model;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using LoanAccessAuthorizer.Domain.VerifierExclusions;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers;

public class PIQUIApplicationVerifier : IApplicationVerifier
{
    public string ApplicationId { get; } = Rules.ApplicationId.PIQ;
    public Task<(bool, VerifierExclusion)> HasWriteAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
    {
        // Defaulting to true because LAA returns true for write only when both read and write are true.
        return Task.FromResult((true, (VerifierExclusion)null));
    }

    public Task<(bool, VerifierExclusion)> HasReadAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
    {
        var hasAccess = user.HasRole(ADRoleNames.PIQInsuranceSpecialist, ADDomain.MiCorp);
        var reason = hasAccess ? null : new VerifierExclusion();
        return Task.FromResult((hasAccess, reason));
    }
}
