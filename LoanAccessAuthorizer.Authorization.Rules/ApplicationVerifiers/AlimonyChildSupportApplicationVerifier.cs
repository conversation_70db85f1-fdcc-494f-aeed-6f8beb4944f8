using LoanAccessAuthorizer.Domain;
using Microsoft.Extensions.Logging;
using static LoanAccessAuthorizer.Authorization.Rules.LoanStatus;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADDomain;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADRoleNames;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers;

class AlimonyChildSupportApplicationVerifier : RoleAccessApplicationVerifier
{
    private static RoleLoanStatusCheckDictionary AccessChecks { get; } =
        new()
        {
            [(IncomeViewerAdmin, MiCorp)] = (loanStatus) => true,
            [(DocViewerRoleIt, MiCorp)] = (loanStatus) => loanStatus < FinalSignoff,
            [(TeamPLSos, MiCorp)] = (loanStatus) => loanStatus < FinalSignoff,
            [(IVBanking, MiCorp)] = (loanStatus) =>
                loanStatus <= FolderReceived
                || loanStatus.IsIn(Suspended, ApprovedByRocket, ReferredByRocket),
            [(IVSolutionConsultant, MiCorp)] = (loanStatus) =>
                loanStatus <= FolderReceived
                || loanStatus.IsIn(Suspended, ApprovedByRocket, ReferredByRocket),

            [(IVClosing, MiCorp)] = (loanStatus) => loanStatus < Closed,
            [(IVUnderwriting, MiCorp)] = (loanStatus) => loanStatus < FinalSignoff,
        };

    public AlimonyChildSupportApplicationVerifier(
        EnvironmentInfo environmentInfo,
        ILogger<AlimonyChildSupportApplicationVerifier> logger
    )
        : base(environmentInfo, AccessChecks, AccessChecks, logger) { }

    public override string ApplicationId => Rules.ApplicationId.CQAlimonyChildSupport;
}
