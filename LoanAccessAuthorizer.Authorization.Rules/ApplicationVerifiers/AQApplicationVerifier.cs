using LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers.RocketLogicUnderwriting;
using LoanAccessAuthorizer.Domain;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using Microsoft.Extensions.Logging;
using static LoanAccessAuthorizer.Authorization.Rules.LoanStatus;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADDomain;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADRoleNames;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers;

public class AQApplicationVerifier : RoleAccessApplicationVerifier
{
    public AQApplicationVerifier(
        EnvironmentInfo environmentInfo,
        ILogger<AQApplicationVerifier> logger
    )
        : base(
            environmentInfo,
            new()
            {
                [ADRoles.AnyMiCorp] = (LoanStatus) => true,
                [ADRoles.AnyQCloud] = (LoanStatus) => true,
            },
            new()
            {
                [(IncomeViewerAdmin, MiCorp)] = (loanStatus) => true,
                [(DocViewerRoleIt, MiCorp)] = (loanStatus) => loanStatus < FinalSignoff,
                [(TeamPLSos, MiCorp)] = (loanStatus) => loanStatus < FinalSignoff,
                [(IVBanking, MiCorp)] = (loanStatus) =>
                    loanStatus <= FolderReceived
                    || loanStatus.IsIn(
                        Suspended,
                        ApprovedByRocket,
                        ReferredByRocket,
                        ApprovedPendingClientConditionsOnProperty
                    ),
                [(IVClosing, MiCorp)] = (loanStatus) => loanStatus < Closed,
                [(IVUnderwriting, MiCorp)] = (loanStatus) => loanStatus < FinalSignoff,
                [(IVSolutionConsultant, MiCorp)] = (loanStatus) =>
                    loanStatus <= FolderReceived
                    || loanStatus.IsIn(
                        Suspended,
                        ApprovedByRocket,
                        ConditionallyApproved,
                        ReferredByRocket,
                        FinalSignoffPendingAction
                    ),
            },
            logger
        ) { }

    protected override string[] RocketLogicTrailWhiteList => RocketLogicTrailNames.AssetGroups;

    public override string ApplicationId => Rules.ApplicationId.Asset;
}
