using LoanAccessAuthorizer.Authorization.Rules.Authorization.Model;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using LoanAccessAuthorizer.Domain.VerifierExclusions;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers;

public interface IApplicationVerifier
{
    string ApplicationId { get; }
    Task<(bool, VerifierExclusion)> HasWriteAccess(ADUser user, VerificationDecisionInputData inputData, AccessDecisionContainer accessContainer);
    Task<(bool, VerifierExclusion)> HasReadAccess(ADUser user, VerificationDecisionInputData inputData, AccessDecisionContainer accessContainer);
}
