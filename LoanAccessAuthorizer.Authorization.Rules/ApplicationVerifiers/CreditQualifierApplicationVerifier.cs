using LoanAccessAuthorizer.Domain;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using Microsoft.Extensions.Logging;
using static LoanAccessAuthorizer.Authorization.Rules.LoanStatus;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADDomain;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADRoleNames;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers;

public class CreditQualifierApplicationVerifier : RoleAccessApplicationVerifier
{
    public CreditQualifierApplicationVerifier(
        EnvironmentInfo environmentInfo,
        ILogger<CreditQualifierApplicationVerifier> logger
    )
        : base(
            environmentInfo,
            new() { [ADRoles.AnyMiCorp] = (LoanStatus) => true },
            new()
            {
                [(IncomeViewerAdmin, MiCorp)] = (loanStatus) => true,
                [(DocViewerRoleIt, MiCorp)] = (loanStatus) => loanStatus < FinalSignoff,
                [(TeamPLSos, MiCorp)] = (loanStatus) => loanStatus < FinalSignoff,
                [(IVBanking, MiCorp)] = (loanStatus) =>
                    loanStatus <= FolderReceived
                    || loanStatus.IsIn(Suspended, ApprovedByRocket, ReferredByRocket),
                [(IVSolutionConsultant, MiCorp)] = (loanStatus) => loanStatus < FinalSignoff,
                [(IVClosing, MiCorp)] = (loanStatus) => loanStatus < Closed,
                [(IVUnderwriting, MiCorp)] = (loanStatus) => loanStatus < FinalSignoff,
            },
            logger
        ) { }

    public override string ApplicationId => Rules.ApplicationId.Credit;
}
