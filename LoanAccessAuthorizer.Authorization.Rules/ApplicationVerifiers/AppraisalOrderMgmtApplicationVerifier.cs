using LoanAccessAuthorizer.Domain;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using Microsoft.Extensions.Logging;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADDomain;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADRoleNames;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers;

public class AppraisalOrderMgmtApplicationVerifier : RoleAccessApplicationVerifier
{
    public AppraisalOrderMgmtApplicationVerifier(
        EnvironmentInfo environmentInfo,
        ILogger<AppraisalOrderMgmtApplicationVerifier> logger
    )
        : base(
            environmentInfo,
            new() { [ADRoles.AnyMiCorp] = (loanStatus) => true },
            new()
            {
                // Appraisal Tech teams and PQ Admin always get write permissions.
                [(ITAppraisalTeam1, MiCorp)] = (loanStatus) => loanStatus is not null,
                [(ITAppraisalTeam2, MiCorp)] = (loanStatus) => loanStatus is not null,
                [(PQAdmin, MiCorp)] = (loanStatus) => loanStatus is not null,
            },
            logger
        ) { }

    public override string ApplicationId => Rules.ApplicationId.AppraisalOrderManagement;
}
