using LoanAccessAuthorizer.Authorization.Rules.Features;
using LoanAccessAuthorizer.Domain;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers.MortgageClient;

/// <summary>
/// Application verifier for Personal Information in Mortgage Client application. <br/>
/// Team: Team Momentum <br/>
/// UI Application ID: MCAddress <br/>
/// </summary>
public class MCAddressApplicationVerifier : PIApplicationVerifier
{
    public MCAddressApplicationVerifier(
        IFeatureEnabledConfigProvider features,
        EnvironmentInfo environmentInfo,
        ILogger<MCAddressApplicationVerifier> logger
    )
        : base(features, environmentInfo, logger) { }

    public override string ApplicationId => Rules.ApplicationId.MCAddress;
}
