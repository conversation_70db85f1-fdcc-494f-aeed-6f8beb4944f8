using LoanAccessAuthorizer.Authorization.Rules.Features;
using LoanAccessAuthorizer.Domain;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using Microsoft.Extensions.Logging;
using static LoanAccessAuthorizer.Authorization.Rules.LoanStatus;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADDomain;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADRoleNames;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers.MortgageClient;

public class DemographicsApplicationVerifier : RoleAccessApplicationVerifier
{
    public DemographicsApplicationVerifier(
        IFeatureEnabledConfigProvider features,
        EnvironmentInfo environmentInfo,
        ILogger<DemographicsApplicationVerifier> logger
    )
        : base(
            environmentInfo,
            new RoleLoanStatusCheckDictionary() { [ADRoles.AnyMiCorp] = _ => true },
            new()
            {
                [(IncomeViewerAdmin, MiCorp)] = (loanStatus) => true,
                [(DocViewerRoleIt, MiCorp)] = (loanStatus) => loanStatus < FinalSignoff,
                [(TeamPLSos, MiCorp)] = (loanStatus) => loanStatus < FinalSignoff,
                [(IVBanking, MiCorp)] = (loanStatus) =>
                    loanStatus <= FolderReceived
                    || loanStatus.IsIn(
                        Suspended,
                        ApprovedByRocket,
                        ReferredByRocket,
                        ApprovedPendingClientConditionsOnProperty
                    ),
                [(IVSolutionConsultant, MiCorp)] = (loanStatus) =>
                    loanStatus <= FolderReceived
                    || loanStatus.IsIn(Suspended, ApprovedByRocket, ReferredByRocket),
                [(IVClosing, MiCorp)] = (loanStatus) => loanStatus < Closed,
                [(IVUnderwriting, MiCorp)] = (loanStatus) => loanStatus < FinalSignoff,
            },
            logger
        )
    {
        if (features.IsFeatureEnabled(FeatureName.MCQCloudReadOnlyAccess))
        {
            _readAccessChecks[ADRoles.AnyQCloud] = _ => true;
        }
    }

    public override string ApplicationId => Rules.ApplicationId.Demographics;
}
