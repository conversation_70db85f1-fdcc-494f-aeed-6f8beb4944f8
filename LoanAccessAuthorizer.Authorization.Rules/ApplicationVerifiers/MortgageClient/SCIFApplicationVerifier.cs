using LoanAccessAuthorizer.Authorization.Rules.Authorization.Model;
using LoanAccessAuthorizer.Authorization.Rules.Features;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using LoanAccessAuthorizer.Domain.VerifierExclusions;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers.MortgageClient;

public class SCIFApplicationVerifier : IApplicationVerifier
{
    private bool _isQCloudReadOnlyAccess;

    public SCIFApplicationVerifier(IFeatureEnabledConfigProvider features)
    {
        _isQCloudReadOnlyAccess = features.IsFeatureEnabled(FeatureName.MCQCloudReadOnlyAccess);
    }

    private static readonly ISet<string> ProductCodes = new HashSet<string>(
        StringComparer.CurrentCultureIgnoreCase
    )
    {
        "130S",
        "130CA",
        "130CC",
        "130C",
        "130",
        "130CH",
        "130SH",
        "915",
        "915CJ",
        "915CS",
        "915J",
        "915SJ",
        "915SL",
        "920",
        "920CJ",
        "920CS",
        "920SJ",
        "920SL",
        "925",
        "925CJ",
        "925CS",
        "925J",
        "925SJ",
        "925SL",
        "930",
        "930CJ",
        "930CS",
        "930J",
        "930LC",
        "930LH",
        "930SJ",
        "930SL",
        "951",
        "951CJ",
        "951CS",
        "951J",
        "951SJ",
        "951SL",
    };

    public string ApplicationId => Rules.ApplicationId.MCSupplementalConsumerInformation;

    public Task<(bool, VerifierExclusion)> HasWriteAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
    {
        // LAA returns true for write only when both read and write are true.
        if (user.HasRole(ADRoleNames.IncomeViewerAdmin, ADDomain.MiCorp))
        {
            return Task.FromResult((true, (VerifierExclusion)null));
        }

        var productCode = inputData.LoanDetails.ProductCode;
        var hasAccess =
            user.HasRole(ADRoleNames.IVUnderwriting, ADDomain.MiCorp)
            && ProductCodes.Contains(productCode);
        return Task.FromResult((hasAccess, new VerifierExclusion()));
    }

    public Task<(bool, VerifierExclusion)> HasReadAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
    {
        if (user.IsInMiCorpDomain())
        {
            return Task.FromResult((true, (VerifierExclusion)null));
        }

        if (user.IsInQCloudDomain())
        {
            var reason = _isQCloudReadOnlyAccess ? null : new VerifierExclusion();
            return Task.FromResult((_isQCloudReadOnlyAccess, reason));
        }

        return Task.FromResult((false, new VerifierExclusion()));
    }
}
