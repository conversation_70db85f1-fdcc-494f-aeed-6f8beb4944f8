using LoanAccessAuthorizer.Authorization.Rules.Features;
using LoanAccessAuthorizer.Domain;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers.MortgageClient;

public class PIVAApplicationVerifier : PIApplicationVerifier
{
    public PIVAApplicationVerifier(
        IFeatureEnabledConfigProvider features,
        EnvironmentInfo environmentInfo,
        ILogger<PIVAApplicationVerifier> logger
    )
        : base(features, environmentInfo, logger) { }

    public override string ApplicationId => Rules.ApplicationId.PersonalInformationVeteransAffairs;
}
