using LoanAccessAuthorizer.Authorization.Rules.Authorization.Model;
using LoanAccessAuthorizer.Authorization.Rules.Features;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using LoanAccessAuthorizer.Domain.Constants;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using LoanAccessAuthorizer.Domain.VerifierExclusions;
using Microsoft.Extensions.Logging;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADDomain;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADRoleNames;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers.MortgageClient;

public class MCClientChoosingTitleApplicationVerifier : RoleAccessApplicationVerifier
{
    private static readonly string _folderReceived = LoanStatus.FolderReceived.ToString();
    private static readonly string _conditionallyApproved =
        LoanStatus.ConditionallyApproved.ToString();

    public override string ApplicationId => Rules.ApplicationId.MCClientChoosingTitlePilot;

    public MCClientChoosingTitleApplicationVerifier(
        IFeatureEnabledConfigProvider features,
        EnvironmentInfo environmentInfo,
        ILogger<MCClientChoosingTitleApplicationVerifier> logger
    )
        : base(
            environmentInfo,
            new RoleLoanStatusCheckDictionary() { [ADRoles.AnyMiCorp] = _ => true },
            new()
            {
                [(RLPurchaseSpecialist, MiCorp)] = loanStatus => loanStatus is < LoanStatus.Closed,
                [(RLTitleCoordinator, MiCorp)] = loanStatus => loanStatus is < LoanStatus.Closed,
                [(RLClosingSpecialist, MiCorp)] = loanStatus => loanStatus is < LoanStatus.Closed,
                [(RLFrontlineSupportSpecialist, MiCorp)] = loanStatus =>
                    loanStatus is < LoanStatus.Closed,
                [(RLHotlineClosingSpecialist, MiCorp)] = loanStatus =>
                    loanStatus is < LoanStatus.Closed,
                [(TeamFinalDocs, MiCorp)] = _ => true,
            },
            logger
        )
    {
        if (features.IsFeatureEnabled(FeatureName.MCQCloudReadOnlyAccess))
        {
            _readAccessChecks[ADRoles.AnyQCloud] = _ => true;
        }
    }

    public override async Task<(bool, VerifierExclusion)> HasWriteAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
    {
        var isLoanInAmp = await accessContainer.StateContainer.Get<bool>(
            ExplicitStateProviderIds.IsLoanInAmp
        );
        if (!isLoanInAmp)
        {
            return (
                false,
                new VerifierExclusion
                {
                    Reason = VerifierExclusionReason.NotInAmp,
                    Comment = "Loan is not in AMP",
                }
            );
        }

        var loanStatuses = await accessContainer.StateContainer.Get<AmpLoanStatusCollection>();
        var loanDetails = inputData.LoanDetails;

        var hasAccess =
            user.Roles.Any(role =>
                role switch
                {
                    (IVBanking, MiCorp) => IsBankerAuthorized(loanStatuses, loanDetails),
                    _ => WriteAccessChecks.TryGetValue(role, out var rule)
                        && rule(loanDetails.CurrentLoanStatus),
                }
            ) && !await IsTitleOrderPending(accessContainer);
        var reason = hasAccess ? null : new VerifierExclusion();
        return (hasAccess, reason);
    }

    private static async Task<bool> IsTitleOrderPending(AccessDecisionContainer accessContainer)
    {
        var ampTitleCompany = await accessContainer.StateContainer.Get<AmpTitleCompany>();
        return ampTitleCompany is { TitleOrderPending: true };
    }

    private static bool IsBankerAuthorized(
        AmpLoanStatusCollection loanStatuses,
        AmpKeyLoanInfo loanDetails
    ) =>
        !HasReachedFolderReceived(loanStatuses)
        || (
            !HasReachedConditionallyApproved(loanStatuses)
            && IsMortgageFirstOrNewConstruction(loanDetails)
        );

    private static bool HasReachedFolderReceived(AmpLoanStatusCollection loanStatuses) =>
        loanStatuses.Any(loanStatus => loanStatus.LoanStatusId == _folderReceived);

    private static bool HasReachedConditionallyApproved(AmpLoanStatusCollection loanStatuses) =>
        loanStatuses.Any(loanStatus => loanStatus.LoanStatusId == _conditionallyApproved);

    private static bool IsMortgageFirstOrNewConstruction(AmpKeyLoanInfo loanDetails) =>
        loanDetails switch
        {
            { LoanPurpose: LoanPurpose.MortgageFirst or LoanPurpose.NewConstruction } => true,
            _ => false,
        };
}
