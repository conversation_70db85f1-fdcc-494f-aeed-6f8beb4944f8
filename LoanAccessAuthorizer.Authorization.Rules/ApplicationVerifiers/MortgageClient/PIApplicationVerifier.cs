using LoanAccessAuthorizer.Authorization.Rules.Features;
using LoanAccessAuthorizer.Domain;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using Microsoft.Extensions.Logging;
using static LoanAccessAuthorizer.Authorization.Rules.LoanStatus;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADDomain;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADRoleNames;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers.MortgageClient;

public class PIApplicationVerifier : RoleAccessApplicationVerifier
{
    public override string ApplicationId => Rules.ApplicationId.PersonalInformation;

    private readonly IFeatureEnabledConfigProvider _features;

    public PIApplicationVerifier(
        IFeatureEnabledConfigProvider features,
        EnvironmentInfo environmentInfo,
        ILogger<PIApplicationVerifier> logger
    )
        : base(
            environmentInfo,
            new() { [ADRoles.AnyMiCorp] = _ => true },
            new()
            {
                [(IncomeViewerAdmin, MiCorp)] = (loanStatus) => true,
                [(DocViewerRoleIt, MiCorp)] = (loanStatus) => loanStatus < FinalSignoff,
                [(TeamPLSos, MiCorp)] = (loanStatus) => loanStatus < FinalSignoff,
                [(IVBanking, MiCorp)] = (loanStatus) =>
                    loanStatus <= FolderReceived
                    || loanStatus.IsIn(
                        Suspended,
                        ApprovedByRocket,
                        ReferredByRocket,
                        ApprovedPendingClientConditionsOnProperty
                    ),
                [(IVSolutionConsultant, MiCorp)] = (loanStatus) =>
                    loanStatus >= FolderReceived && loanStatus <= FinalSignoff,
                [(IVClosing, MiCorp)] = (loanStatus) => loanStatus < Closed,
                [(IVUnderwriting, MiCorp)] = (loanStatus) => loanStatus < FinalSignoff,
                [(RLClientCareSpecialist, MiCorp)] = (loanStatus) =>
                    loanStatus >= FolderReceived && loanStatus <= FinalSignoffPendingAction,
                [(RLClientAdvocate, MiCorp)] = (loanStatus) =>
                    loanStatus >= FolderReceived && loanStatus <= FinalSignoff,
                [(RLResolutionSpecialist, MiCorp)] = (loanStatus) =>
                    loanStatus >= FolderReceived && loanStatus <= FinalSignoff,
                [(RLTitleClearingVestingAnalyst, MiCorp)] = (loanStatus) =>
                    loanStatus >= FolderReceived && loanStatus <= FinalSignoff,
                [(RLFreshStartConsultant, MiCorp)] = (loanStatus) =>
                    loanStatus >= FolderReceived && loanStatus <= FinalSignoff,
                [(RLRefinanceEscalationSpecialist, MiCorp)] = (loanStatus) =>
                    loanStatus >= FolderReceived && loanStatus <= FinalSignoffPendingAction,
                [(RLHotlineClosingSpecialist, MiCorp)] = (loanStatus) =>
                    loanStatus >= FinalSignoff && loanStatus <= DraftHonoredWarehoused,
                [(RLFedsSpecialist, MiCorp)] = (loanStatus) =>
                    loanStatus >= FolderReceived && loanStatus <= FinalSignoff,
                [(RLClosingCareRepresentative, MiCorp)] = (loanStatus) =>
                    loanStatus >= FinalSignoff && loanStatus <= DraftHonoredWarehoused,
                [(RLClosingUnderwriting, MiCorp)] = (loanStatus) =>
                    loanStatus >= FinalSignoff && loanStatus <= FundedByInvestor,
                [(RLFrontlineSupportSpecialist, MiCorp)] = (loanStatus) =>
                    loanStatus >= FolderReceived && loanStatus <= FinalSignoffPendingAction,
                [(RLFolderReceivedAnalyst, MiCorp)] = (loanStatus) =>
                    loanStatus >= FolderReceived && loanStatus <= FinalSignoffPendingAction,
                [(RLPurchaseSpecialist, MiCorp)] = (loanStatus) =>
                    loanStatus >= FolderReceived && loanStatus <= DraftHonoredWarehoused,
                [(RLTitleCoordinator, MiCorp)] = (loanStatus) =>
                    loanStatus >= FolderReceived && loanStatus <= DraftHonoredWarehoused,
                [(RLClosingSpecialist, MiCorp)] = (loanStatus) => loanStatus <= FileReadyToShip,
                [(RLSelfEmploymentLoanAnalyst, MiCorp)] = (loanStatus) =>
                    loanStatus >= FolderReceived && loanStatus <= FinalSignoffPendingAction,
                [(RLPurchaseAgreementReviewer, MiCorp)] = (loanStatus) =>
                    loanStatus >= FolderReceived && loanStatus <= FinalSignoffPendingAction,
            },
            logger
        )
    {
        if (features.IsFeatureEnabled(FeatureName.MCQCloudReadOnlyAccess))
        {
            _readAccessChecks[ADRoles.AnyQCloud] = _ => true;
        }
    }
}
