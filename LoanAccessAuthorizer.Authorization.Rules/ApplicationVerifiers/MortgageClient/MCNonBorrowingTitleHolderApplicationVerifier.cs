using LoanAccessAuthorizer.Authorization.Rules.Authorization.Model;
using LoanAccessAuthorizer.Authorization.Rules.Features;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using LoanAccessAuthorizer.Domain.VerifierExclusions;
using Microsoft.Extensions.Logging;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADRoleNames;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers.MortgageClient;

public class MCNonBorrowingTitleHolderApplicationVerifier : RoleAccessApplicationVerifier
{
    private static readonly string _folderRecieved = LoanStatus.FolderReceived.ToString();
    private static readonly string _closed = LoanStatus.Closed.ToString();

    public override string ApplicationId => Rules.ApplicationId.MCNonBorrowingTitleHolderPilot;

    public MCNonBorrowingTitleHolderApplicationVerifier(
        IFeatureEnabledConfigProvider features,
        EnvironmentInfo environmentInfo,
        ILogger<MCNonBorrowingTitleHolderApplicationVerifier> logger
    )
        : base(environmentInfo, new() { [ADRoles.AnyMiCorp] = _ => true }, new(), logger)
    {
        if (features.IsFeatureEnabled(FeatureName.MCQCloudReadOnlyAccess))
        {
            _readAccessChecks[ADRoles.AnyQCloud] = _ => true;
        }
    }

    public override async Task<(bool, VerifierExclusion)> HasWriteAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
    {
        if (!user.IsInMiCorpDomain())
        {
            return (
                false,
                new VerifierExclusion
                {
                    Reason = VerifierExclusionReason.NotInMiCorpDomain,
                    Comment = $"{user.CommonId} is not in MiCorpDomain",
                }
            );
        }

        var isLoanInAmp = await accessContainer.StateContainer.Get<bool>(
            ExplicitStateProviderIds.IsLoanInAmp
        );
        if (!isLoanInAmp)
        {
            return (
                false,
                new VerifierExclusion
                {
                    Reason = VerifierExclusionReason.NotInAmp,
                    Comment = "Loan is not in AMP",
                }
            );
        }

        var loanStatuses = await accessContainer.StateContainer.Get<AmpLoanStatusCollection>();

        var hasLoanClosed = HasReachedLoanStatus(_closed, loanStatuses);
        var hasAccess = user.Roles.Any(role =>
            role.RoleName switch
            {
                RLTitleCoordinator => !hasLoanClosed,
                RLClosingSpecialist => !hasLoanClosed,
                RLFrontlineSupportSpecialist => !hasLoanClosed,
                RLHotlineClosingSpecialist => !hasLoanClosed,
                RLClosingUnderwriting => !hasLoanClosed,
                IVUnderwriting => !hasLoanClosed,
                RLUnderwritingCondo => !hasLoanClosed,
                RLUnderwritingSupport => !hasLoanClosed,
                RLUnderwritingPilot => !hasLoanClosed,
                TeamFinalDocs => true,
                _ => !HasReachedLoanStatus(_folderRecieved, loanStatuses),
            }
        );
        var reason = hasAccess ? null : new VerifierExclusion();
        return (hasAccess, reason);
    }

    private static bool HasReachedLoanStatus(
        string loanStatusId,
        AmpLoanStatusCollection loanStatuses
    ) => loanStatuses.Any(loanStatus => loanStatus.LoanStatusId == loanStatusId);
}
