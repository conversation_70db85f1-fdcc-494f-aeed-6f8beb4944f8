using LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers.RocketLogicUnderwriting;
using LoanAccessAuthorizer.Authorization.Rules.Features;
using LoanAccessAuthorizer.Domain;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using Microsoft.Extensions.Logging;
using static LoanAccessAuthorizer.Authorization.Rules.LoanStatus;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADDomain;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADRoleNames;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers;

public class IQApplicationVerifier : RoleAccessApplicationVerifier
{
    public IQApplicationVerifier(
        IFeatureEnabledConfigProvider features,
        EnvironmentInfo environmentInfo,
        ILogger<IQApplicationVerifier> logger
    )
        : base(
            environmentInfo,
            new() { [ADRoles.AnyMiCorp] = _ => true },
            new()
            {
                [(IncomeViewerAdmin, MiCorp)] = (loanStatus) => true,
                [(DocViewerRoleIt, MiCorp)] = (loanStatus) => loanStatus < FinalSignoff,
                [(TeamPLSos, MiCorp)] = (loanStatus) => loanStatus < FinalSignoff,
                [(IVSolutionConsultant, MiCorp)] = (loanStatus) =>
                    loanStatus <= FolderReceived
                    || loanStatus.IsIn(Suspended, ApprovedByRocket, ReferredByRocket),
                [(IVBanking, MiCorp)] = (loanStatus) =>
                    loanStatus <= FolderReceived
                    || loanStatus.IsIn(ApprovedByRocket, ReferredByRocket, SubmittedToUnderwriting),
                [(IVClosing, MiCorp)] = (loanStatus) => loanStatus < Closed,
                [(IVUnderwriting, MiCorp)] = (loanStatus) => loanStatus < FinalSignoff,
            },
            logger
        )
    {
        if (features.IsFeatureEnabled(FeatureName.IQQCloudReadOnlyAccess))
        {
            _readAccessChecks[ADRoles.AnyQCloud] = _ => true;
        }
    }

    protected override string[] RocketLogicTrailWhiteList => RocketLogicTrailNames.IncomeGroups;

    public override string ApplicationId => Rules.ApplicationId.Income;
}
