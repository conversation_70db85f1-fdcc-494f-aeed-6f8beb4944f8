using LoanAccessAuthorizer.Domain;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using Microsoft.Extensions.Logging;
using static LoanAccessAuthorizer.Authorization.Rules.LoanStatus;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADDomain;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADRoleNames;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers;

public class CRMApplicationVerifier : RoleAccessApplicationVerifier
{
    public CRMApplicationVerifier(
        EnvironmentInfo environmentInfo,
        ILogger<CRMApplicationVerifier> logger
    )
        : base(
            environmentInfo,
            new() { [ADRoles.AnyMiCorp] = (_) => true },
            new()
            {
                [(TeamPLSos, MiCorp)] = (loanStatus) => loanStatus <= FinalSignoffPendingAction,
                [(IVBanking, MiCorp)] = (loanStatus) =>
                    loanStatus <= LoanSetupComplete
                    || loanStatus.IsIn(Suspended, ApprovedByRocket, ReferredByRocket),
                [(IVSolutionConsultant, MiCorp)] = (loanStatus) =>
                    loanStatus <= LoanSetupComplete
                    || loanStatus.IsIn(Suspended, ApprovedByRocket, ReferredByRocket),
                [(RLFreshStartConsultant, MiCorp)] = (loanStatus) =>
                    loanStatus <= LoanSetupComplete
                    || loanStatus.IsIn(ApprovedByRocket, ReferredByRocket),
                [(IVUnderwriting, MiCorp)] = (loanStatus) => loanStatus < FinalSignoff,
                [(IVClosing, MiCorp)] = (loanStatus) => loanStatus < Closed,
                [(DocViewerRoleIt, MiCorp)] = (loanStatus) => loanStatus < FinalSignoff,
            },
            logger
        ) { }

    public override string ApplicationId => Rules.ApplicationId.CRM;
}
