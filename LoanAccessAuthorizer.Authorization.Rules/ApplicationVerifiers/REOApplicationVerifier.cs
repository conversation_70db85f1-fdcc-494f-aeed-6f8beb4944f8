using LoanAccessAuthorizer.Domain;
using Microsoft.Extensions.Logging;
using static LoanAccessAuthorizer.Authorization.Rules.LoanStatus;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADDomain;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADRoleNames;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers;

public class REOApplicationVerifier : RoleAccessApplicationVerifier
{
    public REOApplicationVerifier(
        EnvironmentInfo environmentInfo,
        ILogger<REOApplicationVerifier> logger
    )
        : base(environmentInfo, AccessChecks, AccessChecks, logger) { }

    public override string ApplicationId => Rules.ApplicationId.REO;

    private static RoleLoanStatusCheckDictionary AccessChecks { get; } =
        new()
        {
            [(IncomeViewerAdmin, MiCorp)] = (loanStatus) => true,
            [(ITTeamTheLand, MiCorp)] = (loanStatus) => loanStatus is not null,
            [(PQAdmin, MiCorp)] = (loanStatus) => loanStatus is not null,
            [(DocViewerRoleIt, MiCorp)] = (loanStatus) => loanStatus < FinalSignoff,
            [(TeamPLSos, MiCorp)] = (loanStatus) => loanStatus < FinalSignoff,
            [(IVBanking, MiCorp)] = (loanStatus) =>
                loanStatus <= FolderReceived
                || loanStatus.IsIn(Suspended, ApprovedByRocket, ReferredByRocket),
            [(IVSolutionConsultant, MiCorp)] = (loanStatus) =>
                loanStatus <= FolderReceived
                || loanStatus.IsIn(Suspended, ApprovedByRocket, ReferredByRocket),
            [(IVClosing, MiCorp)] = (loanStatus) => loanStatus < Closed,
            [(IVUnderwriting, MiCorp)] = (loanStatus) => loanStatus < FinalSignoff,
        };
}
