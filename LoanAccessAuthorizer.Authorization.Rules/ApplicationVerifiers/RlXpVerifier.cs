using LoanAccessAuthorizer.Authorization.Rules.Authorization.Model;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using LoanAccessAuthorizer.Domain.TeamMemberData;
using LoanAccessAuthorizer.Domain.VerifierExclusions;
using Microsoft.Extensions.Logging;
using Serilog.Context;
using static LoanAccessAuthorizer.Authorization.Rules.LoanStatus;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADDomain;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADRoleNames;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers;

public class RlXpVerifier : RoleAccessApplicationVerifier
{
    private readonly BankerCheck _bankerCheck;
    private static readonly int[] _ivSolutionConsultantStatuses =
    {
        Suspended,
        ApprovedByRocket,
        ReferredByRocket,
    };

    private static readonly int[] _ivBankingWriteStatuses =
    {
        Suspended,
        ApprovedByRocket,
        ReferredByRocket,
        SubmittedToUnderwriting,
    };

    public RlXpVerifier(
        BankerCheck bankerCheck,
        EnvironmentInfo environmentInfo,
        ILogger<RlXpVerifier> logger
    )
        : base(
            environmentInfo,
            new()
            {
                [(IVSolutionConsultant, MiCorp)] = loanStatus =>
                    loanStatus <= FolderReceived || loanStatus.IsIn(_ivSolutionConsultantStatuses),
                [(IVClosing, MiCorp)] = loanStatus => loanStatus < Closed,
                [(IVUnderwriting, MiCorp)] = loanStatus => loanStatus < FinalSignoff,
                [(IncomeViewerAdmin, MiCorp)] = _ => true,
                [(TeamPLSos, MiCorp)] = loanStatus =>
                    loanStatus is null || loanStatus < FinalSignoff,
                [(IVBanking, MiCorp)] = _ => true,
                [(RLPropertyVerificationTeam, MiCorp)] = _ => true,
            },
            new()
            {
                [(IncomeViewerAdmin, MiCorp)] = _ => true,
                [(TeamPLSos, MiCorp)] = loanStatus =>
                    loanStatus is null || loanStatus < FinalSignoff,
                [(IVBanking, MiCorp)] = loanStatus =>
                    loanStatus is null
                    || loanStatus <= FolderReceived
                    || loanStatus.IsIn(_ivBankingWriteStatuses),
            },
            logger
        )
    {
        _bankerCheck = bankerCheck;
    }

    public override string ApplicationId => Rules.ApplicationId.RlXp;

    public override async Task<(bool, VerifierExclusion)> HasWriteAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
    {
        // First check if the loan is archived - this blocks all users from having write access
        var isLoanArchived = await accessContainer.StateContainer.Get<bool>(ExplicitStateProviderIds.IsLoanArchivedInAmp);
        if (isLoanArchived)
        {
            return (
                false,
                new VerifierExclusion
                {
                    Reason = VerifierExclusionReason.LoanIsArchived,
                    Comment = "Loan is archived.",
                }
            );
        }
        
        using var commonIdContext = LogContext.PushProperty("CommonId", user.CommonId);
        var isBankerTask = _bankerCheck.IsBanker(user.CommonId);
        var isSchwabBankerTask = _bankerCheck.IsSchwabBanker(user.CommonId);
        var isSchwabLoanTask = accessContainer.StateContainer.Get<bool>(
            ExplicitStateProviderIds.IsSchwabLoan
        );
        var isSchwabClientTask = accessContainer.StateContainer.Get<bool>(
            ExplicitStateProviderIds.IsSchwabClient
        );

        var isBanker = await isBankerTask;
        var isSchwabBanker = await isSchwabBankerTask;
        var isSchwabLoan = await isSchwabLoanTask;
        var isSchwabClient = await isSchwabClientTask;

        if (!isBanker)
        {
            _logger.LogInformation(
                "Common id {CommonId} is not a banker. Allowing access.",
                user.CommonId
            );
            return (true, null);
        }

        if (!isSchwabBanker && !isSchwabLoan && isSchwabClient)
        {
            return (
                false,
                new VerifierExclusion
                {
                    Reason = VerifierExclusionReason.SchwabClientOnRetailLoan,
                    Comment =
                        $"{user.CommonId} is not authorized to work a loan with a previous Schwab client.",
                }
            );
        }

        if (!isSchwabBanker && isSchwabLoan)
        {
            return (
                false,
                new VerifierExclusion
                {
                    Reason = VerifierExclusionReason.NotSchwabBanker,
                    Comment = $"{user.CommonId} is not authorized to work a Schwab loan.",
                }
            );
        }

        return await base.HasWriteAccess(user, inputData, accessContainer);
    }
}
