using LoanAccessAuthorizer.Domain;
using Microsoft.Extensions.Logging;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADDomain;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADRoleNames;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers;

public class ShortageApprovalApplicationVerifier : RoleAccessApplicationVerifier
{
    public ShortageApprovalApplicationVerifier(
        EnvironmentInfo environmentInfo,
        ILogger<ShortageApprovalApplicationVerifier> logger
    )
        : base(
            environmentInfo,
            new()
            {
                [(ITTeamEinstein, MiCorp)] = (loanStatus) => true,
                [(ViewTargetProfit, MiCorp)] = (loanStatus) => true,
                [(ShortageApproveLv2, MiCorp)] = (loanStatus) => true,
                [(ShortageApproveLv3, MiCorp)] = (loanStatus) => true,
                [(ShortageApproveLv4, MiCorp)] = (loanStatus) => true,
                [(ShortageApproveLv5, MiCorp)] = (loanStatus) => true,
                [(ShortageApproveCapMkt, MiCorp)] = (loanStatus) => true,
            },
            new(),
            logger
        ) { }

    public override string ApplicationId => Rules.ApplicationId.ShortageApproval;
}
