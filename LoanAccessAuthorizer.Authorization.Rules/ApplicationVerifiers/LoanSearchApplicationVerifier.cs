using LoanAccessAuthorizer.Authorization.Rules.Authorization.Model;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using LoanAccessAuthorizer.Domain.VerifierExclusions;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers;

public class LoanSearchApplicationVerifier : IApplicationVerifier
{
    public string ApplicationId => Rules.ApplicationId.LoanSearch;

    public Task<(bool, VerifierExclusion)> HasWriteAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
    {
        return Task.FromResult((false, new VerifierExclusion()));
    }

    public Task<(bool, VerifierExclusion)> HasReadAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
    {
        // TODO: Does this need to be locked down to MI Corp or can QCLOUD (Schwab externa users) have access?
        var hasAccess = (user.IsInMiCorpDomain());
        var reason = hasAccess ? null : new VerifierExclusion();
        return Task.FromResult((hasAccess, reason));
    }
}
