using LoanAccessAuthorizer.Authorization.Rules.Authorization.Model;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using LoanAccessAuthorizer.Domain.VerifierExclusions;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers;

public class ActionsApplicationVerifier : IApplicationVerifier
{
    public string ApplicationId => Rules.ApplicationId.Actions;

    public Task<(bool, VerifierExclusion)> HasWriteAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
    {
        // Defaulting to true because LAA returns true for write only when both read and write are true.
        return Task.FromResult((true, (VerifierExclusion)null));
    }

    public async Task<(bool, VerifierExclusion)> HasReadAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
    {
        if (!user.IsInMiCorpDomain())
        {
            return (
                false,
                new VerifierExclusion
                {
                    Reason = VerifierExclusionReason.NotInMiCorpDomain,
                    Comment = $"{user.CommonId} is not in MiCorpDomain"
                }
            );
        }

        var isLoanInAmp = await accessContainer.StateContainer.Get<bool>(
            ExplicitStateProviderIds.IsLoanInAmp
        );
        if (!isLoanInAmp)
        {
            return (
                false,
                new VerifierExclusion
                {
                    Reason = VerifierExclusionReason.NotInAmp,
                    Comment = "Loan is not in AMP"
                }
            );
        }

        var loanStatuses = await accessContainer.StateContainer.Get<AmpLoanStatusCollection>();
        if (
            loanStatuses?.Any(
                loanStatus => loanStatus.LoanStatusId == $"{LoanStatus.FolderReceived}"
            ) ?? false
        )
        {
            return (
                false,
                new VerifierExclusion
                {
                    Reason = VerifierExclusionReason.FolderReceived,
                    Comment = "Loan status: Folder Received"
                }
            );
        }

        var isOriginatedByRocketLogic = await accessContainer.StateContainer.Get<bool>(
            ExplicitStateProviderIds.IsLoanOriginatedByRLB
        );
        var reason = isOriginatedByRocketLogic ? null : new VerifierExclusion();
        return (isOriginatedByRocketLogic, reason);
    }
}
