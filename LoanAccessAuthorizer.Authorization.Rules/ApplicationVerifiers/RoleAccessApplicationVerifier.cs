using LoanAccessAuthorizer.Authorization.Rules.Authorization.Model;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using LoanAccessAuthorizer.Domain;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using LoanAccessAuthorizer.Domain.VerifierExclusions;
using Microsoft.Extensions.Logging;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADDomain;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADRoleNames;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers;

public abstract class RoleAccessApplicationVerifier : IApplicationVerifier
{
    public abstract string ApplicationId { get; }

    protected readonly ILogger<RoleAccessApplicationVerifier> _logger;

    protected virtual string[] RocketLogicTrailWhiteList => Array.Empty<string>();

    protected RoleLoanStatusCheckDictionary ReadAccessChecks
    {
        get => _readAccessChecks;
    }

    protected RoleLoanStatusCheckDictionary WriteAccessChecks
    {
        get => _writeAccessChecks;
    }

    protected RoleLoanStatusCheckDictionary _readAccessChecks;

    protected RoleLoanStatusCheckDictionary _writeAccessChecks;

    protected RoleAccessApplicationVerifier(
        EnvironmentInfo environmentInfo,
        RoleLoanStatusCheckDictionary readAccessChecks,
        RoleLoanStatusCheckDictionary writeAccessChecks,
        ILogger<RoleAccessApplicationVerifier> logger
    )
    {
        _logger = logger;

        _readAccessChecks = readAccessChecks;
        _writeAccessChecks = writeAccessChecks;

        var isBeta = environmentInfo.EnvironmentName == EnvironmentInfo.Beta;
        if (isBeta)
        {
            _readAccessChecks.TryAdd((RLPassport, MiCorp), _ => true);
            _writeAccessChecks.TryAdd((RLPassport, MiCorp), _ => true);
        }
    }

    public virtual Task<(bool, VerifierExclusion)> HasReadAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
    {
        if (user.IsInMiCorpDomain() && ShouldEnforceRLUFlow(inputData) && !HasRLUSupportRole(user))
        {
            var hasAccess = inputData.RocketLogicUnderwritingVisitedTrails.Overlaps(
                RocketLogicTrailWhiteList
            );
            if (!hasAccess)
            {
                _logger.LogInformation(
                    "Blocking access to {applicationId} since the RLUW {requiredTrails} have not been visited yet {visitedTrails}.",
                    ApplicationId,
                    RocketLogicTrailWhiteList,
                    inputData.RocketLogicUnderwritingVisitedTrails
                );
            }
            var reason = hasAccess ? null : new VerifierExclusion();
            return Task.FromResult((hasAccess, reason));
        }
        var hasReadAccess = HasReadAccess(user, inputData.LoanDetails);
        var readAccessReason = hasReadAccess ? null : new VerifierExclusion();
        return Task.FromResult((hasReadAccess, readAccessReason));
    }

    private static bool HasRoleAccess(
        ADUser user,
        AmpKeyLoanInfo loanDetails,
        RoleLoanStatusCheckDictionary rules
    )
    {
        var loanStatus = loanDetails?.CurrentLoanStatus;
        var roles = user.Roles;
        var matchedRules = rules.Keys.Where(ruleKey =>
            roles.Any(role =>
                ruleKey.Equals(role) || (ruleKey.RoleName is null && role.Domain == ruleKey.Domain)
            )
        );
        var hasAccess = matchedRules.Any(role => rules[role](loanStatus));
        return hasAccess;
    }

    protected virtual bool HasReadAccess(ADUser user, AmpKeyLoanInfo loanDetails)
    {
        return HasRoleAccess(user, loanDetails, ReadAccessChecks);
    }

    public virtual Task<(bool, VerifierExclusion)> HasWriteAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
    {
        var hasAccess = HasWriteAccess(user, inputData.LoanDetails);
        var reason = hasAccess ? null : new VerifierExclusion();
        return Task.FromResult((hasAccess, reason));
    }

    protected virtual bool HasWriteAccess(ADUser user, AmpKeyLoanInfo loanDetails)
    {
        return HasRoleAccess(user, loanDetails, WriteAccessChecks);
    }

    private bool ShouldEnforceRLUFlow(VerificationDecisionInputData inputData)
    {
        return (RocketLogicTrailWhiteList?.Any() ?? false)
            && inputData.RocketLogicUnderwritingVisitedTrails?.UnderwriteFinished == false
            && inputData.LoanDetails?.CurrentLoanStatus
                is >= LoanStatus.FolderReceived
                    and < LoanStatus.Suspended;
    }

    private static bool HasRLUSupportRole(ADUser user)
    {
        return user.HasMiCorpRole(ADRoleNames.RLUnderwritingSupport);
    }
}
