using LoanAccessAuthorizer.Authorization.Rules.Authorization.Model;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using LoanAccessAuthorizer.Domain.VerifierExclusions;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers.RocketLogicUnderwriting;

public class RocketLogicUnderwritingApplicationVerifier : IApplicationVerifier
{
    public string ApplicationId => Rules.ApplicationId.RocketLogicUnderwriting;

    private readonly IEnumerable<IRluSubApplicationVerifier> _subAppVerifiers;

    public RocketLogicUnderwritingApplicationVerifier(IEnumerable<IRluSubApplicationVerifier> subAppVerifiers)
    {
        _subAppVerifiers = subAppVerifiers;
    }

    public async Task<(bool, VerifierExclusion)> HasWriteAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
    {
        var hasAccessTasks = _subAppVerifiers.Select(appVerifier =>
            appVerifier.HasWriteAccess(user, inputData, accessContainer)
        );
        var hasAccess = await Task.WhenAll(hasAccessTasks);

        var arrayExists = Array.Exists(hasAccess, access => access.Item1);
        var reason = arrayExists ? null : new VerifierExclusion();
        return (arrayExists, reason);
    }
    public async Task<(bool, VerifierExclusion)> HasReadAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
    {
        var hasAccessTasks = _subAppVerifiers.Select(appVerifier =>
            appVerifier.HasReadAccess(user, inputData, accessContainer)
        );
        var hasAccess = await Task.WhenAll(hasAccessTasks);

        var arrayExists = Array.Exists(hasAccess, access => access.Item1);
        var reason = arrayExists ? null : new VerifierExclusion();
        return (arrayExists, reason);
    }
}
