using LoanAccessAuthorizer.Domain;
using Microsoft.Extensions.Logging;
using static LoanAccessAuthorizer.Authorization.Rules.LoanStatus;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADDomain;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADRoleNames;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers.RocketLogicUnderwriting;

public abstract class RocketLogicUWBaseApplicationVerifier
    : RoleAccessApplicationVerifier,
        IRluSubApplicationVerifier
{
    public RocketLogicUWBaseApplicationVerifier(
        EnvironmentInfo environmentInfo,
        ILogger<RocketLogicUWBaseApplicationVerifier> logger
    )
        : base(
            environmentInfo,
            new()
            {
                [(RLUnderwritingPilot, MiCorp)] = (loanStatus) => CommonStatusCheck(loanStatus),
                [(RLUnderwritingSupport, MiCorp)] = (loanStatus) => CommonStatusCheck(loanStatus),
            },
            new(),
            logger
        ) { }

    public abstract override string ApplicationId { get; }

    private static bool CommonStatusCheck(int? status) =>
        status is not null
        && status >= FolderReceived
        && status.IsNotIn(ApprovedByRocket, ReferredByRocket);
}
