namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers.RocketLogicUnderwriting;

public static class RocketLogicTrailNames
{
    public static readonly string[] AssetGroups = {
        "Asset",
        "Assets",
        "PurchaseAssets",
        "JumboAssets",
        "JumboPurchaseAssets",
        "FhaAssets",
        "FhaNcqStreamlineAssets",
        "FhaPurchaseAssets",
        "VaAssets",
        "VaNcqIrrrlAssets",
        "VaPurchaseAssets",
        "CesAssets",
        "TpoAssets",
        "TpoPurchaseAssets",
        "TpoJumboAssets",
        "TpoJumboPurchaseAssets",
        "TpoFhaAssets",
        "TpoFhaNcqStreamlineAssets",
        "TpoFhaPurchaseAssets",
        "TpoVaAssets",
        "TpoVaNcqIrrrlAssets",
        "TpoVaPurchaseAssets",
        "TpoCesAssets",
        "SchwabAssets",
        "SchwabPurchaseAssets"
    };
    public static readonly string[] CreditGroups = {
        "Credit",
        "PurchaseCredit",
        "JumboCredit",
        "JumboPurchaseCredit",
        "FhaCredit",
        "FhaNcqStreamlineCredit",
        "FhaPurchaseCredit",
        "VaCredit",
        "VaNcqIrrrlCredit",
        "VaPurchaseCredit",
        "CesCredit"
    };
    public static readonly string[] IncomeGroups = {
        "Income",
        "PurchaseIncome",
        "JumboIncome",
        "JumboPurchaseIncome",
        "FhaIncome",
        "FhaNcqStreamlineIncome",
        "FhaPurchaseIncome",
        "VaIncome",
        "VaNcqIrrrlIncome",
        "VaPurchaseIncome",
        "CesIncome",
        "TpoIncome",
        "TpoPurchaseIncome",
        "TpoJumboIncome",
        "TpoJumboPurchaseIncome",
        "TpoFhaIncome",
        "TpoFhaPurchaseIncome",
        "TpoFhaNcqStreamlineIncome",
        "TpoVaIncome",
        "TpoVaNcqIrrrlIncome",
        "TpoVaPurchaseIncome",
        "TpoCesIncome",
        "SchwabIncome",
        "SchwabPurchaseIncome"
    };
}
