using LoanAccessAuthorizer.Domain;
using Microsoft.Extensions.Logging;
using static LoanAccessAuthorizer.Authorization.Rules.LoanStatus;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADDomain;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADRoleNames;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers.RocketLogicUnderwriting;

public class RocketLogicUWCondoApplicationVerifier
    : RoleAccessApplicationVerifier,
        IRluSubApplicationVerifier
{
    public RocketLogicUWCondoApplicationVerifier(
        EnvironmentInfo environmentInfo,
        ILogger<RocketLogicUWCondoApplicationVerifier> logger
    )
        : base(
            environmentInfo,
            new()
            {
                [(RLUnderwritingCondo, MiCorp)] = (loanStatus) => CommonStatusCheck(loanStatus),
                [(RLUnderwritingSupport, MiCorp)] = (loanStatus) => CommonStatusCheck(loanStatus),
            },
            new(),
            logger
        ) { }

    public override string ApplicationId => Rules.ApplicationId.RocketLogicUnderwritingCondo;

    private static bool CommonStatusCheck(int? status) =>
        status is not null && status.IsNotIn(ApprovedByRocket, ReferredByRocket);
}
