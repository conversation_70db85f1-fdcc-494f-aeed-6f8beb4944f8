using LoanAccessAuthorizer.Domain;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers.RocketLogicUnderwriting;

public class RocketLogicUWCreditApplicationVerifier : RocketLogicUWBaseApplicationVerifier
{
    public RocketLogicUWCreditApplicationVerifier(
        EnvironmentInfo environmentInfo,
        ILogger<RocketLogicUWCreditApplicationVerifier> logger
    )
        : base(environmentInfo, logger) { }

    public override string ApplicationId => Rules.ApplicationId.RocketLogicUnderwritingCredit;
}
