using LoanAccessAuthorizer.Domain;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers.RocketLogicUnderwriting;

public class RocketLogicUWTpoApplicationVerifier : RocketLogicUWBaseApplicationVerifier
{
    public RocketLogicUWTpoApplicationVerifier(
        EnvironmentInfo environmentInfo,
        ILogger<RocketLogicUWTpoApplicationVerifier> logger
    )
        : base(environmentInfo, logger) { }

    public override string ApplicationId => Rules.ApplicationId.RocketLogicUnderwritingTpo;
}
