using LoanAccessAuthorizer.Domain;
using Microsoft.Extensions.Logging;
using static LoanAccessAuthorizer.Authorization.Rules.LoanStatus;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADDomain;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADRoleNames;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers;

public class RocketLogicBankingApplicationVerifier : RoleAccessApplicationVerifier
{
    private static readonly int[] _ivSolutionConsultantStatuses =
    {
        Suspended,
        ApprovedByRocket,
        ReferredByRocket,
    };

    private static readonly int[] _ivBankingWriteStatuses =
    {
        Suspended,
        ApprovedByRocket,
        ReferredByRocket,
        SubmittedToUnderwriting,
    };

    public RocketLogicBankingApplicationVerifier(
        EnvironmentInfo environmentInfo,
        ILogger<RocketLogicBankingApplicationVerifier> logger
    )
        : base(
            environmentInfo,
            new()
            {
                [(IVSolutionConsultant, MiCorp)] = loanStatus =>
                    loanStatus <= FolderReceived || loanStatus.IsIn(_ivSolutionConsultantStatuses),
                [(IVClosing, MiCorp)] = loanStatus => loanStatus < Closed,
                [(IVUnderwriting, MiCorp)] = loanStatus => loanStatus < FinalSignoff,
                [(IncomeViewerAdmin, MiCorp)] = _ => true,
                [(TeamPLSos, MiCorp)] = loanStatus =>
                    loanStatus is null || loanStatus < FinalSignoff,
                [(IVBanking, MiCorp)] = _ => true,
                [(RLPropertyVerificationTeam, MiCorp)] = _ => true,
            },
            new()
            {
                [(IncomeViewerAdmin, MiCorp)] = _ => true,
                [(TeamPLSos, MiCorp)] = loanStatus =>
                    loanStatus is null || loanStatus < FinalSignoff,
                [(IVBanking, MiCorp)] = loanStatus =>
                    loanStatus is null
                    || loanStatus <= FolderReceived
                    || loanStatus.IsIn(_ivBankingWriteStatuses),
            },
            logger
        ) { }

    public override string ApplicationId => Rules.ApplicationId.RocketLogicBanking;
}
