using LoanAccessAuthorizer.Domain;
using Microsoft.Extensions.Logging;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADDomain;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADRoleNames;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers;

public class RFOverrideApplicationVerifier : RoleAccessApplicationVerifier
{
    public RFOverrideApplicationVerifier(
        EnvironmentInfo environmentInfo,
        ILogger<RFOverrideApplicationVerifier> logger
    )
        : base(environmentInfo, _accessChecks, _accessChecks, logger) { }

    public override string ApplicationId => Rules.ApplicationId.RFOverride;

    private static readonly RoleLoanStatusCheckDictionary _accessChecks =
        new()
        {
            [(TeamPLSos, MiCorp)] = (loanStatus) => true,
            [(RFSuperAdmin, MiCorp)] = (loanStatus) => true,
        };
}
