using LoanAccessAuthorizer.Domain;
using Microsoft.Extensions.Logging;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADDomain;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADRoleNames;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers;

public class DVApplicationVerifier : RoleAccessApplicationVerifier
{
    public DVApplicationVerifier(
        EnvironmentInfo environmentInfo,
        ILogger<DVApplicationVerifier> logger
    )
        : base(environmentInfo, AccessChecks, AccessChecks, logger) { }

    public override string ApplicationId => Rules.ApplicationId.DV;

    private static RoleLoanStatusCheckDictionary AccessChecks { get; } =
        new()
        {
            [(DocViewerRoleBanker, MiCorp)] = (loanStatus) => true,
            [(DocViewerRoleIt, MiCorp)] = (loanStatus) => true,
            [(IVClosing, MiCorp)] = (loanStatus) => true,
            [(IVSolutionConsultant, MiCorp)] = (loanStatus) => true,
            [(IVUnderwriting, MiCorp)] = (loanStatus) => true,
            [(IncomeViewerAdmin, MiCorp)] = (loanStatus) => true,
            [(TeamPLSos, MiCorp)] = (loanStatus) => true,
            [(EmAdmin, MiCorp)] = (loanStatus) => true,
            [(DocViewerRoleCapitalMarketsReadOnly, MiCorp)] = (loanStatus) => true,
        };
}
