using LoanAccessAuthorizer.Authorization.Rules.Authorization.Model;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using LoanAccessAuthorizer.Domain;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using LoanAccessAuthorizer.Domain.VerifierExclusions;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers;

public class OverrideEligibilityStoppersApplicationVerifier : IApplicationVerifier
{
    private readonly string _readAccessRole;
    private readonly string _writeAccessRole;

    public string ApplicationId => Rules.ApplicationId.OverrideEligibilityStoppers;

    public OverrideEligibilityStoppersApplicationVerifier(EnvironmentInfo env)
    {
        _readAccessRole = ADRoleNames.OverrideEligibilityStopperReadOnly;
        _writeAccessRole = string.Equals(
            env.EnvironmentName,
            EnvironmentInfo.Prod,
            StringComparison.CurrentCultureIgnoreCase
        )
            ? ADRoleNames.OverrideEligibilityStopper
            : ADRoleNames.NonProdOverrideEligibilityStopper;
    }

    public Task<(bool, VerifierExclusion)> HasWriteAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
    {
        var hasRole = user.HasMiCorpRole(_writeAccessRole);
        return Task.FromResult((hasRole, new VerifierExclusion()));
    }

    public Task<(bool, VerifierExclusion)> HasReadAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
    {
        var hasRole = user.HasMiCorpRole(_writeAccessRole) || user.HasMiCorpRole(_readAccessRole);
        return Task.FromResult((hasRole, new VerifierExclusion()));
    }
}
