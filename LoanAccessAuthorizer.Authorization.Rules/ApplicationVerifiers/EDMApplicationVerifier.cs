using LoanAccessAuthorizer.Authorization.Rules.Features;
using LoanAccessAuthorizer.Domain;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using Microsoft.Extensions.Logging;
using static LoanAccessAuthorizer.Authorization.Rules.LoanStatus;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADDomain;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADRoleNames;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers;

public class EDMApplicationVerifier : RoleAccessApplicationVerifier
{
    public EDMApplicationVerifier(
        IFeatureEnabledConfigProvider features,
        EnvironmentInfo environmentInfo,
        ILogger<EDMApplicationVerifier> logger
    )
        : base(
            environmentInfo,
            new RoleLoanStatusCheckDictionary() { [ADRoles.AnyMiCorp] = _ => true },
            new()
            {
                [(IncomeViewerAdmin, MiCorp)] = (_) => true,
                [(IVVendor, MiCorp)] = (loanStatus) => loanStatus is not null,
                [(DocViewerRoleIt, MiCorp)] = (loanStatus) => loanStatus < FinalSignoff,
                [(TeamPLSos, MiCorp)] = (loanStatus) => loanStatus < FinalSignoff,
                [(IVBanking, MiCorp)] = (loanStatus) =>
                    loanStatus <= FolderReceived
                    || loanStatus.IsIn(BankerWriteAllowedStatusesAfterFolder),
                [(IVClosing, MiCorp)] = (loanStatus) => loanStatus < Closed,
                [(IVUnderwriting, MiCorp)] = (loanStatus) => loanStatus < FinalSignoff,
                [(IVSolutionConsultant, MiCorp)] = (loanStatus) =>
                    loanStatus <= FolderReceived
                    || loanStatus.IsIn(SolutionConsultantWriteAllowedStatusesAfterFolder),
            },
            logger
        )
    {
        if (features.IsFeatureEnabled(FeatureName.EDMQCloudReadOnlyAccess))
        {
            _readAccessChecks[ADRoles.AnyQCloud] = _ => true;
        }
    }

    public override string ApplicationId => Rules.ApplicationId.EDM;

    private static readonly int[] BankerWriteAllowedStatusesAfterFolder = new[]
    {
        Suspended,
        ApprovedByRocket,
        ReferredByRocket,
        ApprovedPendingClientConditionsOnProperty,
    };

    private static readonly int[] SolutionConsultantWriteAllowedStatusesAfterFolder = new[]
    {
        Suspended,
        ApprovedByRocket,
        ReferredByRocket,
        ApprovedPendingClientConditionsOnProperty,
    };
}
