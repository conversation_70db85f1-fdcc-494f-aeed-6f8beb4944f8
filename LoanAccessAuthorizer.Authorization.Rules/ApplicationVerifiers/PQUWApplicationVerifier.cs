using LoanAccessAuthorizer.Domain;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using Microsoft.Extensions.Logging;
using static LoanAccessAuthorizer.Authorization.Rules.LoanStatus;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADDomain;
using static LoanAccessAuthorizer.Domain.ActiveDirectory.ADRoleNames;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers;

public class PQUWApplicationVerifier : RoleAccessApplicationVerifier
{
    public PQUWApplicationVerifier(
        EnvironmentInfo environmentInfo,
        ILogger<PQUWApplicationVerifier> logger
    )
        : base(
            environmentInfo,
            new() { [ADRoles.AnyMiCorp] = (LoanStatus) => true },
            new()
            {
                [(ITTeamTheLand, MiCorp)] = (loanStatus) => loanStatus is not null,
                [(PQAdmin, MiCorp)] = (loanStatus) => loanStatus is not null,
                [(PQCU<PERSON>, MiCorp)] = (loanStatus) => loanStatus < FinalSignoff,
            },
            logger
        ) { }

    public override string ApplicationId => Rules.ApplicationId.PropertyUnderwriting;
}
