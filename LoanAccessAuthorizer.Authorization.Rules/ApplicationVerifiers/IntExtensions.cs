namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers;

public static class IntExtensions
{
    public static bool IsIn(this int? number, params int[] numbers)
    {
        return number is not null && numbers.Contains(number.Value);
    }

    public static bool IsNotIn(this int? number, params int[] numbers)
    {
        return number is null || !numbers.Contains(number.Value);
    }
}
