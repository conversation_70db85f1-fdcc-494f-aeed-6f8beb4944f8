using System.Runtime.CompilerServices;
using LoanAccessAuthorizer.Authorization.Rules.Authorization.Model;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using LoanAccessAuthorizer.Domain.Models.Response;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;
using LoanAccessAuthorizer.Domain.TeamMemberData;
using LoanAccessAuthorizer.Exclusions;
using LoanAccessAuthorizer.RocketLogicApi.Models;
using Microsoft.Extensions.Logging;
using Serilog.Context;
using LoanAccessAuthorizer.Domain.VerifierExclusions;

[assembly: InternalsVisibleTo("UnitTests.LoanAccessAuthorizer.Authorization.Rules")]

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers;

public class AMPApplicationVerifier : IApplicationVerifier
{
    public string ApplicationId => Rules.ApplicationId.AMP;

    // List of statuses that should short circuit/exclude from RL and unlock AMP to users
    internal static readonly ICollection<string> _ampAutoUnlockStatuses = new HashSet<string>
    {
        LoanStatus.LoggedIntoAccounting.ToString()
    };

    // All ApplicationIDs that should be used to determine if Amp is locked out for this loan
    internal static readonly IEnumerable<string> RocketLogicApplicationIds = new[]
    {
        Rules.ApplicationId.RCLockout,
        Rules.ApplicationId.MCNonBorrowingSpouse,
        Rules.ApplicationId.MCNonBorrowingTitleHolder,
        Rules.ApplicationId.MCVA,
        Rules.ApplicationId.MCClientChoosingTitle,
        Rules.ApplicationId.RocketOutOfAMP,
        Rules.ApplicationId.REO,
        Rules.ApplicationId.PersonalInformation,
        Rules.ApplicationId.PersonalInformationVeteransAffairs,
        Rules.ApplicationId.MCAddress,
        Rules.ApplicationId.MortgageClient,
        Rules.ApplicationId.Demographics,
        Rules.ApplicationId.Asset,
        Rules.ApplicationId.Credit,
        Rules.ApplicationId.CRM,
        Rules.ApplicationId.CQAlimonyChildSupport,
        Rules.ApplicationId.PropertyTaxesPaidInFull,
        Rules.ApplicationId.ActionsAdjustAppDepositAmount,
        Rules.ApplicationId.TitleLeaseholdSelected,
        Rules.ApplicationId.RentalIncome,
        Rules.ApplicationId.AmpLockout
    };

    internal static readonly IEnumerable<string> PurchaseRocketLogicApplicationIds =
        RocketLogicApplicationIds
            .Concat(
                new[] { Rules.ApplicationId.PalValPrinting, Rules.ApplicationId.PurchaseNoAddress }
            )
            .ToArray();

    private readonly IRocketLogicBankingAuthorizer _rocketLogicBankingAuthorizer;
    private readonly IAmpAuthorizer _ampAuthorizer;
    private readonly IAppDepositFolderingAuthorizer _appDepositFolderingAuthorizer;
    private readonly BankerCheck _bankerCheck;
    private readonly IExclusionsRepository _exclusionsRepo;    
    private readonly ILogger<AMPApplicationVerifier> _logger;

    public AMPApplicationVerifier(
        IRocketLogicBankingAuthorizer rocketLogicBankingAuthorizer,
        IAmpAuthorizer ampAuthorizer,
        IAppDepositFolderingAuthorizer appDepositFolderingAuthorizer,
        BankerCheck bankerCheck,
        IExclusionsRepository exclusionsRepo,
        ILogger<AMPApplicationVerifier> logger
    )
    {
        _rocketLogicBankingAuthorizer = rocketLogicBankingAuthorizer;
        _ampAuthorizer = ampAuthorizer;
        _appDepositFolderingAuthorizer = appDepositFolderingAuthorizer;
        _bankerCheck = bankerCheck;
        _exclusionsRepo = exclusionsRepo;
        _logger = logger;
    }

    public async Task<(bool, VerifierExclusion)> HasReadAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
    {
        ArgumentNullException.ThrowIfNull(user);
        // bool hasAccess;
        using var loanIdentifierContext = LogContext.PushProperty(
            "LoanIdentifier",
            inputData.LoanDetails.LoanNumber
        );
        using var commonIdContext = LogContext.PushProperty("CommonId", user.CommonId);

        if (user.IsInQCloudDomain())
        {
            _logger.LogInformation("Allowing AMP access for QCloud user.");
            return (true, null);
        }

        if (await IsAmpUnlockedByStatus(accessContainer))
        {
            _logger.LogInformation("Allowing AMP access based on AMP Loan Status");
            return (true, null);
        }

        var commonId = user.CommonId;

        if (!await _rocketLogicBankingAuthorizer.IsInPilot(user, accessContainer))
        {
            _logger.LogInformation("User is not in the RLB pilot - allow access to AMP");
            return (true, null);
        }

        var isOrignatedByRLB = await accessContainer.StateContainer.Get<bool>(
            ExplicitStateProviderIds.IsLoanOriginatedByRLB
        );
        if (!isOrignatedByRLB)
        {
            _logger.LogInformation("Origination system is not RLB - allow access to AMP");
            return (true, null);
        }

        var rlApiLoanDetails = await accessContainer.StateContainer.Get<RocketLogicLoanDetails>();

        var isInAmpLockoutPilot = await _ampAuthorizer.IsInPilot(commonId, accessContainer);
        if (isInAmpLockoutPilot && rlApiLoanDetails is not null)
        {
            _logger.LogInformation("Loan is in AMP lockout pilot. Checking Access Deciders");
            var checkRocketLogicAccessDeciders = await CheckRocketLogicAccessDeciders(
                accessContainer,
                rlApiLoanDetails,
                commonId
            );
            var decidersReason = checkRocketLogicAccessDeciders ? null : new VerifierExclusion();
            return (checkRocketLogicAccessDeciders, decidersReason);
        }

        if (!await _bankerCheck.IsBanker(commonId, true))
        {
            _logger.LogInformation("User is not a banker - allow access to AMP");
            return (true, null);
        }

        var isImportedByRocketLogicBanking = await IsImportedByRocketLogicBanking(accessContainer);
        var reason = isImportedByRocketLogicBanking ? null : new VerifierExclusion();
        return (isImportedByRocketLogicBanking, reason);
    }

    private async Task<bool> IsImportedByRocketLogicBanking(AccessDecisionContainer accessContainer)
    {
        var isInModsPilotTask = accessContainer.GetAccessDecision(Rules.ApplicationId.MODS);
        var rcLockoutTask = accessContainer.GetAccessDecision(Rules.ApplicationId.RCLockout);
        var isInModsPilot = await isInModsPilotTask;
        var isInRcLockout = await rcLockoutTask;

        if (isInModsPilot.AccessDecision && isInRcLockout.AccessDecision)
        {
            var isProductSelected = await accessContainer.StateContainer.Get<bool>(
                ExplicitStateProviderIds.IsLoanInRocketChoice
            );
            _logger.LogInformation(
                "Loan is in MODS Pilot - {isProductSelected}",
                isProductSelected
            );
            return isProductSelected;
        }

        _logger.LogInformation("Loan is imported by RocketLogic Banker");
        return true;
    }

    private async Task<bool> CheckRocketLogicAccessDeciders(
        AccessDecisionContainer accessContainer,
        RocketLogicLoanDetails rlbLoanDetails,
        int commonId
    )
    {
        if (await IsPastLoanStatusCutoff(commonId, accessContainer))
        {
            return true;
        }

        var appIds = rlbLoanDetails.LoanPurpose switch
        {
            LoanPurpose.Refinance => RocketLogicApplicationIds,
            LoanPurpose.Purchase => PurchaseRocketLogicApplicationIds,
            _
                => throw new Exception(
                    $"Loan Purpose '{rlbLoanDetails.LoanPurpose}' on {nameof(RocketLogicLoanDetails)} is invalid"
                )
        };

        var tasks = appIds.Select(
            async appId => (appId, accessDecision: await GetPilotAccessDecision(appId, accessContainer))
        );
        var rocketLogicAccessDecisions = await Task.WhenAll(tasks);

        _logger.LogInformation(
            "Rocket Logic Access decisions for AMP lockout {@accessDecisions}",
            rocketLogicAccessDecisions
        );

        // Allow access to Amp if any access decision is false
        if (rocketLogicAccessDecisions.Any(item => !item.accessDecision.AccessDecision))
        {
            _logger.LogInformation("Access decider return false - allowing access to AMP");
            return true;
        }

        var bankerCheck = !await _bankerCheck.IsBanker(commonId, true);
        _logger.LogInformation(
            "Amp Banker Check returned {BankerCheck} - Access to AMP {AmpAccess}",
            !bankerCheck,
            bankerCheck
        );
        return bankerCheck;
    }

    private Task<AccessResult> GetPilotAccessDecision(string appId, AccessDecisionContainer accessContainer)
    {
        switch (appId)
        {
            case Rules.ApplicationId.RocketOutOfAMP:
                return GetRoaAccessDecision(accessContainer);
            default:
                return accessContainer.GetAccessDecision(appId);
        }
    }

    private async Task<bool> IsAmpUnlockedByStatus(AccessDecisionContainer accessContainer)
    {
        var loanStatuses = await accessContainer.StateContainer.Get<AmpLoanStatusCollection>();

        // Allow access to Amp once the loan reaches the status cut off
        var foundExclusionStatus = loanStatuses?.FirstOrDefault(
            loanStatus => _ampAutoUnlockStatuses.Contains(loanStatus.LoanStatusId)
        );
        if (foundExclusionStatus == null)
        {
            return false;
        }

        _logger.LogInformation(
            "Loan has status {ampLockoutExclusionStatus} - allowing access to AMP",
            foundExclusionStatus
        );
        return true;
    }

    private async Task<bool> IsPastLoanStatusCutoff(
        int commonId,
        AccessDecisionContainer accessContainer
    )
    {
        var loanStatusCutoffTask = GetLoanStatusCutoff(commonId, accessContainer);
        var loanStatusesTask = accessContainer.StateContainer.Get<AmpLoanStatusCollection>();

        var loanStatusCutoff = (await loanStatusCutoffTask).ToString();
        var loanStatuses = await loanStatusesTask;

        _logger.LogInformation(
            "Using {loanStatusCutoff} to determine Amp access",
            loanStatusCutoff
        );

        // Allow access to Amp once the loan reaches the status cut off
        if (loanStatuses?.Any(loanStatus => loanStatus.LoanStatusId == loanStatusCutoff) ?? false)
        {
            _logger.LogInformation(
                "Loan has status {loanStatusCutoff} - allowing access to AMP",
                loanStatusCutoff
            );
            return true;
        }

        return false;
    }

    private async Task<AccessResult> GetRoaAccessDecision(AccessDecisionContainer accessContainer)
    {
        // Loans like CES/VA IRRRL/HELOCs are are manual underwriting only. They do not get rocketed in AMP and 
        // will never be rocketed in RL. The ROA access decider should not be a deciding factor for 
        // AMP lockout when a loan has a ManualUnderwriting exclusion.
        // returns true if out of pilot and has a ManualUnderwriting exclusion, else return ROA decision
        var accessDecision = await accessContainer.GetAccessDecision(Rules.ApplicationId.RocketOutOfAMP);
        if (accessDecision.AccessDecision)
        {
            return accessDecision;
        }
        var initialLoanState = await accessContainer.StateContainer.Get<InitialLoanState>();
        var exclusionInfo = await _exclusionsRepo.GetExclusionsForApplication(initialLoanState.LoanNumber, Rules.ApplicationId.RocketOutOfAMP);
        if (exclusionInfo.Exclusions.Any(exclusion => exclusion.Reason == ExclusionReason.ManualUnderwriting)) {
            _logger.LogInformation("Roa exclusions contain ManualUnderwriting reason {ROAExclusions}", exclusionInfo.Exclusions);
            return new AccessResult(true);
        }
        return accessDecision;
    }

    private async Task<int> GetLoanStatusCutoff(
        int commonId,
        AccessDecisionContainer accessContainer
    )
    {
        var (isInAppDepositFolderingPilot, _) =
            await _appDepositFolderingAuthorizer.IsInPilotWithLoanData(commonId, accessContainer);
        return isInAppDepositFolderingPilot ? LoanStatus.FolderReceived : LoanStatus.Application;
    }

    public Task<(bool, VerifierExclusion)> HasWriteAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
    {
        // Default allow write since what LAA returns for writes only returns true if read && write
        return Task.FromResult((true, (VerifierExclusion)null));
    }
}
