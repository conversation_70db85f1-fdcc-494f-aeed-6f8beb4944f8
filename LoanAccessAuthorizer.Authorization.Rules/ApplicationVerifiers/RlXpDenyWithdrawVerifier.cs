using LoanAccessAuthorizer.Authorization.Rules.Authorization.Model;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using LoanAccessAuthorizer.Domain.VerifierExclusions;
using static LoanAccessAuthorizer.Authorization.Rules.LoanStatus;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers;

public class RlXpDenyWithdrawVerifier : IApplicationVerifier
{
    public string ApplicationId => Rules.ApplicationId.RlXpDenyWithdrawVerifier;

    public async Task<(bool, VerifierExclusion)> HasWriteAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    )
    {
        try
        {
            var loanStatuses = await accessContainer.StateContainer.Get<AmpLoanStatusCollection>();
            if (
                loanStatuses?.Any(status => int.Parse(status.LoanStatusId) == FolderReceived)
                ?? false
            )
            {
                return (
                    false,
                    new VerifierExclusion
                    {
                        Reason = VerifierExclusionReason.FolderReceived,
                        Comment = "Loan status: Folder Received"
                    }
                );
            }

            return (true, null);
        }
        catch
        {
            return (true, null);
        }
    }

    public Task<(bool, VerifierExclusion)> HasReadAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer
    ) => Task.FromResult((true, (VerifierExclusion)null));
}
