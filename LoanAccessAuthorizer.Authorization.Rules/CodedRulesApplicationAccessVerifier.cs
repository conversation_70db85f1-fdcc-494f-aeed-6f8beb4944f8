using LoanAccessAuthorizer.Authorization.Rules.ApplicationVerifiers;
using LoanAccessAuthorizer.Authorization.Rules.Authorization;
using LoanAccessAuthorizer.Authorization.Rules.Authorization.Model;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.Extensions;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using LoanAccessAuthorizer.Domain.Models.Response;
using LoanAccessAuthorizer.Domain.VerifierExclusions;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules;

public class CodedRulesApplicationAccessVerifier : IAccessVerifier
{
    private readonly Dictionary<string, IApplicationVerifier> applicationVerifiers;
    private readonly ILogger<CodedRulesApplicationAccessVerifier> logger;
    private static readonly DateTime QualifierLockOutStartDate = new(2023, 06, 29);

    public CodedRulesApplicationAccessVerifier(IEnumerable<IApplicationVerifier> applicationVerifiers,
        ILogger<CodedRulesApplicationAccessVerifier> logger)
    {
        this.logger = logger;

        try
        {
            this.applicationVerifiers =
                applicationVerifiers.ToDictionary(verifier => verifier.ApplicationId.ToLower());
        }
        catch (ArgumentException exception)
        {
            throw new ApplicationVerifierConfigurationException("There cannot be 2 application verifiers with the same id.", exception);
        }
    }

    public async Task<ApplicationAuthorizationResponse> VerifyAccess(
        ADUser user,
        VerificationDecisionInputData inputData,
        string appId,
        AccessDecisionContainer accessContainer
    )
    {
        var authorizationResponse = new ApplicationAuthorizationResponse(false, false);

        if (applicationVerifiers.TryGetValue(appId.ToLower(), out var verifier))
        {
            var writeAccessTask = verifier.HasWriteAccess(user, inputData, accessContainer);
            var readAccessTask = verifier.HasReadAccess(user, inputData, accessContainer);
            var (hasWriteAccess, writeAccessExclusion) = await writeAccessTask;
            var (hasReadAccess, readAccessExclusion) = await readAccessTask;

            VerifierExclusion exclusion = null;
            if (!hasReadAccess)
            {
                exclusion = readAccessExclusion;
            }
            else if (!hasWriteAccess)
            {
                exclusion = writeAccessExclusion;
            }

            authorizationResponse = new ApplicationAuthorizationResponse(hasReadAccess, hasWriteAccess, exclusion);
        }
        else
        {
            logger.LogInformation($"No application verifier found for appId '{appId}', returning no access", appId);
            return authorizationResponse;
        }

        if (RocketLogicUIApplicationIds.Ids.Contains(appId) && authorizationResponse.Read)
        {
            if (await ImportedBeforeLockoutStartDate(accessContainer))
            {
                return authorizationResponse;
            }

            var inAmp = await accessContainer.StateContainer.Get<bool>(ExplicitStateProviderIds.IsLoanInAmp);
            if (inAmp)
            {
                var originatedByRlbTask = accessContainer.StateContainer.Get<bool>(ExplicitStateProviderIds.IsLoanOriginatedByRLB);
                var isRlbTask = accessContainer.StateContainer.IsRocketLogicBankingLoan();

                var originatedByRlb = await originatedByRlbTask;
                var (isRlbLoan, _) = await isRlbTask;

                if (!originatedByRlb || !isRlbLoan)
                {
                    //Don't want to block non-RLB loans from using qualifiers
                    return authorizationResponse;
                }
            }

            var leadMetadata = await accessContainer.StateContainer.Get<RocketLogicBankingLeadDetails>();
            var RlbCompletedInRlb = leadMetadata?.RlbCompleted ?? true;

            authorizationResponse.Read = RlbCompletedInRlb;

            logger.LogInformation(
                "RLB returned {RlbCompleted} for if trails are completed, returning {access} for read access for application {RocketLogicUIApplication}",
                RlbCompletedInRlb,
                authorizationResponse.Read,
                appId);
        }

        return authorizationResponse;
    }

    private static async Task<bool> ImportedBeforeLockoutStartDate(AccessDecisionContainer accessContainer)
    {
        try
        {
            var initialContactDate = await accessContainer.StateContainer.Get<DateTime>(ExplicitStateProviderIds.ApplicationOrInitialContactDate);
            //This is temporary and only to support legacy loans that do not have RlbCompleted set already
            return initialContactDate < QualifierLockOutStartDate;
        }
        catch (Exception)
        {
            return true;
        }
    }
}
