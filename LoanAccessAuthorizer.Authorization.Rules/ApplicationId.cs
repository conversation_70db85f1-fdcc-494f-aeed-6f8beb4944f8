namespace LoanAccessAuthorizer.Authorization.Rules;

using System.Reflection;

public static class ApplicationId
{
    public static readonly string[] All = GetAll();
    public const string Asset = "AQ";
    public const string ConditionManager = "cm";
    public const string Credit = "CQ";
    public const string CQAlimonyChildSupport = "cqacs";
    public const string Demographics = "Demographics";
    public const string DI = "di";
    public const string RocketDocs = "RocketDocs";
    public const string EDM = "EDM";
    public const string Einstein = "einstein";
    public const string Income = "iq";
    public const string LoanSearch = "ls";
    public const string MortgageClient = "MC";
    public const string Property = "PQ";
    public const string PropertyUnderwriting = "PQUW";
    public const string RocketLogicBanking = "rlb";
    public const string RLBBackHalf = "RLBBackHalf";
    public const string RLBTaskBased = "RLBTaskBased";
    public const string RLBCRM = "RLBCRM";
    public const string RLBSoftCredit = "RLBSoftCredit";
    public const string Actions = "Actions";
    public const string AppDepositFoldering = "AppDepositFoldering";
    public const string ActionsAdjustAppDepositAmount = "ActionsAdjustAppDepositAmount";

    // Covers the main app because "rluw" was already in use by AMP
    public const string RocketLogicUnderwriting = "RocketLogicUnderwriting";
    public const string RocketLogicUnderwritingCredit = "rluw";
    public const string RocketLogicUnderwritingCondo = "RlUnderwritingCondo";
    public const string RocketLogicUnderwritingSchwab = "RlUnderwritingSchwab";
    public const string RocketLogicUnderwritingTpo = "RlUnderwritingTpo";
    public const string LouisDocs = "LouisDocs";
    public const string Tasks = "Tasks";
    public const string REO = "reo";
    public const string ReoImport = "ReoImport";
    public const string ReoTpoPreRegistration = "ReoTpoPreRegistration";
    public const string LOLA = "LOLA";
    public const string AMP = "AMP";
    public const string AssetConditionProvider = "ACP";
    public const string RequiredAssetsCalculator = "RAC";
    public const string MODS = "mods";
    public const string PersonalInformation = "PI";
    public const string PricingElasticity = "PE";
    public const string AppraisalOrderManagement = "AppraisalOrderMgmt";
    public const string MCAddress = "MCAddress";
    public const string MCClientChoosingTitle = "MCClientChoosingTitle";
    public const string MCClientChoosingTitlePilot = "MCClientChoosingTitlePilot";
    public const string MCNonBorrowingTitleHolderPilot = "MCNonBorrowingTitleHolderPilot";
    public const string MCNonBorrowingSpouse = "MCNonBorrowingSpouse";
    public const string MCNonBorrowingTitleHolder = "MCNonBorrowingTitleHolder";
    public const string MCMaritalRelationship = "MCMaritalRelationship";
    public const string MCVA = "MCVA";
    public const string MCV3 = "MCV3";
    public const string DV = "DV";
    public const string BankerPresentationTool = "BPT";
    public const string TaskDndLockout = "TaskDndLockout";
    public const string TaskManagerBlocksFolderReceived = "TaskManagerBlocksFolderReceived";
    public const string RLToolbox = "RLToolbox";
    public const string RocketOutOfAMP = "ROA";
    public const string PersonalInformationVeteransAffairs = "PIVA";
    public const string EMPreventDelete = "EMPreventDelete";
    public const string DocIngestionStandardEmploymentIncome =
        "DocIngestionStandardEmploymentIncome";
    public const string DocIngestionPaystub = "DocIngestionPaystub";
    public const string DocIngestionThirdParty = "DocIngestionThirdParty";
    public const string DocIngestionW2 = "DocIngestionW2";
    public const string DocFraudDetection = "DocFraudDetection";
    public const string RCLockout = "RCLockout";
    public const string RCSolutionLoop = "RCSolutionLoop";
    public const string RCFormatExceptions = "RCFormatExceptions";
    public const string RCAdmin = "RCAdmin";
    public const string LoanDomain = "LD";
    public const string PIQ = "PIQ";
    public const string POQ = "POQ";
    public const string POQRMToRM = "POQRM";
    public const string RealTimeClearVisionTasks = "RealTimeClearVisionTasks";
    public const string CRM = "CRM";
    public const string RCQuickEdit = "RCQuickEdit";
    public const string LoanImporterService = "LIS";
    public const string RocketDocsThirdParty = "RocketDocsThirdParty";
    public const string Bankruptcy = "BK";
    public const string ClosedEndSeconds = "ClosedEndSeconds";
    public const string W2ConditionTrackingItems = "W2ConditionTrackingItems";
    public const string RocketDocs1099 = "RocketDocs1099";
    public const string RocketDocsW2 = "RocketDocsW2";
    public const string RocketDocsPaystub = "RocketDocsPaystub";
    public const string RocketDocsWireInstructions = "RocketDocsWireInstructions";
    public const string ConditionDrivenClosingDisclosureFeeExtraction = "ConditionDrivenClosingDisclosureFeeExtraction";
    public const string BypassCaptivaQdoc = "BypassCaptiva-Qdoc";
    public const string BypassCaptivaRMO = "BypassCaptiva-RMO";
    public const string BypassCaptivaTPO = "BypassCaptiva-TPO";
    public const string BypassCaptivaURLA = "BypassCaptiva-URLA";
    public const string RocketDocsEmployerTasks = "RocketDocsEmployerTasks";
    public const string RLRedisclosure = "RLRedisclosure";
    public const string PropertyTaxesPaidInFull = "PropertyTaxesPaidInFull";
    public const string BankerOutOfAmp = "BankerOutOfAmp";
    public const string CQCreditReportDetails = "CQCreditReportDetails";
    public const string AmpSlimMenuFlow = "AmpSlimMenuFlow";
    public const string TitleLeaseholdSelected = "TitleLeaseholdSelected";
    public const string EnforcePaystubFolderMilestone = "EnforcePaystubFolderMilestone";
    public const string MCSupplementalConsumerInformation = "MCSupplementalConsumerInformation";
    public const string PricingLockout = "PricingLockout";
    public const string RocketDocsBetterQualityImages = "RocketDocsBetterQualityImages";
    public const string LegacyIdBridge = "LegacyIdBridge";
    public const string AppPackagePrinting = "AppPackagePrinting";
    public const string EnforceHardCredit = "EnforceHardCredit";
    public const string RlbBackHalfAmpLockout = "RlbBackHalfAmpLockout";
    public const string RLBLola = "RLBLola";
    public const string ShortageApproval = "ShortageApproval";
    public const string RlbDynamicTrails = "RlbDynamicTrails";
    public const string RateLockServiceV2 = "RateLockServiceV2";
    public const string RLPPostRateLock = "RLPPostRateLock";
    public const string RentalIncome = "RentalIncome";
    public const string PalValPrinting = "PalValPrinting";
    public const string AmpLockout = "AmpLockout";
    public const string FeeOverride = "FeeOverride";
    public const string ParAutomatedReview = "ParAutomatedReview";
    public const string ExtendMODS = "ExtendMODS";
    public const string RatelockServiceRelock = "RatelockServiceRelock";
    public const string RatelockServiceLockTransfer = "RatelockServiceLockTransfer";
    public const string PurchaseNoAddress = "PurchaseNoAddress";
    public const string RlbAssistant = "RlbAssistant";
    public const string RocketLogicAssets = "RocketLogicAssets";
    public const string WaiveLowerAppDeposit = "WaiveLowerAppDeposit";
    public const string HeresHowYourNumbersWorkPrinting = "HeresHowYourNumbersWorkPrinting";
    public const string PrintLoanEstimateWithIntentToProceed = "PrintLoanEstimateWithIntentToProceed";
    public const string RlbBoomerang = "RlbBoomerang";
    public const string WithholdingsAndExpenses = "WithholdingsAndExpenses";
    public const string VATitleInformation = "VATitleInformation";
    public const string ReoUIv3 = "ReoUIv3";
    public const string RlXp = "rl-xp";
    public const string SelfEmploymentAssist = "SelfEmploymentAssist";
    public const string IsBankerLicensed = "IsBankerLicensed";
    public const string RLPMiscFieldDataEntry = "RLPMiscFieldDataEntry";
    public const string SingleBureau = "SingleBureau";
    public const string StateSpecificQuestions = "StateSpecificQuestions";
    public const string LoanStudio = "LoanStudio";
    public const string FeesOrchestratorEvaluation = "FeesOrchestratorEvaluation";
    public const string FeeRequestOrchestration = "FeeRequestOrchestration";
    public const string RLFees = "RLFees";
    public const string FeesOrchestratorEvaluationParent = "FeesOrchestratorEvaluationParent";
    public const string DocEntityExplorer = "DocEntityExplorer";
    public const string LoanPurposeService = "LoanPurposeService";
    public const string RocketFindings = "RocketFindings";
    public const string PreAppCreditReportUpgrade = "PreAppCreditReportUpgrade";
    public const string PreAppCreditReportUpgradePurchase = "PreAppCreditReportUpgradePurchase";
    public const string RocketLogicShell = "RocketLogicShell";
    public const string RlxpAssistant = "RlxpAssistant";
    public const string RlxpClientDataImport = "RlxpClientDataImport";
    public const string UnifiedDataLayerAssets = "UnifiedDataLayerAssets";
    public const string EnforceSoftPullRLXP = "EnforceSoftPullRLXP";
    public const string RlXpDenyWithdrawVerifier = "RlXpDenyWithdrawVerifier";
    public const string CommandeerTask = "CommandeerTask";
    public const string RFOverride = "RFOverride";
    public const string OverrideEligibilityStoppers = "OverrideEligibilityStoppers";
    public const string RlXpPricing = "RlXpPricing";
    public const string RlDocumentGenerator = "RlDocumentGenerator";

    private static string[] GetAll() =>
        typeof(ApplicationId)
            .GetFields(BindingFlags.Public | BindingFlags.Static)
            .Where(fieldInfo => fieldInfo.IsLiteral && fieldInfo.FieldType == typeof(string))
            .Select(fieldInfo => fieldInfo.GetValue(null))
            .Where(value => value is not null)
            .Cast<string>()
            .ToArray();
}
