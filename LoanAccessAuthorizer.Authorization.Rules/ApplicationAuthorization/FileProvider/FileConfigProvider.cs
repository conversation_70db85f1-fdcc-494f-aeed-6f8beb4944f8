﻿using LoanAccessAuthorizer.Domain;

namespace LoanAccessAuthorizer.Authorization.Rules.ApplicationAuthorization.FileProvider;

public abstract class FileConfigProvider
{
    protected readonly string EnvironmentName;

    public FileConfigProvider(EnvironmentInfo env)
    {
        EnvironmentName = env.EnvironmentName.ToLower();
    }
    protected IEnumerable<string> GetConfigFiles(string appName)
    {
        var configDirectory = Path.Combine(Directory.GetCurrentDirectory(), appName, EnvironmentName);
        if (!Directory.Exists(configDirectory))
        {
            return Enumerable.Empty<string>();
        }
        return Directory.GetFiles(configDirectory);
    }
}
