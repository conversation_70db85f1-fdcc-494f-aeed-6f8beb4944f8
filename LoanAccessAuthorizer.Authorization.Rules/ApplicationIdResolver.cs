using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using Microsoft.Extensions.DependencyInjection;

namespace LoanAccessAuthorizer.Authorization.Rules;

public class ApplicationIdResolver
{
    private readonly IReadOnlyDictionary<string, string> _applicationIdMap;

    public ApplicationIdResolver(IServiceProvider serviceProvider)
    {
        using var scope = serviceProvider.CreateScope();

        var exclusionDeciders = scope.ServiceProvider.GetServices<IExclusionDecider>();
        _applicationIdMap = exclusionDeciders.ToDictionary(
            decider => decider.AccessDecisionId,
            decider => decider.AccessDecisionId,
            StringComparer.InvariantCultureIgnoreCase);
    }

    public virtual string Resolve(string applicationId)
    {
        if (!_applicationIdMap.TryGetValue(applicationId, out var resolvedApplicationId))
        {
            throw new ArgumentOutOfRangeException($"Application Id '{applicationId}' does not exist");
        }

        return resolvedApplicationId;
    }

    public virtual IEnumerable<string> Resolve(IEnumerable<string> applicationIds) => applicationIds.Select(Resolve);
}
