using LoanAccessAuthorizer.Domain.TeamMemberData;
using LoanAccessAuthorizer.Authorization.Rules.TeamMember.Models;
using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot.PilotConfigProvider;
using LoanAccessAuthorizer.Domain.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.TeamMember;

public class TeamMemberPilotAccessService : ITeamMemberPilotAccessService
{
    private readonly ILeaderHierarchyProvider _leaderHierarchyProvider;
    private readonly IPilotConfigProvider _pilotConfigProvider;
    private readonly IEnumerable<string> _appIds;

    public TeamMemberPilotAccessService(ILeaderHierarchyProvider leaderHierarchyProvider,
        IPilotConfigProvider pilotConfigProvider)
    {
        _leaderHierarchyProvider = leaderHierarchyProvider;
        _pilotConfigProvider = pilotConfigProvider;
        _appIds = ApplicationId.All;
    }

    public async Task<IEnumerable<TeamMemberPilotStatus>> GetTeamMemberPilotStatuses(string commonId) =>
        (await GetTeamMemberPilotStatuses(commonId, false)).Statuses;

    public async Task<IEnumerable<TeamMemberPilotStatus>> GetTeamMemberPilotStatus(string commonId, string appId) =>
        (await GetTeamMemberPilotStatuses(commonId, false, appId)).Statuses;

    public Task<TeamMemberLeaderPilotStatus> GetTeamMemberLeaderPilotStatuses(string commonId) =>
        GetTeamMemberPilotStatuses(commonId, true);

    public Task<TeamMemberLeaderPilotStatus> GetTeamMemberLeaderPilotStatus(string commonId, string appId) =>
        GetTeamMemberPilotStatuses(commonId, true, appId);

    private async Task<TeamMemberLeaderPilotStatus> GetTeamMemberPilotStatuses(string commonId, bool leaderPilot,
        string appId = null)
    {
        var commonIds = new HashSet<string> { commonId };
        var leaderIds = new List<string>();
        if (leaderPilot)
        {
            leaderIds = (await _leaderHierarchyProvider.GetLeaderHierarchy(UserId.FromCommonId(commonId))).LeaderCommonIds.ToList();
            commonIds.UnionWith(leaderIds);
        }

        var appIds = string.IsNullOrWhiteSpace(appId) ? _appIds : new List<string> { appId };

        var statuses = appIds.Select(id =>
        {
            var pilotConfigs = _pilotConfigProvider.GetPilots(id);
            var pilotGroups = pilotConfigs.Select(GetPilotGroup).ToList();
            return GetTeamMemberPilotStatus(pilotGroups, id, commonIds);
        });
        return new TeamMemberLeaderPilotStatus { Statuses = statuses, LeaderHierarchy = leaderIds };
    }

    private static TeamMemberPilotStatus GetTeamMemberPilotStatus(
        IEnumerable<(DateTime PilotDate, PilotGroup PilotGroup)> pilotGroups, string appId, ISet<string> commonIds) =>
        new()
        {
            ApplicationId = appId,
            IsInPilot = pilotGroups.Any(pilot =>
                DateTime.Now >= pilot.PilotDate && pilot.PilotGroup.ContainsAnyCommonIds(commonIds, string.Empty))
        };

    private static (DateTime PilotDate, PilotGroup PilotGroup) GetPilotGroup(PilotConfiguration pilotConfig) =>
        (pilotConfig.PilotDate,
            new PilotGroup(pilotConfig.CommonIdentifiers, pilotConfig.ExcludedCommonIdentifiers,
                pilotConfig.LoanChannels, pilotConfig.IncludedQualificationGroups,
                pilotConfig.ExcludedQualificationGroups, pilotConfig.Features));
}
