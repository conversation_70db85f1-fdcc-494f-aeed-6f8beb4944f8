﻿using LoanAccessAuthorizer.Authorization.Rules.TeamMember.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.TeamMember;

public interface ITeamMemberPilotAccessService
{
    Task<IEnumerable<TeamMemberPilotStatus>> GetTeamMemberPilotStatuses(string commonId);
    Task<IEnumerable<TeamMemberPilotStatus>> GetTeamMemberPilotStatus(string commonId, string appId);
    Task<TeamMemberLeaderPilotStatus> GetTeamMemberLeaderPilotStatuses(string commonId);
    Task<TeamMemberLeaderPilotStatus> GetTeamMemberLeaderPilotStatus(string commonId, string appId);
}
