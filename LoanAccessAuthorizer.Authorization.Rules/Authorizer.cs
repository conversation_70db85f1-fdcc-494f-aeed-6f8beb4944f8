using LoanAccessAuthorizer.Authorization.Rules.Authorization;
using LoanAccessAuthorizer.Authorization.Rules.Authorization.Model;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using LoanAccessAuthorizer.Domain.Models.Response;

namespace LoanAccessAuthorizer.Authorization.Rules;

public class Authorizer
{
    private readonly IAccessVerifier _accessVerifier;

    public Authorizer(IAccessVerifier accessVerifier)
    {
        _accessVerifier = accessVerifier;
    }

    public virtual async Task<ApplicationAuthorizationResponse> GetAccessByApplication(
        string appId,
        ADUser user,
        VerificationDecisionInputData data,
        AccessDecisionContainer accessContainer
    )
    {
        var appIds = new HashSet<string> { appId };
        var responses = await GetAccessByApplications(appIds, user, data, accessContainer);
        return responses[appId];
    }

    public virtual async Task<Dictionary<string, ApplicationAuthorizationResponse>> GetAccessByApplications(
        IEnumerable<string> appIds,
        ADUser user,
        VerificationDecisionInputData inputData,
        AccessDecisionContainer accessContainer)
    {
        var tasks = appIds
            .Select(async appId => (appId, access: await _accessVerifier.VerifyAccess(
                user,
                inputData,
                appId,
                accessContainer
            )))
            .ToArray();

        var access = await Task.WhenAll(tasks);
        return access.ToDictionary(item => item.appId, item => item.access);
    }
}
