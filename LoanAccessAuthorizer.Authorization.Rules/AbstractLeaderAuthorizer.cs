using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot.Utils;
using LoanAccessAuthorizer.Domain.Models;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules;

public abstract class AbstractLeaderAuthorizer
{
    protected readonly IPilotCheck _pilotCheck;
    protected readonly ILogger<AbstractLeaderAuthorizer> _logger;

    protected AbstractLeaderAuthorizer(PilotCheckFactory pilotCheckFactory,
        string applicationId,
        ILogger<AbstractLeaderAuthorizer> logger)
    {
        _logger = logger;

        _pilotCheck = pilotCheckFactory(PilotCheckType.Leader, applicationId);
    }

    public virtual async Task<(bool isInPilot, bool hasLoanData)> IsInPilotWithLoanData(int commonId, AccessDecisionContainer accessContainer)
    {
        var pilotDeterminantDate = await GetLoanDateForPilotComparison(accessContainer);

        var stringCommonId = commonId.ToString();

        return await _pilotCheck.IsInPilot(accessContainer, UserId.FromCommonId(stringCommonId), pilotDeterminantDate);
    }

    protected virtual Task<DateTime?> GetLoanDateForPilotComparison(AccessDecisionContainer accessContainer)
    {
        return RLBUtil.GetLoanDateForPilotComparisonWithDefault(accessContainer, DateTime.UtcNow, _logger);
    }
}
