namespace LoanAccessAuthorizer.Authorization.Rules.Configuration;

public class ParAutomatedReviewConfiguration
{
    public HashSet<string> SupportedStates { get; set; }
    public HashSet<string> SupportedQualificationGroups { get; set; }
    public DateTime StartDate { get; set; }
    public ParAutomatedReviewDocumentTypes DocumentTypes { get; set; }
    public bool AddendumsEnabled { get; set; } = true;
}
