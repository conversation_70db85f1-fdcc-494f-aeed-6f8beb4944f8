﻿using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.KeyValueStore;


namespace LoanAccessAuthorizer.Authorization;

public class ApplicationAuthorizerKeyValueStore : IApplicationAuthorizerDecisionStore
{
    private readonly IKeyValueStore _keyValueStore;

    public ApplicationAuthorizerKeyValueStore(IKeyValueStore KeyValueStore)
    {
        _keyValueStore = KeyValueStore;
    }

    public async Task<bool?> GetDecision(string loanNumber, string appName)
    {
        var storeKey = makeStoreKey(loanNumber, appName);
        var persistedIsAuthorized = await _keyValueStore.GetBooleanValue(storeKey);
        return persistedIsAuthorized;
    }

    public async Task StoreDecision(string loanNumber, string appName, bool decision)
    {
        var storeKey = makeStoreKey(loanNumber, appName);
        await _keyValueStore.StoreValue(storeKey, decision);
    }

    public async Task RemoveDecision(string loanNumber, string appName)
    {
        var storeKey = makeStoreKey(loanNumber, appName);
        await _keyValueStore.RemoveValue(storeKey);
    }

    private static string makeStoreKey(string loanNumber, string appName)
    {
        return $"{loanNumber}#${appName}#defaultAuthorizerDecision";
    }
}
