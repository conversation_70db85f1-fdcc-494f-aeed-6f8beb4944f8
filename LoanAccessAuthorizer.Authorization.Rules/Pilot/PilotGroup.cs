namespace LoanAccessAuthorizer.Authorization.Rules.Pilot;

public class PilotGroup
{
    public IEnumerable<string> Features { get; }
    private readonly ISet<string> _commonIds;
    private readonly ISet<string> _excludedCommonIds;
    private readonly bool _allowAllCommonIds;
    private readonly ISet<string> _loanChannels;
    public readonly bool AllowAllLoanChannels;
    private readonly ISet<string> _includedQualificationGroups;
    private readonly ISet<string> _excludedQualificationGroups;

    private const string AllowAllIdentifier = "*";
    public bool HasIncludedQualificationGroups { get; }
    public bool HasExcludedQualificationGroups { get; }


    public PilotGroup(
        IEnumerable<string> commonIds,
        IEnumerable<string> excludedCommonIds,
        IEnumerable<string> loanChannels,
        IEnumerable<string> includedQualificationGroups,
        IEnumerable<string> excludedQualificationGroups,
        IEnumerable<string> features)
    {
        _commonIds = GenerateHashSet(commonIds);
        _excludedCommonIds = GenerateHashSet(excludedCommonIds);
        _allowAllCommonIds = _commonIds.Contains(AllowAllIdentifier);
        _loanChannels = GenerateHashSet(loanChannels);
        AllowAllLoanChannels = _loanChannels.Contains(AllowAllIdentifier);
        _includedQualificationGroups = GenerateHashSet(includedQualificationGroups);
        _excludedQualificationGroups = GenerateHashSet(excludedQualificationGroups);
        ValidateQualificationGroups();
        HasIncludedQualificationGroups = _includedQualificationGroups.Any();
        HasExcludedQualificationGroups = _excludedQualificationGroups.Any();
        Features = GenerateHashSet(features);

    }

    public virtual bool ContainsCommonId(string commonId)
    {
        return (_allowAllCommonIds || _commonIds.Contains(commonId)) && !_excludedCommonIds.Contains(commonId);
    }

    public virtual bool ContainsAnyCommonIds(ISet<string> leaderCommonIds, string commonId)
    {
        var allow = _allowAllCommonIds || _commonIds.Overlaps(leaderCommonIds);
        if (allow)
        {
            return !_excludedCommonIds.Overlaps(leaderCommonIds) &&
                !_excludedCommonIds.Contains(commonId);
        }

        return false;
    }

    public virtual bool ContainsLoanChannel(string loanChannel)
    {
        return AllowAllLoanChannels || _loanChannels.Contains(loanChannel);
    }
    private void ValidateQualificationGroups()
    {
        if (_includedQualificationGroups.Overlaps(_excludedQualificationGroups))
        {
            throw new PilotConfigurationException("IncludedQualificationGroups and ExcludedQualificationGroups are overlapping");
        }
    }

    public virtual bool AllowsQualificationGroups(IEnumerable<string> qualificationGroups)
    {
        return (!HasIncludedQualificationGroups || _includedQualificationGroups.Overlaps(qualificationGroups))
                   && (!HasExcludedQualificationGroups || !_excludedQualificationGroups.Overlaps(qualificationGroups));
    }

    private HashSet<string> GenerateHashSet(IEnumerable<string> enumerable)
    {
        return new HashSet<string>(enumerable ?? Enumerable.Empty<string>(), StringComparer.OrdinalIgnoreCase);
    }
}
