using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using LoanAccessAuthorizer.Domain.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.Pilot;

public interface IPilotCheck
{
    bool HasLoanChannelCheck { get; }
    bool HasQualificationGroupCheck { get; }
    IReadOnlyList<(DateTime date, PilotGroup group)> Pilots { get; }
    Task<bool> IsCommonIdInPilot(AccessDecisionContainer accessContainer);
    Task<PilotCheckResult> IsInPilot(AccessDecisionContainer accessContainer);
    Task<PilotCheckResult> IsInPilot(
        AccessDecisionContainer accessContainer,
        UserId id,
        DateTime? pilotDeterminantDate);
}
