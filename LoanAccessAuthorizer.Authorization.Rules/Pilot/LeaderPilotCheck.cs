using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot.PilotConfigProvider;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Domain.Models;
using LoanAccessAuthorizer.Domain.TeamMemberData;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.Pilot;

public class LeaderPilotCheck : PilotCheck
{
    private readonly ILeaderHierarchyProvider _leaderHierarchyProvider;

    public LeaderPilotCheck(string applicationId,
        IPilotConfigProvider pilotConfigProvider,
        ILeaderHierarchyProvider leaderHierarchyProvider,
        IRhidToCommonIdProvider rhidToCommonIdProvider,
        ILogger<LeaderPilotCheck> logger)
        : base(applicationId, pilotConfigProvider, logger, rhidToCommonIdProvider)
    {
        _leaderHierarchyProvider = leaderHierarchyProvider;
    }

    public override async Task<bool> IsCommonIdInPilot(AccessDecisionContainer accessContainer)
    {
        var loanOfficerId = await accessContainer.StateContainer.Get<UserId>(
            ExplicitStateProviderIds.LoanOfficerCommonId
        );

        var leaderHierarchy = await GetLeaderHierarchy(loanOfficerId);

        var isInPilot = Pilots.Any(pilot =>
            pilot.group.ContainsAnyCommonIds(leaderHierarchy.LeaderCommonIds, loanOfficerId.Id) ||
            pilot.group.ContainsCommonId(loanOfficerId.Id));

        return isInPilot;
    }

    public override async Task<PilotCheckResult> IsInPilot(
        AccessDecisionContainer accessContainer,
        UserId id,
        DateTime? pilotDeterminantDate)
    {
        var leaderHierarchyTask = GetLeaderHierarchy(id);

        var loanChannel = GetLoanChannel(accessContainer.StateContainer);

        var qualificationGroups = GetQualificationGroups(accessContainer.StateContainer);

        await Task.WhenAll(leaderHierarchyTask, loanChannel, qualificationGroups);

        if (leaderHierarchyTask.Result == null || pilotDeterminantDate == null || loanChannel.Result == null ||
            qualificationGroups.Result == null)
        {
            return new PilotCheckResult(false, false);
        }

        var leaderHierarchy = leaderHierarchyTask.Result;
        var applicablePilots = Pilots.Where(pilot =>
            pilotDeterminantDate >= pilot.date
            && (pilot.group.ContainsAnyCommonIds(leaderHierarchy.LeaderCommonIds, leaderHierarchy.OwnCommonId)
                || pilot.group.ContainsCommonId(leaderHierarchy.OwnCommonId))
            && pilot.group.ContainsLoanChannel(loanChannel.Result)
            && pilot.group.AllowsQualificationGroups(qualificationGroups.Result)
        );

        var accessDecision = applicablePilots.Any();

        var features = applicablePilots
            .SelectMany(x => x.group.Features)
            .ToHashSet();

        return new PilotCheckResult(accessDecision, true, features);
    }

    protected virtual async Task<LeaderHierarchy> GetLeaderHierarchy(UserId id)
    {
        var leaderHierarchy = await _leaderHierarchyProvider.GetLeaderHierarchy(id);
        if (leaderHierarchy == null || !leaderHierarchy.LeaderCommonIds.Any())
        {
            _logger.LogWarning("No leaders present in hierarchy for user {@userId}", id);
        }
        return leaderHierarchy;
    }
}
