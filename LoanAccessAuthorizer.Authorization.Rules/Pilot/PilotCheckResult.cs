using LoanAccessAuthorizer.Domain.Models.Response;

namespace LoanAccessAuthorizer.Authorization.Rules.Pilot;

public class PilotCheckResult
{
    public bool IsInPilot { get; set; }
    public bool HasLoanData { get; set; }
    public IEnumerable<string> Features { get; set; }

    public PilotCheckResult(
        bool isInPilot,
        bool hasLoanData,
        IEnumerable<string> features = null)
    {
        IsInPilot = isInPilot;
        HasLoanData = hasLoanData;
        Features = features ?? Enumerable.Empty<string>();
    }

    public static implicit operator (bool, bool)(PilotCheckResult pcr) => (pcr.IsInPilot, pcr.HasLoanData);

    public void Deconstruct(out bool isInPilot, out bool hasLoanData)
    {
        isInPilot = IsInPilot;
        hasLoanData = HasLoanData;
    }
}
