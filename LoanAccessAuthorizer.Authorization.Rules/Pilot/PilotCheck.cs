using DecisionServices.Core.StateProvider;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Pilot.PilotConfigProvider;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.Models;
using LoanAccessAuthorizer.Domain.TeamMemberData;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.Pilot;

public class PilotCheck : IPilotCheck
{
    protected readonly string _applicationId;
    protected readonly ILogger<PilotCheck> _logger;
    protected readonly IRhidToCommonIdProvider _rhidToCommonIdProvider;

    public IReadOnlyList<(DateTime date, PilotGroup group)> Pilots { get; }

    public bool HasLoanChannelCheck { get; }
    public bool HasQualificationGroupCheck { get; }

    public PilotCheck(
        string applicationId,
        IPilotConfigProvider pilotConfigProvider,
        ILogger<PilotCheck> logger,
        IRhidToCommonIdProvider rhidToCommonIdProvider
    )
    {
        _applicationId = applicationId;
        _logger = logger;
        _rhidToCommonIdProvider = rhidToCommonIdProvider;

        Pilots = pilotConfigProvider
            .GetPilots(_applicationId)
            .Select(data =>
                (
                    data.PilotDate,
                    new PilotGroup(
                        data.CommonIdentifiers,
                        data.ExcludedCommonIdentifiers,
                        data.LoanChannels,
                        data.IncludedQualificationGroups,
                        data.ExcludedQualificationGroups,
                        data.Features
                    )
                )
            )
            .ToList();

        if (!Pilots.Any())
        {
            throw new PilotConfigurationException(
                $"No pilot configurations found for {_applicationId}"
            );
        }

        HasLoanChannelCheck = Pilots.Any(pilot => !pilot.group.AllowAllLoanChannels);
        HasQualificationGroupCheck = Pilots.Any(pilot =>
            pilot.group.HasIncludedQualificationGroups || pilot.group.HasExcludedQualificationGroups
        );
    }

    public virtual async Task<bool> IsCommonIdInPilot(AccessDecisionContainer accessContainer)
    {
        var loanOfficerId = await accessContainer.StateContainer.Get<UserId>(
            ExplicitStateProviderIds.LoanOfficerCommonId
        );

        var inPilot = Pilots.Any(pilot => pilot.group.ContainsCommonId(loanOfficerId.Id));
        if (!inPilot)
        {
            _logger.LogInformation(
                "Assigned loan officer {bankerCommonId} not in pilot group",
                loanOfficerId
            );
        }

        return inPilot;
    }

    public async Task<PilotCheckResult> IsInPilot(AccessDecisionContainer accessContainer)
    {
        var loanOfficerCommonIdTask = accessContainer.StateContainer.Get<UserId>(
            ExplicitStateProviderIds.LoanOfficerCommonId
        );
        var pilotDeterminantDateTask = accessContainer.StateContainer.Get<DateTime?>(
            ExplicitStateProviderIds.ApplicationOrInitialContactDate
        );

        return await IsInPilot(
            accessContainer,
            await loanOfficerCommonIdTask,
            await pilotDeterminantDateTask
        );
    }

    public virtual async Task<PilotCheckResult> IsInPilot(
        AccessDecisionContainer accessContainer,
        UserId id,
        DateTime? pilotDeterminantDate
    )
    {
        var commonId = id;
        if (id.Type == UserIdType.RockHumanId)
        {
            commonId = UserId.FromCommonId(
                await _rhidToCommonIdProvider.GetCommonIdFromRhid(id.Id)
            );
        }
        else if (id.Type != UserIdType.CommonId)
        {
            return new PilotCheckResult(false, false);
        }

        var loanChannel = GetLoanChannel(accessContainer.StateContainer);

        var qualificationGroups = GetQualificationGroups(accessContainer.StateContainer);

        await Task.WhenAll(loanChannel, qualificationGroups);

        if (
            pilotDeterminantDate == null
            || loanChannel.Result == null
            || qualificationGroups.Result == null
        )
        {
            return new PilotCheckResult(false, false);
        }

        var applicablePilots = Pilots.Where(pilot =>
            pilotDeterminantDate >= pilot.date
            && pilot.group.ContainsCommonId(commonId.Id)
            && pilot.group.ContainsLoanChannel(loanChannel.Result)
            && pilot.group.AllowsQualificationGroups(qualificationGroups.Result)
        );

        var accessDecision = applicablePilots.Any();

        var features = applicablePilots.SelectMany(x => x.group.Features).ToHashSet();

        return new PilotCheckResult(accessDecision, true, features);
    }

    protected Task<string> GetLoanChannel(StateContainer stateContainer)
    {
        if (HasLoanChannelCheck)
        {
            return stateContainer.Get<string>(ExplicitStateProviderIds.LoanChannel);
        }

        return Task.FromResult(string.Empty);
    }

    protected Task<QualificationGroupSet> GetQualificationGroups(StateContainer stateContainer)
    {
        if (HasQualificationGroupCheck)
        {
            return stateContainer.Get<QualificationGroupSet>();
        }

        return Task.FromResult(new QualificationGroupSet(null));
    }
}
