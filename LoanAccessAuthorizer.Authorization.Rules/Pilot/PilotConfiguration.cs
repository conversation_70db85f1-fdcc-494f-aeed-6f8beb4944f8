namespace LoanAccessAuthorizer.Authorization.Rules.Pilot;

public class PilotConfiguration
{
    public DateTime PilotDate { get; set; }
    public ICollection<string> CommonIdentifiers { get; set; }
    public ICollection<string> ExcludedCommonIdentifiers { get; set; }
    public ICollection<string> LoanChannels { get; set; }
    public HashSet<string> IncludedQualificationGroups { get; set; }
    public HashSet<string> ExcludedQualificationGroups { get; set; }
    public HashSet<string> Features { get; set; }
}
