using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Domain.Models;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules;

public class AppDepositFolderingAuthorizer : AbstractLeaderAuthorizer, IAppDepositFolderingAuthorizer
{
    public AppDepositFolderingAuthorizer(PilotCheckFactory pilotCheckFactory,
        ILogger<AppDepositFolderingAuthorizer> logger)
        : base(pilotCheckFactory, ApplicationId.AppDepositFoldering, logger)
    {
    }

    public override async Task<(bool isInPilot, bool hasLoanData)> IsInPilotWithLoanData(int commonId, AccessDecisionContainer accessContainer)
    {
        var loanOfficerCommonId =
            await accessContainer.StateContainer.Get<UserId>(ExplicitStateProviderIds.LoanOfficerCommonId);
        if (!int.TryParse(loanOfficerCommonId.Id, out var numericLoanOfficerCommonId))
        {
            return (false, false);
        }

        if (numericLoanOfficerCommonId != commonId)
        {
            var (isLoanOfficerInPilot, hasLoanData) = await base.IsInPilotWithLoanData(numericLoanOfficerCommonId, accessContainer);
            if (!isLoanOfficerInPilot)
            {
                return (false, hasLoanData);
            }
        }

        return await base.IsInPilotWithLoanData(commonId, accessContainer);
    }
}
