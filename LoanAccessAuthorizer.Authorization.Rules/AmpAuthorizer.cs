using LoanAccessAuthorizer.Authorization.Rules.Features;
using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.Extensions;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using Microsoft.Extensions.Logging;
using LoanAccessAuthorizer.RocketLogicApi.Models;
using DecisionServices.Core.StateProvider;
using LoanAccessAuthorizer.Domain.Models;

namespace LoanAccessAuthorizer.Authorization.Rules;

public class AmpAuthorizer : AbstractLeaderAuthorizer, IAmpAuthorizer
{
    private readonly IFeatureEnabledConfigProvider _featureFlagConfigProvider;

    public AmpAuthorizer(
        PilotCheckFactory pilotCheckFactory,
        ILogger<AmpAuthorizer> logger,
        IFeatureEnabledConfigProvider featureEnabledConfigProvider
    )
        : base(pilotCheckFactory, ApplicationId.BankerOutOfAmp, logger)
    {
        _featureFlagConfigProvider = featureEnabledConfigProvider;
    }

    public async Task<bool> IsInPilot(int commonId, AccessDecisionContainer accessContainer)
    {
        var loanOfficerCommonId = await accessContainer.StateContainer.Get<UserId>(
            ExplicitStateProviderIds.LoanOfficerCommonId
        );

        if (!int.TryParse(loanOfficerCommonId.Id, out var numericLoanOfficerCommonId))
        {
            return false;
        }

        if (numericLoanOfficerCommonId != commonId)
        {
            var isLoanOfficerInPilot = await IsInPilotWithLoanData(
                numericLoanOfficerCommonId,
                accessContainer
            );
            if (!isLoanOfficerInPilot.isInPilot)
            {
                return false;
            }
        }

        var (isInPilot, _) = await IsInPilotWithLoanData(commonId, accessContainer);
        if (!isInPilot)
        {
            return false;
        }

        var leadDetails = await accessContainer.StateContainer.Get<RocketLogicBankingLeadDetails>();
        if (leadDetails?.IsUnsupportedLeadType ?? false)
        {
            _logger.LogInformation(
                "Lead type for loan {leadDetails.LoanNumber} is unsupported",
                leadDetails.LoanNumber
            );
            return false;
        }

        var isOriginatedByRocketLogicTask = IsOriginatedByRocketLogic(accessContainer.StateContainer);
        var rlApiLoanDetailsTask = accessContainer.StateContainer.Get<RocketLogicLoanDetails>();

        var isOriginatedByRocketLogic = await isOriginatedByRocketLogicTask;
        var rlApiLoanDetails = await rlApiLoanDetailsTask;

        if (rlApiLoanDetails is null)
        {
            _logger.LogInformation("No details in RLB - loan is not in pilot");
            return false;
        }

        if (_featureFlagConfigProvider.IsFeatureEnabled(FeatureName.LockPurchaseLoansOutOfAmp))
        {
            return isOriginatedByRocketLogic;
        }

        return rlApiLoanDetails.LoanPurpose == LoanPurpose.Refinance && isOriginatedByRocketLogic;
    }

    private static async Task<bool> IsOriginatedByRocketLogic(StateContainer stateContainer)
    {
        var isInAmp = await stateContainer.Get<bool>(ExplicitStateProviderIds.IsLoanInAmp);
        if (!isInAmp)
        {
            return false;
        }

        var isOriginatedByRocketLogic = await stateContainer.Get<bool>(
            ExplicitStateProviderIds.IsLoanOriginatedByRLB);
        return isOriginatedByRocketLogic;
    }
}
