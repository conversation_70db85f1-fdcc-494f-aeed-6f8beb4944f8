using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using LoanAccessAuthorizer.Domain.ActiveDirectory;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules;

public class RocketLogicBankingAuthorizer : AbstractLeaderAuthorizer, IRocketLogicBankingAuthorizer
{

    public RocketLogicBankingAuthorizer(
        PilotCheckFactory pilotCheckFactory,
        ILogger<RocketLogicBankingAuthorizer> logger
    )
        : base(pilotCheckFactory, ApplicationId.RocketLogicBanking, logger) { }

    public async Task<bool> IsInPilot(
        ADUser user,
        AccessDecisionContainer accessContainer
    )
    {
        var (isInPilot, _) = await IsInPilotWithLoanData(user.CommonId, accessContainer);
        return isInPilot;
    }
}
