import { Modu<PERSON> } from '@nestjs/common';
import { MessageCenterController } from './message-center.controller';
import { MessageCenterService } from './message-center.service';
import { ArchivedLoanService } from 'src/archived-loan/archived-loan.service';
import { LoanContextService } from 'src/loan-context/loan-context.service';
import { NotificationModule } from 'src/notifications/notification.module';
import { OauthClientModule } from 'src/oauth/oauth-client/oauth-client.module';
import { HttpModule } from 'src/http/http.module';
import { LoggerModule } from 'src/logger/logger.module';

@Module({
  imports: [
    NotificationModule,
    OauthClientModule,
    HttpModule,
    LoggerModule,
  ],
  controllers: [MessageCenterController],
  providers: [
    MessageCenterService,
    ArchivedLoanService,
    LoanContextService,
  ],
  exports: [MessageCenterService, ArchivedLoanService],
})
export class MessageCenterModule {}
