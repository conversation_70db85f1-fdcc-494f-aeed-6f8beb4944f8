using System.Net;
using System.Text.Json;
using System.Text.Json.Serialization;
using DecisionServices.Core.Exceptions;
using LoanAccessAuthorizer.Domain.Exceptions;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Common.HttpRequestHandler;

public abstract class HttpRequestHandler
{
    private readonly HttpClient _client;
    protected readonly ILogger _logger;

    private static readonly JsonSerializerOptions _defaultSerializerOptions =
        new()
        {
            PropertyNameCaseInsensitive = true,
            Converters = { new JsonStringEnumConverter() },
        };

    protected HttpRequestHandler(HttpClient client, ILogger logger)
    {
        _client = client;
        _logger = logger;
    }

    protected virtual async Task<T> HandleHttpRequest<T>(
        HttpRequestMessage request,
        JsonSerializerOptions serializerOptions = null,
        bool logResponse = false
    )
    {
        HttpResponseMessage response;
        try
        {
            response = await _client.SendAsync(request);
        }
        catch (Exception ex)
        {
            throw new StatusCodeException(
                $"{GetType().Name} failed to connect successfully.",
                HttpStatusCode.InternalServerError,
                ex
            );
        }

        if (response is null)
        {
            throw new Exception($"Received null response from {request.RequestUri?.AbsolutePath}");
        }

        var content = await response.Content.ReadAsStringAsync();

        if (response.IsSuccessStatusCode)
        {
            serializerOptions ??= _defaultSerializerOptions;

            try
            {
                var result = JsonSerializer.Deserialize<T>(content, serializerOptions);

                if (logResponse)
                {
                    _logger.LogInformation(
                        "Got successful response for {method} {path} {@responseBody}",
                        request.Method,
                        request.RequestUri?.AbsolutePath,
                        result
                    );
                }

                return result;
            }
            catch (JsonException exception)
            {
                throw new Exception(
                    $"Unable to deserialize string. Request Path: {request.RequestUri?.AbsolutePath}",
                    exception
                );
            }
        }

        if (!string.IsNullOrEmpty(content))
        {
            _logger.LogError("response content {content}", content);
        }

        throw response.StatusCode switch
        {
            HttpStatusCode.Unauthorized => new UnauthorizedException(content),
            HttpStatusCode.Forbidden => new ForbiddenException(content),
            HttpStatusCode.NotFound => new RequestNotFoundException(content),
            HttpStatusCode.BadRequest => new BadRequestException(content),
            _ => new StatusCodeException(content, response.StatusCode)
        };
    }
}
