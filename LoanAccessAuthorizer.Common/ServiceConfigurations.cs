using LoanAccessAuthorizer.Common.AccessPopulations;
using LoanAccessAuthorizer.Common.Random;
using Microsoft.Extensions.DependencyInjection;

namespace LoanAccessAuthorizer.Common;

public static class ServiceConfigurations
{
    public static IServiceCollection AddAccessPopulations(this IServiceCollection services,
        IEnumerable<AccessPopulationConfiguration> accessPopulationConfigurations)
    {
        services.AddDefaultRandomNumberGenerator();
        services.AddSingleton<IAccessPopulationCreator, RandomPercentageBasedAccessPopulationCreator>();

        services.AddSingleton((sp) => new AccessPopulationFactory(accessPopulationConfigurations,
            sp.GetRequiredService<IEnumerable<IAccessPopulationCreator>>()));

        return services;
    }

    public static IServiceCollection AddDefaultRandomNumberGenerator(this IServiceCollection services)
    {
        return services.AddSingleton<IRandomNumberGenerator, SystemRandomNumberGenerator>();
    }
}
