using LoanAccessAuthorizer.Common.Random;

namespace LoanAccessAuthorizer.Common.AccessPopulations;

public class RandomPercentageBasedAccessPopulationCreator : IAccessPopulationCreator
{
    public const string PercentageParameterKey = "percentage";
    public const string MinimumParameterKey = "minimum";
    public const string MaximumParameterKey = "maximum";
    public string CreatorType => "random-percentage-based";
    private const int MinimumThresholdValue = 0;
    private const int MaximiumThresholdValue = 100;

    private readonly IRandomNumberGenerator randomNumberGenerator;

    public RandomPercentageBasedAccessPopulationCreator(IRandomNumberGenerator randomNumberGenerator)
    {
        this.randomNumberGenerator = randomNumberGenerator;
    }

    public IAccessPopulation CreateAccessPopulation(AccessPopulationConfiguration configuration)
    {
        var MinimumThreshold = configuration.Parameters.ContainsKey(MinimumParameterKey) &&
                            int.TryParse(configuration.Parameters[MinimumParameterKey], out var minimum)
                            ? minimum
                            : MinimumThresholdValue;

        var MaximiumThreshold = configuration.Parameters.ContainsKey(MaximumParameterKey) &&
                            int.TryParse(configuration.Parameters[MaximumParameterKey], out var maximum)
                            ? maximum
                            : MaximiumThresholdValue;
        if (configuration.Parameters.ContainsKey(PercentageParameterKey) &&
            double.TryParse(configuration.Parameters[PercentageParameterKey], out var percentage))
        {
            return new RandomPercentageBasedAccessPopulation(percentage, MinimumThreshold, MaximiumThreshold, randomNumberGenerator);
        }

        throw new Exception($"AccessPopulation {configuration.Id} has missing or invalid parameter {PercentageParameterKey}");
    }
}
