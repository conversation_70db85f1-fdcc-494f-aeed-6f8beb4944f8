using LoanAccessAuthorizer.Common.Random;

namespace LoanAccessAuthorizer.Common.AccessPopulations;

public class RandomPercentageBasedAccessPopulation : IAccessPopulation
{
    private readonly IRandomNumberGenerator randomNumberGenerator;
    private readonly double percentage;
    private readonly int MinimumThreshold;
    private readonly int MaximiumThreshold;

    public RandomPercentageBasedAccessPopulation(double percentage, int minimumThreshold, int maximumThreshold, IRandomNumberGenerator randomNumberGenerator)
    {
        if (percentage > maximumThreshold || percentage < minimumThreshold)
        {
            throw new Exception($"Supplied percentage not in valid range {minimumThreshold} - {maximumThreshold}");
        }

        this.percentage = percentage;
        this.randomNumberGenerator = randomNumberGenerator;
        MinimumThreshold = minimumThreshold;
        MaximiumThreshold = maximumThreshold;
    }

    public Task<bool> IsInPopulation()
    {
        var randomNumber = randomNumberGenerator.GenerateRandomNumber(MinimumThreshold, MaximiumThreshold);

        return Task.FromResult(randomNumber < percentage);
    }
}
