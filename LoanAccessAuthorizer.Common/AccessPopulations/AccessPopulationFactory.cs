namespace LoanAccessAuthorizer.Common.AccessPopulations;

public class AccessPopulationFactory
{
    private readonly Dictionary<string, IAccessPopulation> idToAccessPopulation;

    public AccessPopulationFactory(IEnumerable<AccessPopulationConfiguration> accessPopulationConfigurations, IEnumerable<IAccessPopulationCreator> creators)
    {
        var typeToCreator = creators.ToDictionary(creator => creator.CreatorType.ToLower());

        idToAccessPopulation = accessPopulationConfigurations.ToDictionary(config => config.Id, config =>
        {
            if (typeToCreator.TryGetValue(config.Type, out var creator))
            {
                return creator.CreateAccessPopulation(config);
            }
            throw new Exception($"No creator found for access population id {config.Id} of type {config.Type}");
        });
    }

    public virtual IAccessPopulation GetAccessPopulation(string id)
    {
        if (!idToAccessPopulation.TryGetValue(id, out var accessPopulation))
        {
            throw new Exception($"No access population configured for id {id}");
        }

        return accessPopulation;
    }
}
