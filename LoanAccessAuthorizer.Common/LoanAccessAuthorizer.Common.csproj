<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="DecisionServices.Core.Cache" Version="18.0.0" />
      <PackageReference Include="DecisionServices.Core.Extensions.AspNetCore" Version="18.0.0" />
      <PackageReference Include="DecisionServices.Core.Extensions.Authentication" Version="18.0.0" />
      <PackageReference Include="DecisionServices.Core.Extensions.Logging" Version="18.0.0" />
      <PackageReference Include="PolymorphicDeserializer.Core" Version="3.0.0" />
      <PackageReference Include="PolymorphicDeserializer.System.Text.Json" Version="3.0.0" />
      <PackageReference Include="System.Text.Json" Version="8.0.5" />
      <PackageReference Include="System.Net.Http" Version="4.3.4" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\LoanAccessAuthorizer.Domain\LoanAccessAuthorizer.Domain.csproj" />
    </ItemGroup>
</Project>
