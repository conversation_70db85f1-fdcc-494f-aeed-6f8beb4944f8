variable "aws_region" {}

variable "aws_account_id" {}

# ---------------------------------------------------------------------------------------------------------------------
# Standard Module Required Variables
# ---------------------------------------------------------------------------------------------------------------------

variable "app_id" {
  description = "Apphub ID of the application."
}

variable "application_name" {
  description = "The name of the application, whether it be a service, website, api, etc."
}

variable "environment" {
  description = "The environment name in which the infrastructure is located. (e.g. dev, test, beta, prod)"
}

variable "contact_email" {
  description = "The development team email address that is responsible for this resource(s)."
  type        = string
}

variable "replica_regions" {
  description = "Replica regions for global table"
  type        = list(string)
  default     = []
}
# ---------------------------------------------------------------------------------------------------------------------
# Infrastructure Tags
# ---------------------------------------------------------------------------------------------------------------------

variable "app_tags" {
  type    = map(string)
  default = {}
}

variable "module_source" {
  description = "The source of the terraform module.  Automatically populated by HAL."
  type        = string
  default     = ""
}

variable "module_version" {
  description = "The version of the terraform module.  Automatically populated by HAL."
  type        = string
  default     = ""
}

variable "hal_app_id" {
  description = "Hal app ID of the application.  Automatically populated by HAL."
  type        = string
  default     = null
}