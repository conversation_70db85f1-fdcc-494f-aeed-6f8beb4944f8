provider "aws" {
  region = var.aws_region
}

locals {
  table_prefix = "${var.application_name}-${var.environment}-${var.app_id}"

  iac_tags = {
    iac                           = "terraform"
    module                        = "dynamo-db"
    module_source                 = var.module_source
    module_version                = var.module_version
    hal-app-id                    = var.hal_app_id
    app-id                        = var.app_id
    environment                   = var.environment
    development-team-email        = lower(var.contact_email)
    infrastructure-team-email     = lower(var.contact_email)
    infrastructure-engineer-email = lower(var.contact_email)
  }

  tags = merge(var.app_tags, local.iac_tags)
}

resource "aws_dynamodb_table" "exclusions" {
  name         = "${local.table_prefix}-exclusions"
  billing_mode = "PAY_PER_REQUEST"

  hash_key  = "loanNumber"
  range_key = "sort"

  server_side_encryption {
    enabled = true
  }

  point_in_time_recovery {
    enabled = var.environment == "prod"
  }

  attribute {
    name = "loanNumber"
    type = "S"
  }

  attribute {
    name = "sort"
    type = "S"
  }

  attribute {
    name = "applicationId"
    type = "S"
  }

  attribute {
    name = "timestamp"
    type = "S"
  }

  global_secondary_index {
    name            = "ApplicationActivityIndex"
    hash_key        = "applicationId"
    range_key       = "timestamp"
    projection_type = "ALL"
  }

  stream_enabled = true
  stream_view_type = "NEW_AND_OLD_IMAGES"

  tags = local.tags

  dynamic "replica" {
    for_each = var.replica_regions
    content {
      region_name = replica.value
    }
  }
}