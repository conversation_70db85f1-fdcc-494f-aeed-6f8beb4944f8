permissions:
  - name: 'admin'
    rules:
      - - key: token.scope
          values:
            - 'admin'

awsPolicyGenerator:
  routes:
    - path: '/admin/*'
      verb: '*'
      requiredPermission:
        - 'admin'
    - path: "*"
      verb: "*"
      requiredPermission: []
  exposeOutputPaths:
    - unixid
    - commonid
    - client_name
verifiers:
  - type: openid
    aud: urn:ql-api:rocket_logic_ui-209947:Beta
    issuer: https://sso.beta.authrock.com/
    outputMapping:
      - inputPath: userInfo['https://ql.custom.openid.com/common_id']
        outputPath: commonid
      - inputPath: userInfo['https://ql.custom.openid.com/unixid']
        outputPath: unixid
      - inputPath: payload['https://ql.custom.openid.com/client_name']
        outputPath: client_name
  - type: openid
    aud: urn:ql-api:louis-ui-shell-203381:Beta
    issuer: https://sso.beta.authrock.com/
    outputMapping:
      - inputPath: userInfo['https://ql.custom.openid.com/common_id']
        outputPath: commonid
      - inputPath: userInfo['https://ql.custom.openid.com/unixid']
        outputPath: unixid
      - inputPath: payload['https://ql.custom.openid.com/client_name']
        outputPath: client_name
  - type: openid
    aud: urn:ql-api:loan_access_authorizer-202723:Beta
    issuer: https://sso.beta.authrock.com/
    outputMapping:
      - inputPath: payload
        outputPath: token
      - inputPath: payload['https://ql.custom.openid.com/client_name']
        outputPath: client_name
