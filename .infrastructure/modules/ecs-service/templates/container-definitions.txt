[
    {
        "name": "$${primary_container_name}",
        "image": "$${docker_repository}:$${app_version}",
        "essential": true,
        "portMappings": [{ "containerPort": $${primary_container_port} }],
        "logConfiguration": {
            "logDriver": "awslogs",
            "options": {
                "awslogs-group": "$${awslogs_group}",
                "awslogs-region": "$${aws_region}",
                "awslogs-stream-prefix": "$${aws_region}"
            }
        },
        "environment": [
            {"name": "ASPNETCORE_ENVIRONMENT", "value": "$${environment}_aws"},
            {"name": "ASPNETCORE_HTTP_PORTS", "value": "80"},
            {"name": "UseDockerSecrets", "value": "false"},
            {"name": "REGION", "value": "$${aws_region}"},
            {"name": "DT_HOME", "value": "/opt/dynatrace/oneagent/${dynatrace_env}"},
            {"name": "DT_CUSTOM_PROP", "value": "HOST_ENVIRONMENT=$${environment} APP_ID=${app_id}"},
            {"name": "DT_NETWORK_ZONE", "value": "aws.$${aws_region}.secure.0"},
            {"name": "LD_PRELOAD", "value": "/opt/dynatrace/oneagent/${dynatrace_env}/agent/lib64/liboneagentproc.so"}
        ],
		"secrets": [{
			"name": "Metrics:Password",
			"valueFrom": "arn:aws:ssm:us-east-2:${aws_account_id}:parameter/kraken/${environment}-aws/999999/INFLUX_PASSWORD"
		},{
			"name": "LDAP:0:Password",
			"valueFrom": "arn:aws:ssm:us-east-2:${aws_account_id}:parameter/kraken/${environment}-aws/${app_id}/ldap-password"
		},{
			"name": "LDAP:1:Password",
			"valueFrom": "arn:aws:ssm:us-east-2:${aws_account_id}:parameter/kraken/${environment}-aws/${app_id}/ldap-password"
		},{
            "name": "TeamMemberDataService:ClientId",
            "valueFrom": "arn:aws:ssm:us-east-2:${aws_account_id}:parameter/kraken/${environment}-aws/${app_id}/TMDSClientId"
        },{
            "name": "TeamMemberDataService:ClientSecret",
            "valueFrom": "arn:aws:ssm:us-east-2:${aws_account_id}:parameter/kraken/${environment}-aws/${app_id}/TMDSClientSecret"
        },{
            "name": "TimeTrackingCheckService:ClientId",
            "valueFrom": "arn:aws:ssm:us-east-2:${aws_account_id}:parameter/kraken/${environment}-aws/${app_id}/TMDSClientId"
        },{
            "name": "TimeTrackingCheckService:ClientSecret",
            "valueFrom": "arn:aws:ssm:us-east-2:${aws_account_id}:parameter/kraken/${environment}-aws/${app_id}/TMDSClientSecret"
        },{
            "name": "AuthHub:ClientId",
            "valueFrom": "arn:aws:ssm:us-east-2:${aws_account_id}:parameter/kraken/${environment}-aws/${app_id}/authhub-client-id"
        },{
            "name": "AuthHub:ClientSecret",
            "valueFrom": "arn:aws:ssm:us-east-2:${aws_account_id}:parameter/kraken/${environment}-aws/${app_id}/authhub-client-secret"
        },{
            "name": "SpecialAccessLoanNumbers:LoanNumbers:0",
            "valueFrom": "arn:aws:ssm:us-east-2:${aws_account_id}:parameter/kraken/${environment}-aws/${app_id}/special-access-loan-number"
        },{
            "name": "Metrics:OpenTelemetryConfiguration:ApiKey",
            "valueFrom": "arn:aws:ssm:us-east-2:${aws_account_id}:parameter/rocket-logic/${opentelemetry_env}/dynatrace_api_key"
        },{
            "name": "DynatraceApiKey",
            "valueFrom": "arn:aws:ssm:$${aws_region}:${aws_account_id}:parameter/dynatrace/${dynatrace_env}/rl_experience"
        }],
        "healthCheck": {
            "retries": 3,
            "command": [
                "CMD",
                "curl",
                "--fail",
                "localhost/health"
            ],
            "timeout": 10,
            "interval": 30,
            "startPeriod": 20
        },
        "cpu": ${cpu},
        "memory": ${memory}
    }
]
