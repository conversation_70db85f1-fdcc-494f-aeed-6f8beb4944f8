variable "aws_region" {
}
variable "load_balancer_name" {
}
variable "application_name" {
}
variable "primary_container_port" {
}
variable "desired_number_of_tasks" {
}
variable "aws_account_id" {
}
variable "app_id" {
}
variable "vpc_name" {
}
variable "environment" {
}
variable "load_balanced_port" {}
variable "min_number_of_tasks" {
  description = "Minimum number of tasks allowed to run due to autoscaling. Ignored if var.use_auto_scaling is false."
  default     = 1
}
variable "max_number_of_tasks" {
  description = "Maximum number of tasks allowed to run due to autoscaling. Ignored if var.use_auto_scaling is false."
  default     = 20
}
variable "use_auto_scaling" {
  description = "Is autoscaling enabled? If false, tasks running will always match var.desired_number_of_tasks"
  default     = false
}
variable "autoscaling_usage_threshold" {
  description = "Desired threshold (default 30), at which ECS will add/remove tasks if the average cpu usage is above/below this percentage."
  default     = 30
}
variable "cpu" {
  description = "CPU value for the entire task (can be split up per container within the task). Valid values: 256, 512, 1024, 2048, 4096."
  default     = 256
}
variable "memory" {
  description = "Memory value for the entire task (can be split up per container within task). Valid values: 512, 1024, 2048, 3072, 4096, etc (1024 increments)"
  default     = 2048
}
variable "api_domain_name" {
}
variable "regional_domain_name" {
  default = ""
}
variable "hosted_zone_name" {
}
variable "routing_failover_type" {
  type = string
}
variable "vpc_link_name" {}

variable cluster_name {}

variable allow_redis_cache_access {
  default = true
}

variable "logging_level" {
  default = "ERROR"
}

variable "log_filter_pattern" {}

variable "contact_email" {
  type = string
}
