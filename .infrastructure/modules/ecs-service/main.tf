provider "aws" {
  region = var.aws_region
  allowed_account_ids = [
    var.aws_account_id
  ]
}

locals {
  redis_env = var.environment == "prod" ? "prod" : "np"
  new_redis_sg_name = "laa_redis_access_${local.redis_env}"

  redis_sg_name = "allow_access_to_ds-${var.environment}-${var.aws_region}-cache"
  full_table_name = "${var.application_name}-${var.environment}-${var.app_id}"
  exclusions_table_name = "${var.application_name}-${var.environment}-${var.app_id}-exclusions"
}

data "aws_security_groups" "redis_access" {
  filter {
    name = "group-name"
    values = var.allow_redis_cache_access ? [local.redis_sg_name, local.new_redis_sg_name] : []
  }
}

data "aws_dynamodb_table" "dynamo" {
  name = local.full_table_name
}

data "aws_dynamodb_table" "dynamo_exclusions" {
  name = local.exclusions_table_name
}

data "aws_iam_policy_document" "dynamo" {
  statement {
    actions = [
      "dynamodb:BatchWriteItem",
      "dynamodb:Query",
      "dynamodb:PutItem",
      "dynamodb:GetItem",
      "dynamodb:DescribeTable",
      "dynamodb:DeleteItem",
      "dynamodb:UpdateItem"
    ]

    effect = "Allow"
    resources = [
      "${data.aws_dynamodb_table.dynamo.arn}",
      "${data.aws_dynamodb_table.dynamo_exclusions.arn}",
      "${data.aws_dynamodb_table.dynamo_exclusions.arn}/index/*"
    ]
  }
}


module "ecs_service" {
  source = "git::https://git.rockfin.com/DecisionServices/aws-modules.git//ecs-service?ref=11.3.8"

  primary_container_port = 80

  container_definitions = templatefile("${path.module}/templates/container-definitions.txt", {
    aws_account_id = var.aws_account_id
    environment = var.environment == "train" ? "uat" : var.environment
    app_id = var.app_id
    dynatrace_env = var.environment == "prod" ? "prod" : "nonprod"
    opentelemetry_env = var.environment == "prod" ? "prod" : "beta"
    dynatrace_env = var.environment == "prod" ? "prod" : "nonprod"
    cpu = var.cpu
    memory = var.memory
  })

  aws_region = var.aws_region

  cluster_name = var.cluster_name
  application_name = var.application_name
  aws_account_id = var.aws_account_id

  vpc_name = var.vpc_name
  vpc_link_name = var.vpc_link_name
  environment = var.environment
  app_id = var.app_id

  load_balanced_port = var.load_balanced_port
  load_balancer_name = var.load_balancer_name

  desired_number_of_tasks = var.desired_number_of_tasks
  min_number_of_tasks = var.min_number_of_tasks
  max_number_of_tasks = var.max_number_of_tasks
  use_auto_scaling = var.use_auto_scaling
  autoscaling_usage_threshold = var.autoscaling_usage_threshold

  cpu = var.cpu
  memory = var.memory

  api_domain_name = var.api_domain_name
  regional_domain_name = var.regional_domain_name
  hosted_zone_name = var.hosted_zone_name

  r53_routing = {
    type = "failover",
    weight = null,
    failover_type = var.routing_failover_type
    ttl = 60
  }

  add_tags_to_fargate_service = false

  auth_config_path = "${path.module}/auth-config/"

  log_filter_pattern = var.log_filter_pattern

  additional_security_groups = data.aws_security_groups.redis_access.ids

  extra_task_policies = [data.aws_iam_policy_document.dynamo.json]

  development_team_email = var.contact_email
  infrastructure_engineer_email = var.contact_email
  infrastructure_team_email = var.contact_email

  authorizer_layer_version = "7"
}


