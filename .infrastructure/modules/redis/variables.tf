variable "aws_region" {
  type = string
}
variable "aws_account_id" {
  type = string
}
variable "environment" {
  type = string
}
variable "application_name" {
  type = string
}
variable "app_id" {
  type = string
}
variable "vpc_name" {
  type = string
}
variable "redis_instance_type" {
  type = string
}
variable "number_of_redis_instances" {
  type = number
}
variable "private_hosted_zone_name" {
  type = string
}

variable "redis_host_name" {
  type = string
}

variable "contact_email" {
  type = string
}

