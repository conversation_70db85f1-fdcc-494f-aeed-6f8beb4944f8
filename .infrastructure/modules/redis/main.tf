locals {
  redis_env = var.environment == "prod" ? "prod" : "np"
  redis_access_sg_name = "laa_redis_access_${local.redis_env}"
}

data "aws_vpc" "selected" {
  tags = {
    Name = var.vpc_name
  }
}

data "aws_subnets" "private" {
  filter {
    name = "vpc-id"
    values = [
      data.aws_vpc.selected.id
    ]
  }

  tags = {
    Tier = "Private"
  }
}

data "aws_sns_topic" "alarm_sns_topic" {
  name = "decision-services-alerts-${var.environment}"
}

module "redis" {
  source = "git::https://git.rockfin.com/terraform/aws-elasticache-tf//modules/redis?ref=4.0.4"

  app_id           = var.app_id
  application_name = var.application_name
  aws_account_id   = var.aws_account_id
  aws_region       = var.aws_region
  environment      = local.redis_env


  development_team_email        = var.contact_email
  infrastructure_engineer_email = var.contact_email
  infrastructure_team_email     = var.contact_email

  instance_type          = var.redis_instance_type
  replication_group_size = var.number_of_redis_instances

  sns_topic_for_notifications = data.aws_sns_topic.alarm_sns_topic.arn
  subnet_ids                  = data.aws_subnets.private.ids
  vpc_id                      = data.aws_vpc.selected.id

  redis_version                         = "6.x"
  allow_connections_from_security_group = []
  create_parameter_group = true
  parameter_group_name = "memory-policy"
  parameter_group_description = " "
  cache_family = "redis6.x"
  parameter = [
    {
      name  = "maxmemory-policy"
      value = "allkeys-lru"
    }
  ]
}

#######
# Security Group to access Redis
#######
resource "aws_security_group" "laa_redis_security_group" {
  name   = local.redis_access_sg_name
  vpc_id = data.aws_vpc.selected.id
}

resource "aws_security_group_rule" "allow_connections_from_security_group" {
  type      = "ingress"
  from_port = module.redis.redis_cache_port
  to_port   = module.redis.redis_cache_port
  protocol  = "tcp"

  source_security_group_id = aws_security_group.laa_redis_security_group.id
  security_group_id        = module.redis.redis_security_group_id
}

#######
# Private DNS for Redis cluster
#######
data "aws_route53_zone" "vpc_zone" {
  name         = var.private_hosted_zone_name
  private_zone = true
  vpc_id       = data.aws_vpc.selected.id
}

resource "aws_route53_record" "lopa_redis_record" {
  name    = var.redis_host_name
  type    = "CNAME"
  ttl     = 60
  zone_id = data.aws_route53_zone.vpc_zone.id

  records = [
    module.redis.redis_primary_endpoint_address[0]
  ]
}
