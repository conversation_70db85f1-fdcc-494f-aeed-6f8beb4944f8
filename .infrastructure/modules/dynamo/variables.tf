variable "aws_region" {}

# ---------------------------------------------------------------------------------------------------------------------
# Standard Module Required Variables
# ---------------------------------------------------------------------------------------------------------------------

variable "app_id" {
  description = "Core ID of the application."
}

variable "application_name" {
  description = "The name of the application, whether it be a service, website, api, etc."
}

variable "environment" {
  description = "The environment name in which the infrastructure is located. (e.g. dev, test, beta, prod)"
}

# ---------------------------------------------------------------------------------------------------------------------
# Infrastructure Tags
# ---------------------------------------------------------------------------------------------------------------------

variable "app_tags" {
  type    = map(string)
  default = {}
}