locals {
  full_table_name = "${var.application_name}-${var.environment}-${var.app_id}"
  email = "<EMAIL>"

  iac_tags = {
    iac                           = "terraform"
    module                        = "dynamo-db"
    app-id                        = var.app_id
    environment                   = var.environment
    development-team-email        = lower(local.email)
    infrastructure-team-email     = lower(local.email)
    infrastructure-engineer-email = lower(local.email)
  }

  tags = merge(var.app_tags, local.iac_tags)
}

resource "aws_dynamodb_table" "laa" {
  name         = local.full_table_name
  billing_mode = "PAY_PER_REQUEST"

  hash_key  = "partition"

  server_side_encryption {
    enabled = true
  }

  point_in_time_recovery {
    enabled = var.environment == "prod"
  }

  attribute {
    name = "partition"
    type = "S"
  }

  stream_enabled = true

  stream_view_type = "NEW_AND_OLD_IMAGES"

  tags = local.tags
}