aws_account_id = "************"

contact_email = "<EMAIL>"

vpc_name = "decision-services-default-vpc"

load_balancer_name = "decision-services-nlb"

app_id = 202723

application_name = "laa"

primary_container_port = 80

desired_number_of_tasks = 8
min_number_of_tasks = 8
max_number_of_tasks = 20

use_auto_scaling = true
autoscaling_usage_threshold = 30

cpu = 4096
memory = 8192

allow_inbound_from_security_group_ids_num = 0

logging_level = "NONE"

vpc_link_name = "decision-services-nlb-vpc-link"

log_filter_pattern = "-health -Health"

// redis stuff
private_hosted_zone_name = "decision-services"
redis_instance_type       = "cache.m6g.xlarge"
number_of_redis_instances = 2
redis_host_name           = "redis.laa.decision-services"


// ecs service account module

use_custom_authorizer = true

additional_proxy_request_parameters = {
  "integration.request.header.x-amp-id" = "context.authorizer.unixid",
  "integration.request.header.x-common-id" = "context.authorizer.commonid",
  "integration.request.header.x-request-source" = "context.authorizer.client_name"
}

swagger_path      = "swagger"
health_check_path = "health"

development_team_email = "<EMAIL>"
infrastructure_team_email = "<EMAIL>"
infrastructure_engineer_email = "<EMAIL>"

allowed_ip_cidr_blocks = [
  "************/24",   # QL desktop range
  "*************/21",  # QL desktop range
  "***********/24",    # QL web range
  "************/27",   # QL web range
  "************/32",   # Circle CI
  "**************/32", # Circle CI
  "**************/32", # Circle CI
  "**********/18",     # Global
  "**************/26", # us-west-1
  "************/26",   # us-west-1
  "************/26",   # us-west-2
  "*************/26",  # us-west-2
  "*************/26",  # us-east-1
  "************/26",   # us-east-1
  "*************",     # us-east-2 Einstein
  "************",      # us-east-2 Einstein
  "*************",     # us-east-2 Einstein
  "************",      # us-west-2 Einstein
  "************",      # us-west-2 Einstein
  "************",      # us-west-2 Einstein
  "**********",        # us-east-2 Einstein
  "*************",     # us-east-2 Einstein
  "************",      # us-east-2 Einstein
  "*************",     # us-west-2 Einstein
  "**************",    # us-west-2 Einstein
  "100.21.52.45",      # us-west-2 Einstein
  "3.18.179.141",      # veritest
  "3.18.115.83",       # veritest
  "3.14.97.95",        # veritest
  "52.89.248.22",      # decision-services us-west-2
  "54.71.252.158",     # decision-services us-west-2
  "52.36.18.225",      # decision-services us-west-2
  "3.19.102.62",       # decision-services us-east-2
  "3.13.248.207",      # decision-services us-east-2
  "3.19.35.240",       # decision-services us-east-2
  "18.218.242.118",    # us-east-2 mortgage_operations-default-vpc
  "3.128.107.41",      # us-east-2 mortgage_operations-default-vpc
  "3.135.133.162",     # us-east-2 mortgage_operations-default-vpc
  "34.208.65.229",     # us-west-2 mortgage_operations-default-vpc
  "35.155.41.33",      # us-west-2 mortgage_operations-default-vpc
  "52.42.83.232",      # us-west-2 mortgage_operations-default-vpc
  "18.116.122.59",     # us-east-2 processing-services
  "3.137.117.46",      # us-east-2 processing-services
  "3.142.203.137",     # us-east-2 processing-services
  "**************",    # us-west-2 processing-services
  "*************",     # us-west-2 processing-services
  "*************",     # us-west-2 processing-services
  "*************",     # us-east-2 fee-services
  "************",      # us-east-2 fee-services
  "************",      # us-east-2 fee-services
  "*************",     # us-west-2 fee-services
  "*************",     # us-west-2 fee-services
  "*************",     # us-west-2 fee-services
  "**************/29", # allow select home IPs
  "*************/29",  # allow select home IPs
  "**************/29", # allow select home IPs
  "**************/29", # allow select home IPs
  "***********",       # us-east-2 vendor processing
  "************",      # us-east-2 vendor processing
  "***********",       # us-east-2 vendor processing
  "************",      # us-east-2 pricing mgmt
  "************",      # us-east-2 pricing mgmt
  "**************",    # us-east-2 pricing mgmt
  "************",      # us-west-2 pricing mgmt
  "***********",       # us-west-2 pricing mgmt
  "*************",     # us-west-2 pricing mgmt
  "***********",       # mortgage-review
  "*************",     # mortgage-review
  "************",      # mortgage-review
  "*************"      # mortgage-review
]
