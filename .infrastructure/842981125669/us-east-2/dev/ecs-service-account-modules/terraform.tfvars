terragrunt_source = "git::https://git.rockfin.com/DecisionServices/aws-modules.git//ecs-service-account-modules?ref=11.3.8"

use_custom_authorizer = true

additional_proxy_request_parameters = {
    "integration.request.header.x-amp-id" = "context.authorizer.unixid",
    "integration.request.header.x-common-id" = "context.authorizer.commonid",
    "integration.request.header.x-request-source" = "context.authorizer.client_name"
}

swagger_path      = "swagger"
health_check_path = "health"

development_team_email = "<EMAIL>"
infrastructure_team_email = "<EMAIL>"
infrastructure_engineer_email = "<EMAIL>"

allowed_ip_cidr_blocks = [
    "************/24",   # QL desktop range
    "*************/21",  # QL desktop range
    "***********/24",    # QL web range
    "************/27",   # QL web range
    "************/32",   # Circle CI
    "**************/32", # Circle CI
    "**************/32", # Circle CI
    "**********/18",     # Global
    "**************/26", # us-west-1
    "************/26",   # us-west-1
    "************/26",   # us-west-2
    "*************/26",  # us-west-2
    "*************/26",  # us-east-1
    "************/26",   # us-east-1
    "************",      # us-east-2 Einstein
    "***********",       # us-east-2 Einstein
    "*************",     # us-east-2 Einstein
    "*************",     # us-west-2 Einstein
    "***********",       # us-west-2 Einstein
    "************",      # us-west-2 Einstein
    "**********",        # us-east-2 Einstein
    "************",      # us-east-2 Einstein
    "************",      # us-east-2 Einstein
    "*************",     # us-west-2 Einstein
    "52.12.138.74",      # us-west-2 Einstein
    "52.12.164.11",      # us-west-2 Einstein
    "3.18.179.141",      # us-east-2 decision services
    "3.18.115.83",       # us-east-2 decision services
    "3.14.97.95",        # us-east-2 decision services
    "34.209.97.175",     # us-west-2 decision services
    "54.70.34.205",      # us-west-2 decision services
    "34.211.30.126",     # us-west-2 decision services
    "3.21.155.12",       # us-east-2 mortgage_operations-default-vpc
    "3.21.90.66",        # us-east-2 mortgage_operations-default-vpc
    "3.23.182.22",       # us-east-2 mortgage_operations-default-vpc
    "34.210.120.146",    # us-west-2 mortgage_operations-default-vpc
    "44.230.204.135",    # us-west-2 mortgage_operations-default-vpc
    "54.188.63.56",      # us-west-2 mortgage_operations-default-vpc
    "3.134.199.98",      # us-east-2 processing-services
    "3.143.198.252",     # us-east-2 processing-services
    "3.22.165.1",        # us-east-2 processing-services
    "************",      # us-west-2 processing-services
    "**************",    # us-west-2 processing-services
    "**************",    # us-west-2 processing-services
    "*************",     # us-east-2 fee-services
    "*************",     # us-east-2 fee-services
    "************",      # us-east-2 fee-services
    "***********",       # us-west-2 fee-services
    "*************",     # us-west-2 fee-services
    "***********",       # us-west-2 fee-services
    "**************/29", # allow select home IPs
    "*************/29",  # allow select home IPs
    "**************/29", # allow select home IPs
    "**************/29", # allow select home IPs
    "*************",     # us-east-2 vendor processing
    "*************",     # us-east-2 vendor processing
    "************",      # us-east-2 vendor processing
    "***********",       # us-east-2 pricing mgmt
    "***********",       # us-east-2 pricing mgmt
    "************",      # us-east-2 pricing mgmt
    "*************",     # us-west-2 pricing mgmt
    "*************",     # us-west-2 pricing mgmt
    "**************",    # us-west-2 pricing mgmt,
    "************",      # mortgage-review
    "***********",       # mortgage-review
    "************"       # mortgage-review
]
