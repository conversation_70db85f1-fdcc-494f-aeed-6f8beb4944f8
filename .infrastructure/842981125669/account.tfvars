aws_account_id = "************"

contact_email = "<EMAIL>"

vpc_name = "decision-services-default-vpc"

load_balancer_name = "decision-services-nlb"

app_id = 202723

application_name = "laa"

primary_container_port = 80

desired_number_of_tasks = 2

use_auto_scaling = true

allow_inbound_from_security_group_ids_num = 0

vpc_link_name = "decision-services-nlb-vpc-link"

log_filter_pattern = "-health -Health"

// redis stuff
private_hosted_zone_name = "np.decision-services"
redis_instance_type       = "cache.t3.small"
number_of_redis_instances = 1
redis_host_name           = "redis.laa.np.decision-services"
