import { Injectable } from '@nestjs/common';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ConsoleLoggerService } from 'src/logger/implementations/console-logger.service';
import { OauthClientService } from 'src/oauth/oauth-client/oauth-client.service';
import { HttpErrorHandler } from 'src/http/http-error-handler';

@Injectable()
export class ArchivedLoanService {
  constructor(
    private oauthClient: OauthClientService,
    private logger: ConsoleLoggerService,
    private httpErrorHandler: HttpErrorHandler,
  ) {
    this.logger.setContext(ArchivedLoanService.name);
  }

  /**
   * Check if a loan is archived by calling the Loan Access Authorizer (LAA)
   * @param loanId The loan identifier
   * @returns Observable<boolean> indicating if the loan is archived
   */
  public isLoanArchived(loanId: string): Observable<boolean> {
    if (!loanId) {
      return of(false);
    }

    // Call LAA to check if loan is archived
    // This endpoint should match the one used by the frontend UserAuthorizationService
    return this.oauthClient
      .post<{ authorization: { applicationAuth: { LoanIsArchived?: { exclusion?: { reason?: string } } } } }>(
        `/authorization/loans/${loanId}`,
        {
          pilotIds: ['LoanIsArchived']
        }
      )
      .pipe(
        map((response) => {
          const loanIsArchived = response.data?.authorization?.applicationAuth?.LoanIsArchived;
          return !!loanIsArchived?.exclusion?.reason;
        }),
        catchError((err) => {
          this.logger.error(`Failed to check if loan ${loanId} is archived`, err);
          // Return false on error to avoid blocking the user
          return of(false);
        }),
      );
  }
}
