import { DestroyRef, Injectable, computed, inject, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, Validators } from '@angular/forms';
import { Liability, LiabilityType } from '@rocket-logic/rl-xp-bff-models';
import { distinctUntilChanged, map, of, scan, startWith, withLatestFrom } from 'rxjs';
import { removeEmptyValues } from '../../../util/remove-empty-values';
import { resetNonDirtyControls } from '../../../util/reset-non-dirty-controls';
import { UserAuthorizationService } from '../../user-authorization/user-authorization.service';
import { AbstractCollectionFormService } from '../abstract-collection-form.service';
import { LiabilityStateService } from '../liability-state/liability-state.service';
import {
  AllLiabilityControls,
  AllLiabilityForm,
  AllLiabilityGroup,
  LienInformationForm,
} from './form-types';
import { LiabilitySubscriptionManager } from './liability-subscription-manager.service';
import { LiabilityUpdateHandlerService } from './liability-update-handler.service';
import { LienAssociationHandlerService } from './lien-association-handler.service';
import { LienPositionHandlerService } from './lien-position-handler.service';
import { LIABILITY_AUTO_SAVE_TRIGGER } from './provide-liability-state';

@Injectable()
export class LiabilityFormRef extends AbstractCollectionFormService<AllLiabilityControls> {
  public entityFormMap = signal(new Map<string, AllLiabilityGroup>());
  override entityValues = computed(() => Array.from(this.entityFormMap().values() ?? []));
  public override formBuilder = inject(FormBuilder);
  public formMapChanges$ = this.entityValueChanges$.pipe(
    startWith(Array.from(this.entityFormMap().entries())),
    map((_) => Array.from(this.entityFormMap().entries())),
  );

  public formArray = this.entityFormArray;

  public updateFormState(id: string, liabilityForm: AllLiabilityGroup) {
    this.updateState(id, liabilityForm);
  }

  public deleteFormState(id: string) {
    this.deleteState(id);
  }
}

@Injectable()
export class LiabilityFormService {
  liabilityFormRef = inject(LiabilityFormRef);
  liabilityStateService = inject(LiabilityStateService);
  userAuthorizationService = inject(UserAuthorizationService);
  isLoanEditingDisabled = this.userAuthorizationService.isLoanArchived$;
  updateHandler = inject(LiabilityUpdateHandlerService);
  lienPositionHandler = inject(LienPositionHandlerService);
  lienAssociationHandler = inject(LienAssociationHandlerService);
  autoSaveTrigger = inject(LIABILITY_AUTO_SAVE_TRIGGER);
  subManager = inject(LiabilitySubscriptionManager);
  private destroyRef = inject(DestroyRef);

  formArray = this.liabilityFormRef.formArray;
  formMap = this.liabilityFormRef.entityFormMap;
  formMapValues = this.liabilityFormRef.entityValues;

  unassociatedLiabilities$ = this.liabilityFormRef.formMapChanges$.pipe(
    map((liabilities) =>
      liabilities.filter(
        ([_, liabilityFormGroup]) => liabilityFormGroup.value.ownedPropertyId == null,
      ),
    ),
    takeUntilDestroyed(this.destroyRef),
  );

  public getAssociatedLiabilities$(ownedPropertyId: string) {
    if (!ownedPropertyId) {
      return of([]);
    }

    return this.liabilityFormRef.entityValueChanges$.pipe(
      map((_) =>
        Array.from(this.formMap().entries()).filter(
          ([_, liabilityFormGroup]) => liabilityFormGroup.value.ownedPropertyId === ownedPropertyId,
        ),
      ),
      takeUntilDestroyed(this.destroyRef),
    );
  }

  constructor() {
    this.lienPositionHandler.addLienPositionListener();
    this.autoSaveTrigger.registerControls(this.formArray);
    this.liabilityStateService.state$
      .pipe(
        map((state) => state.data),
        distinctUntilChanged(),
        scan(
          (prev, current) => {
            return (
              Array.from(current?.entries() ?? []).map(([liabilityKey, liability]) => {
                const updated =
                  prev?.find(({ id }) => id === liabilityKey)?.liability !== liability;
                return { liability: liability, updated, id: liabilityKey };
              }) ?? []
            );
          },
          [] as { liability: Liability; updated: boolean; id: string }[],
        ),
        withLatestFrom(this.isLoanEditingDisabled),
        takeUntilDestroyed(),
      )
      .subscribe(([liabilities, isLoanEditingDisabled]) => {
        const newFormKeys: string[] = [];
        liabilities?.forEach(({ liability, updated, id }) => {
          let liabilityForm = this.formMap().get(id);
          if (!liabilityForm) {
            newFormKeys.push(id);
            liabilityForm = this.buildLiabilityForm();
            this.liabilityFormRef.updateFormState(id, liabilityForm);
            this.formArray.push(liabilityForm);
          }

          if (updated) {
            // This resets default values assigned in form builder but is required for auto save
            // Marking controls as dirty creates an endless autosave loop
            resetNonDirtyControls(liability, liabilityForm);
          }

          // set the default value for monthlyPaymentIncludesTaxesAndInsurance
          if (liabilityForm.controls.monthlyPaymentIncludesTaxesAndInsurance.value === null) {
            liabilityForm.controls.monthlyPaymentIncludesTaxesAndInsurance.setValue(true);
          }
        });

        newFormKeys.forEach((id) => this.addListeners(id, this.formMap().get(id)!));

        if (isLoanEditingDisabled) {
          this.formArray.disable();
        }
      });

    this.isLoanEditingDisabled.pipe(takeUntilDestroyed()).subscribe((isInactive) => {
      if (isInactive) {
        this.formArray.disable();
      }
    });
  }

  public getLiabilityForm(liabilityId: string): AllLiabilityGroup | undefined {
    return this.formMap().get(liabilityId);
  }

  public getLiabiltyValue(form: AllLiabilityGroup): Liability {
    const rawFormValue = form.getRawValue();
    return removeEmptyValues(rawFormValue) as Liability;
  }

  public addLiability(
    liabilityType: LiabilityType,
    ownedPropertyId?: string | null,
  ): AllLiabilityGroup {
    const liabilityForm = this.buildLiabilityForm();
    const liabilityKey = crypto.randomUUID();
    liabilityForm.patchValue({ liabilityType });

    this.liabilityFormRef.updateFormState(liabilityKey, liabilityForm);

    this.addListeners(liabilityKey, liabilityForm);

    this.formArray.push(liabilityForm);

    if (ownedPropertyId) {
      liabilityForm.patchValue({ ownedPropertyId });
    }

    return liabilityForm;
  }

  public deleteLiability(liabilityKey: string) {
    const liabilityFormGroup = this.formMap().get(liabilityKey);

    if (!liabilityFormGroup) {
      return;
    }

    this.liabilityStateService.deleteLiability$(liabilityKey!).subscribe(() => {
      this.liabilityFormRef.deleteFormState(liabilityKey!);
      this.formArray.removeAt(this.formArray.controls.indexOf(liabilityFormGroup));
      this.updateHandler.onDeleteLiability(liabilityKey!);
      this.subManager.removeSubscriptions(liabilityKey!);
    });
  }

  private addListeners(id: string, formGroup: AllLiabilityGroup) {
    this.updateHandler.addLiabilityUpdateListener(formGroup, id);
    this.lienAssociationHandler.addLienAssociationListener(id);
  }

  private buildLiabilityForm(): AllLiabilityGroup {
    return this.liabilityFormRef.formBuilder.group<AllLiabilityForm>({
      id: [null],
      liabilityType: [null],
      clientIds: [null, [Validators.required]],
      isCurrentDebtObligation: [null],
      qualifyingMonthlyPaymentAmount: [null],
      monthlyPaymentAmount: [null],
      unpaidBalance: [null],
      pastDueAmount: [null],
      creditorName: [null],
      accountId: [null],
      accountOpenedDate: [null],
      creditLimitAmount: [null],
      highBalanceAmount: [null],
      calculatedMonthlyPaymentsRemaining: [null],
      repaymentTermInMonths: [null],
      isPayingOffUnpaidBalanceAsPartOfTransaction: [null],
      isPayingOffPastDueAmountAsPartOfTransaction: [null],
      isOnCreditReport: [null],
      delinquencies: [null],
      debtToIncomeRatioDetail: [null],
      creditReportDetail: [null],
      isDisputedByConsumer: [null],
      currentRatingType: [null],
      isMedicalDebt: [null],
      payInFullPortionBalanceAmount: [null],
      payInFullPortionHasBeenReducedFromAssets: [null],
      payOverTimePortionMinPaymentAmount: [null],
      studentLoanRepaymentStatus: [null],
      isIncomeDrivenRepayment: [null],
      willPaymentAmountChangePriorToClose: [null],
      isMonthlyPaymentAnEstimateFromLoanServicer: [null],
      isSecuredByLienAgainstProperty: [null],
      ownedPropertyId: [null],
      payoffAmount: [null],
      lienInformation: this.liabilityFormRef.formBuilder.group<LienInformationForm>({
        lienPosition: [null],
        lienType: [null],
      }),
      mortgageType: [null],
      mortgageHolder: [null],
      currentLenderIsServicer: [null],
      wasOriginatedAsPartOfPurchaseTransaction: [null],
      amortizationType: [null],
      interestRate: [null],
      mortgageInsuranceMonthlyPaymentAmount: [null],
      principalAndInterestMonthlyPaymentAmount: [null],
      monthlyPaymentIncludesTaxesAndInsurance: [true],
      areTaxesDelinquent: [null],
      isClientOnRepaymentPlan: [null],
    });
  }
}
