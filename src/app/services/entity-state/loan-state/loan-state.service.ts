import { inject, Injectable } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import {
    DeactivationType,
    Lead,
    Loan,
    LoanDeactivationDetails,
} from '@rocket-logic/rl-xp-bff-models';
import {
    catchError,
    combineLatest,
    distinctUntilChanged,
    filter,
    map,
    NEVER,
    Observable,
    of,
    ReplaySubject,
    shareReplay,
    startWith,
    switchMap,
    take,
    tap,
} from 'rxjs';
import { LeadInitError } from '../../lead/lead.service';
import { LoanStatusLoadingService } from '../../loan-status-loading/loan-status-loading.service';
import { UserAuthorizationService } from '../../user-authorization/user-authorization.service';
import {
    EntityStateErrorType,
    EntityStateName,
    EntityStateService,
} from '../abstract-entity-state.service';

@Injectable()
export class LoanStateService extends EntityStateService<Loan> {
  private loanLoadingError$ = inject(LoanStatusLoadingService).loanLoadingError$;
  private userAuthorizationService = inject(UserAuthorizationService, { optional: true });
  name = EntityStateName.Loan;
  private isInitializedSubject = new ReplaySubject<boolean>(1);
  isInitialized$ = this.isInitializedSubject.asObservable();

  public isLoanDeactivated$ = this.state$.pipe(
    map(
      (state) =>
        state?.data?.loanDeactivationDetails &&
        Object.keys(state.data.loanDeactivationDetails).length > 0,
    ),
  );

  public loanHistory$ = this.loanIdService.loanId$.pipe(
    filter((loanId): loanId is string => !!loanId),
    take(1),
    switchMap((loanId) => this.dataProvider.getLoanHistoryUrl$(loanId)),
    switchMap((url: string) => this.dataProvider.fetchS3UrlContents$(url)),
    shareReplay(1),
  );

  public isLoanXpCompleted$ = this.loanApplicationStateService.isXpCompleted$;

  // Get archived loan status directly from UserAuthorizationService signal
  public isLoanArchived = this.userAuthorizationService?.isLoanArchived || (() => false);

  // Convert signal to observable for use in combineLatest
  public isLoanArchived$ = this.userAuthorizationService?.isLoanArchived$ || of(false);

  // Combined observable for all factors that disable loan editing
  public isLoanEditingDisabled$ = combineLatest([
    this.isLoanDeactivated$.pipe(startWith(false)),
    this.isLoanXpCompleted$.pipe(startWith(false)),
    this.loanLoadingError$.pipe(
      filter((error): error is LeadInitError => !!error),
      startWith(null),
    ),
    this.isLoanArchived$
  ]).pipe(
    map(
      ([isLoanDeactivated, isLoanXpCompleted, isLoanLoadingError, isLoanArchived]) =>
        isLoanDeactivated || isLoanXpCompleted || isLoanLoadingError === LeadInitError.Unsupported || isLoanArchived,
    ),
    takeUntilDestroyed(),
  );

  isLoanEditingDisabled = toSignal(this.isLoanEditingDisabled$);

  constructor() {
    super();
    this.loanIdService.loanId$
      .pipe(distinctUntilChanged(), takeUntilDestroyed())
      .subscribe(() => this.isInitializedSubject.next(false));
  }

  protected override getEntityState$(loanId: string): Observable<Loan> {
    return this.leadService.leadInitializationResult$.pipe(
      filter((result) => !!result.lead),
      take(1),
      switchMap(() => this.dataProvider.getLoan$(loanId)),
      catchError((error) => {
        if (error.status !== 404) {
          throw error;
        }
        return this.dataProvider.createLoanFromLead$(loanId).pipe(
          catchError((err) => {
            this.logger.error(`Failed to create loan from lead ${loanId}`, err);
            return of(null);
          }),
          switchMap(() => {
            return this.dataProvider.getLoan$(loanId);
          }),
        );
      }),
      tap(() => {
        this.isInitializedSubject.next(true);
      }),
    );
  }

  protected override updateEntityState$(loanId: string, state: Loan) {
    return this.dataProvider.updateLoan$(loanId, state);
  }

  public deactivateLoan$(deactivationType: DeactivationType, reason: string) {
    return this.loanIdService.loanId$.pipe(
      take(1),
      switchMap((loanId) =>
        loanId
          ? this.dataProvider.deactivateLoan$(loanId, {
              deactivationType,
              reason,
            } as LoanDeactivationDetails)
          : NEVER,
      ),
      catchError((error) => {
        if (error?.status === 403) {
          this.logger.error(`Failed to deactivate loan`, error);
          this.manualStateSubject.next({ error: EntityStateErrorType.UpdateError });
        }
        return error;
      }),
      tap(() => this.refreshState()),
    );
  }

  public completeInitialApplication$(formData: unknown): Observable<string> {
    return this.loanIdService.loanId$.pipe(
      filter((loanId): loanId is string => !!loanId),
      take(1),
      switchMap((loanId) =>
        this.dataProvider.completeInitialApplication$(loanId, formData).pipe(map(() => loanId)),
      ),
    );
  }

  public archiveLoanHistory$(history: unknown): Observable<void> {
    return this.loanIdService.loanId$.pipe(
      filter((loanId): loanId is string => !!loanId),
      take(1),
      switchMap((loanId) => {
        return this.dataProvider.saveLoanHistory$(loanId, history);
      }),
      shareReplay(1),
    );
  }

  public setConsentToObtainSchwabAssets$(hasConsent: boolean): Observable<Lead> {
    return this.loanIdService.loanId$.pipe(
      filter((loanId): loanId is string => !!loanId),
      take(1),
      switchMap((loanId) => this.dataProvider.updateSchwabImportConsent$(loanId, hasConsent)),
      tap((lead) => (this.leadService.lead = lead)),
    );
  }

  public setHasInternationalCredit$(hasConsent: boolean | null | undefined): Observable<Lead> {
    return this.loanIdService.loanId$.pipe(
      filter((loanId): loanId is string => !!loanId),
      take(1),
      switchMap((loanId) => this.dataProvider.updateHasInternationalCredit$(loanId, hasConsent)),
      tap((lead) => (this.leadService.lead = lead)),
    );
  }
}
