import { DestroyRef, Injectable, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { AbstractControl, FormControl, FormGroup, ValidationErrors } from '@angular/forms';
import {
  CurrentResidenceDetail,
  ResidenceDetail,
  ResidencyType,
} from '@rocket-logic/rl-xp-bff-models';
import {
  EMPTY,
  Observable,
  Subscription,
  distinctUntilChanged,
  filter,
  map,
  pairwise,
  shareReplay,
  startWith,
  switchMap,
  takeUntil,
  tap,
  withLatestFrom,
} from 'rxjs';
import { markDiffsDirty } from '../../../util/mark-diffs-dirty';
import { rawValueChanges$ } from '../../../util/raw-value-changes';
import { LoanStateService } from '../loan-state/loan-state.service';
import { ClientSubscriptionManager } from './client-subscription-manager.service';
import { ClientControls, CurrentResidenceControls } from './form-types';

@Injectable()
export class SameResidenceHandlerService {
  private destroyRef = inject(DestroyRef);
  private clientSubManager = inject(ClientSubscriptionManager);
  private loanStateService = inject(LoanStateService);
  private isLoanEditingDisabled$ = this.loanStateService.isLoanEditingDisabled$;
  private readonly sameAsResidenceValidator = (
    control: AbstractControl,
  ): ValidationErrors | null => {
    const residenceDetail = control.getRawValue() as ResidenceDetail;
    return !!residenceDetail.address.addressLine1 &&
      !!residenceDetail.address.city &&
      !!residenceDetail.address.state &&
      !!residenceDetail.address.zipCode
      ? null
      : { requiredFields: false };
  };

  addSameResidenceListener(
    clientFormMap: Map<string, FormGroup<ClientControls>>,
    clientKey: string,
  ) {
    const clientForm = clientFormMap.get(clientKey)!;
    const sameAsClientIdControl =
      clientForm.controls.residenceInformation.controls.currentResidence.controls.sameAsClientId;
    const isPrimaryBorrowerControl = clientForm.controls.isPrimaryBorrower;

    const sameAsClientForm$ = this.getSameAsClientForm$(
      sameAsClientIdControl,
      isPrimaryBorrowerControl,
      clientForm,
      clientFormMap,
    );
    const sub = new Subscription();
    sub.add(this.addResidenceSyncListener(sameAsClientForm$, clientForm));
    sub.add(this.addIndirectReferenceListener(sameAsClientForm$, clientForm));
    sub.add(this.addResetUseAsSubjectPropertyListener(sameAsClientForm$, clientForm));

    this.clientSubManager.addSubscription(clientKey, sub);
  }

  /**
   * Creates an observable that returns the appropriate client form based on the sameAsClientId control value.
   * @param sameAsClientIdControl
   * @param isPrimaryBorrowerControl
   * @param clientForm
   * @param clientFormMap
   * @returns An observable that emits a client form or `null`/`undefined` if there is no sameAsClientId.
   */
  private getSameAsClientForm$(
    sameAsClientIdControl: FormControl<string | null | undefined>,
    isPrimaryBorrowerControl: FormControl<boolean | null | undefined>,
    clientForm: FormGroup<ClientControls>,
    clientFormMap: Map<string, FormGroup<ClientControls>>,
  ) {
    return sameAsClientIdControl.valueChanges.pipe(
      startWith(sameAsClientIdControl.value),
      withLatestFrom(
        isPrimaryBorrowerControl.valueChanges.pipe(startWith(isPrimaryBorrowerControl.value)),
      ),
      distinctUntilChanged(
        ([prevSameAsClientId, prevIsPrimaryBorrower], [sameAsClientId, isPrimaryBorrower]) =>
          prevSameAsClientId === sameAsClientId && prevIsPrimaryBorrower === isPrimaryBorrower,
      ),
      tap(([value, isPrimaryBorrower]) => {
        if (!value || isPrimaryBorrower) this.enableCurrentResidence(clientForm);
        else this.disableCurrentResidence(clientForm);
      }),
      map(([sameAsClientId, isPrimaryBorrower]) => {
        const sameAsClientForm = sameAsClientId
          ? Array.from(clientFormMap.values()).find(
            (control) => control.value.id === sameAsClientId,
          )
          : null;

        if (isPrimaryBorrower && sameAsClientForm) {
          sameAsClientForm
            .get('residenceInformation.currentResidence.sameAsClientId')!
            .reset(clientForm.value.id);
          clientForm.get('residenceInformation.currentResidence.sameAsClientId')!.reset();
          return null;
        }

        return sameAsClientForm;
      }),
      shareReplay(1),
    );
  }

  /**
   * Listens for changes in the sameAsClient form and updates the currentResidence control in the client form.
   * @param sameAsClientForm$
   * @param clientForm
   * @returns `Subscription` to the listener
   */
  private addResidenceSyncListener(
    sameAsClientForm$: Observable<FormGroup<ClientControls> | null | undefined>,
    clientForm: FormGroup<ClientControls>,
  ) {
    return sameAsClientForm$
      .pipe(
        takeUntil(this.isLoanEditingDisabled$.pipe(filter((disabled) => disabled === true))),
        switchMap((sameAsClientForm) => {
          if (!sameAsClientForm) return EMPTY;
          const currentResidence = sameAsClientForm.get('residenceInformation.currentResidence')!;
          return rawValueChanges$(currentResidence);
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe((residence) => {
        delete residence.sameAsClientId;
        delete residence.taxesFiledAtThisAddress;
        delete residence.durationAtResidence;
        delete residence.ownedPropertyId;
        delete residence.planForProperty;
        delete residence.occupancyType;
        delete residence.sellingPrice;
        delete residence.monthlyRent;

        if (residence.residencyType === ResidencyType.Own) {
          residence.residencyType = ResidencyType.LivingRentFree;
        }

        const currentResidence = clientForm.controls.residenceInformation.controls.currentResidence;
        markDiffsDirty(residence as CurrentResidenceDetail, currentResidence);
        currentResidence.patchValue(residence);
      });
  }

  /**
   * Listens for changes in the sameAsClientForm and updates the sameAsClientId control in the client form to avoid indirect same residence references.
   * @param sameAsClientForm$
   * @param clientForm
   * @returns `Subscription` to the listener
   */
  private addIndirectReferenceListener(
    sameAsClientForm$: Observable<FormGroup<ClientControls> | null | undefined>,
    clientForm: FormGroup<ClientControls>,
  ) {
    return sameAsClientForm$
      .pipe(
        takeUntil(this.isLoanEditingDisabled$.pipe(filter((disabled) => disabled === true))),
        switchMap((sameAsClientForm) => {
          if (!sameAsClientForm) return EMPTY;
          const sameAsClientId = sameAsClientForm.get(
            'residenceInformation.currentResidence.sameAsClientId',
          )!;
          return rawValueChanges$(sameAsClientId, false).pipe(
            filter((sameAsClientId): sameAsClientId is string => !!sameAsClientId),
          );
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe((otherSameAsClientId) => {
        // prevent indirect references, override sameAsClientId if the other client changes it to a non-empty value
        clientForm
          .get('residenceInformation.currentResidence.sameAsClientId')!
          .patchValue(otherSameAsClientId);
      });
  }

  /**
   * Listens for changes in the sameAsClientForm and resets the useAsSubjectProperty control in the client form
   * when the residence is no longer the same as another client.
   * @param sameAsClientForm$
   * @param clientForm
   * @returns `Subscription` to the listener
   */
  private addResetUseAsSubjectPropertyListener(
    sameAsClientForm$: Observable<FormGroup<ClientControls> | null | undefined>,
    clientForm: FormGroup<ClientControls>,
  ) {
    return sameAsClientForm$
      .pipe(
        pairwise(),
        takeUntil(this.isLoanEditingDisabled$.pipe(filter((disabled) => disabled === true))),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(([prev, next]) => {
        if (prev && !next) {
          clientForm.controls.residenceInformation.controls.currentResidence.patchValue({
            useAsSubjectProperty: false,
          });
        }
      });
  }

  private disableCurrentResidence(clientForm: FormGroup<ClientControls>) {
    const currentResidence = clientForm.get(
      'residenceInformation.currentResidence',
    )! as FormGroup<CurrentResidenceControls>;
    currentResidence.addValidators(this.sameAsResidenceValidator);

    Object.entries(currentResidence.controls).forEach(([key, control]) => {
      const excludedKeys = [
        'sameAsClientId',
        'taxesFiledAtThisAddress',
        'durationAtResidence',
        'landlordDetails',
        'useAsSubjectProperty',
        'monthlyRent'
      ];

      if (excludedKeys.includes(key)) {
        return;
      }

      control.disable();
    });
  }

  private enableCurrentResidence(clientForm: FormGroup<ClientControls>) {
    const currentResidence = clientForm.get('residenceInformation.currentResidence');
    currentResidence!.removeValidators(this.sameAsResidenceValidator);
    currentResidence!.enable();
  }
}
