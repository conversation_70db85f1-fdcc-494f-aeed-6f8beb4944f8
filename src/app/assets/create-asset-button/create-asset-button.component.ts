import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, Output, computed, inject, input } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatSelectModule } from '@angular/material/select';
import { AssetType } from '@rocket-logic/rl-xp-bff-models';
import { ClientStateService } from '../../services/entity-state/client-state/client-state.service';
import { LoanFormRef } from '../../services/entity-state/loan-state/loan-form.service';
import { LoanStateService } from '../../services/entity-state/loan-state/loan-state.service';
import { UserAuthorizationService } from '../../services/user-authorization/user-authorization.service';
import { pascalCaseToSentence } from '../../util/formatting-helpers';

@Component({
  selector: 'app-create-asset-button',
  standalone: true,
  imports: [
    MatMenuModule,
    MatButtonModule,
    CommonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    MatSelectModule,
  ],
  templateUrl: './create-asset-button.component.html',
  styleUrl: './create-asset-button.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CreateAssetButtonComponent {
  private clientStateService = inject(ClientStateService);
  private userAuthorizationService = inject(UserAuthorizationService);
  private loanStateService = inject(LoanStateService);
  private subjectPropFormRef = inject(LoanFormRef);

  @Output() createTypeSelected = new EventEmitter<AssetType>();
  isDisabled = toSignal(this.loanStateService.isLoanEditingDisabled$);
  buttonLabel = input.required<string>();

  public choices = computed(() => {
    const choices = [
      {
        category: 'Bank Account',
        options: [
          AssetType.CheckingAccount,
          AssetType.SavingsAccount,
          AssetType.MoneyMarket,
          AssetType.CertificateOfDeposit,
        ].map(t => ({ assetType: t, display: mapAssetTypeToString(t) })),
      },
      {
        category: 'Retirement Accounts',
        options: [
          AssetType._401K,
          AssetType.IndividualRetirementArrangement,
          AssetType._403B,
          AssetType._457Plan,
          AssetType.ThriftSavingsPlan,
          AssetType.SimplifiedEmployeePension,
          AssetType.Annuity,
          AssetType.Keogh,
        ].map(t => ({ assetType: t, display: mapAssetTypeToString(t) })),
      },
      {
        category: 'Investments',
        options: [
          AssetType.BrokerageAccount,
          AssetType.MutualFund,
        ].map(t => ({ assetType: t, display: mapAssetTypeToString(t) })),
      },
      {
        category: 'Miscellaneous',
        options: [
          AssetType.BridgeLoan,
          AssetType._529CollegeSavingsPlan,
          AssetType.Grant,
          AssetType.LifeInsurance,
          AssetType.PledgedAssetAccount,
          AssetType.TrustAccount,
          AssetType._1031Exchange,
        ].map(t => ({ assetType: t, display: mapAssetTypeToString(t) })),
      },
      {
        category: 'Gift',
        options: [
          AssetType.GiftOfCash
        ].map(t => ({ assetType: t, display: mapAssetTypeToString(t) })),
      },
    ];

    if (this.subjectPropFormRef.isNonArmsLength()) {
      choices
        .find((group) => group.category === 'Gift')
        ?.options.push({
          assetType: AssetType.GiftOfEquity,
          display: mapAssetTypeToString(AssetType.GiftOfEquity),
        });
    }

    return choices;
  });

  createAsset(assetChoice: AssetType) {
    this.createTypeSelected.emit(assetChoice);
  }

}

export function mapAssetTypeToString(assetType: AssetType) {
  switch (assetType) {
    case AssetType._401K: return '401k';
    case AssetType.IndividualRetirementArrangement: return 'IRA';
    case AssetType._403B: return '403b';
    case AssetType._457Plan: return '457';
    case AssetType.ThriftSavingsPlan: return 'TSP';
    case AssetType.SimplifiedEmployeePension: return 'SEP';
    case AssetType.BrokerageAccount: return 'Brokerage Account';
    case AssetType.MutualFund: return 'Mutual Fund Account';
    case AssetType._529CollegeSavingsPlan: return 'College Savings';
    case AssetType.TrustAccount: return 'Trust';
    case AssetType._1031Exchange: return '1031 Exchange';
    default: return pascalCaseToSentence(assetType);
  }
}
