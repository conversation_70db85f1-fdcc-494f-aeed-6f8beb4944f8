version: 2.1

orbs:
  dotnet: rocket-technology/dotnet@2
  hal: rocket-technology/hal@1
  docker: rocket-technology/docker@1
  sonar: rocket-technology/sonarqube@2
  snyk: rocket-technology/snyk@3

executors:
  linux:
    docker:
      - image: cimg/base:current

#############################################################
# Anchors
#############################################################

dotnet_parameters: &dotnet_parameters
  executor:
    name: dotnet/sdk
    version: "8.0"
  project: "./LoanAccessAuthorizer.sln"

dotnet_publish_parameters: &dotnet_publish_parameters
  artifact-project: LoanAccessAuthorizer
  artifact-path: "./dist"

ecr_registry: &ecr_registry "laa"
hal_org_id: &hal_org_id "67"
hal_app_id: &hal_app_id "2664"
app_id: &app_id "202723"
app_name: &app_name "laa"

deploy_setting_id_dev_east: &deploy_setting_id_dev_east "11853"
deploy_setting_id_test_east: &deploy_setting_id_test_east "11857"
deploy_setting_id_beta_east: &deploy_setting_id_beta_east "11858"
deploy_setting_id_beta_west: &deploy_setting_id_beta_west "11856"
deploy_config_train_east: &deploy_config_train_east "43166"
deploy_setting_id_prod_east: &deploy_setting_id_prod_east "11859"
deploy_setting_id_prod_west: &deploy_setting_id_prod_west "11860"

#############################################################
# Commands
#############################################################

commands:
  tag_docker_image_latest:
    description: Tags the docker image pushed with this commit as latest
    parameters:
      aws_account_id:
        type: string
      region:
        type: string
      environment:
        type: string
    steps:
      - setup_remote_docker:
          docker_layer_caching: true
      - hal/login-to-ecr:
          api-token-var: HAL_API_TOKEN
          environment: << parameters.environment >>
          organization: *hal_org_id
          region: << parameters.region >>
      - run:
          name: Pull Docker image
          command: docker pull << parameters.aws_account_id >>.dkr.ecr.<< parameters.region >>.amazonaws.com/laa:${CIRCLE_SHA1}
      - run:
          name: Tag Docker Image
          command: docker tag << parameters.aws_account_id >>.dkr.ecr.<< parameters.region >>.amazonaws.com/laa:${CIRCLE_SHA1} << parameters.aws_account_id >>.dkr.ecr.<< parameters.region >>.amazonaws.com/laa:latest
      - run:
          name: Push Docker Image
          command: docker push << parameters.aws_account_id >>.dkr.ecr.<< parameters.region >>.amazonaws.com/laa:latest

#############################################################
# Define your workflow
#############################################################

workflows:
  version: 2

  pull_request:
    jobs:
      - hal/checkout:
          filters:
            branches:
              ignore:
                - main
      - dotnet/build:
          requires: [hal/checkout]
          <<: *dotnet_parameters
          <<: *dotnet_publish_parameters
      - dotnet/test:
          requires: [dotnet/build]
          <<: *dotnet_parameters
      - snyk/test:
          name: snyk-test
          requires: [dotnet/build]
          context: global.snyk
      - sonar/scan:
          requires: [dotnet/test]
          app-id: *app_id
      - approve_for_dev:
          type: approval
          requires: [dotnet/test]
      - docker/build:
          name: build_and_publish_image_nonprod
          requires: [approve_for_dev]
          context: hal-push
          environment: dev
          hal-org-id: *hal_org_id
          secret-var: HAL_API_TOKEN
          registry-name: *ecr_registry
          app-id: *app_id
          app-name: *app_name
          region: us-east-2
          dockerfile: LoanAccessAuthorizer/Dockerfile
      - hal/build:
          requires: [build_and_publish_image_nonprod]
          context: hal-push
          hal-appid: *hal_app_id
          from-deployment-setting: *deploy_setting_id_dev_east
      - hal/deploy:
          name: deploy_dev
          requires: [hal/build]
          context: hal-push
          deploy-setting-id: *deploy_setting_id_dev_east
          job-file: ".hal_deploy_id_dev"

  merge:
    jobs:
      - hal/checkout:
          filters:
            branches:
              only:
                - main
                - /^hotfix.*$/

      - dotnet/build:
          requires: [hal/checkout]
          <<: *dotnet_parameters
          <<: *dotnet_publish_parameters
      - dotnet/test:
          requires: [dotnet/build]
          <<: *dotnet_parameters
      - snyk/test:
          name: snyk-test
          requires: [dotnet/build]
          context: global.snyk
      - sonar/scan:
          requires: [dotnet/test]
          app-id: *app_id
      - docker/build:
          name: build_and_publish_image_nonprod
          requires: [dotnet/test]
          context: hal-push
          environment: dev
          hal-org-id: *hal_org_id
          secret-var: HAL_API_TOKEN
          registry-name: *ecr_registry
          app-id: *app_id
          app-name: *app_name
          region: us-east-2
          dockerfile: LoanAccessAuthorizer/Dockerfile
      - tag_image_latest_nonprod_east:
          requires: [build_and_publish_image_nonprod]
          context: hal-push
      - hal/build:
          requires: [build_and_publish_image_nonprod]
          context: hal-push
          hal-appid: *hal_app_id
          from-deployment-setting: *deploy_setting_id_dev_east
      - hal/deploy:
          name: deploy_dev
          requires: [hal/build]
          context: hal-push
          deploy-setting-id: *deploy_setting_id_dev_east
          job-file: ".hal_deploy_id_dev"
      - hal/deploy:
          name: deploy_test
          requires: [hal/build]
          context: hal-push
          deploy-setting-id: *deploy_setting_id_test_east
          job-file: ".hal_deploy_id_test"
      - approve_for_beta:
          type: approval
          requires: [deploy_test]
      - hal/deploy:
          name: deploy_beta_east
          requires: [approve_for_beta]
          context: hal-push
          deploy-setting-id: *deploy_setting_id_beta_east
          job-file: ".hal_deploy_id_beta_east"
      - docker/promote:
          name: promote_image_beta_west
          requires: [approve_for_beta]
          context: hal-push
          hal-org-id: *hal_org_id
          from-secret-var: HAL_API_TOKEN
          from-region: us-east-2
          from-environment: test
          from-registry-name: *ecr_registry
          secret-var: HAL_API_TOKEN
          region: us-west-2
          environment: beta
          registry-name: *ecr_registry
          app-id: *app_id
          app-name: *app_name
      - tag_image_latest_nonprod_west:
          requires: [promote_image_beta_west]
          context: hal-push
      - hal/deploy:
          name: deploy_beta_west
          requires: [promote_image_beta_west]
          context: hal-push
          deploy-setting-id: *deploy_setting_id_beta_west
          job-file: ".hal_deploy_id_beta_west"
      - approve_for_prod:
          type: approval
          requires: [deploy_beta_east, deploy_beta_west]
      - docker/promote:
          name: promote_image_prod_east
          requires: [approve_for_prod]
          context: hal-push
          hal-org-id: *hal_org_id
          from-secret-var: HAL_API_TOKEN
          from-region: us-east-2
          from-environment: beta
          from-registry-name: *ecr_registry
          secret-var: HAL_API_TOKEN
          region: us-east-2
          environment: prod
          registry-name: *ecr_registry
          app-id: *app_id
          app-name: *app_name
      - tag_image_latest_prod_east:
          requires: [promote_image_prod_east]
          context: hal-push
      - hal/deploy:
          name: deploy_prod_east
          requires: [promote_image_prod_east]
          context: hal-push
          deploy-setting-id: *deploy_setting_id_prod_east
          job-file: ".hal_deploy_id_prod_east"
      - docker/promote:
          name: promote_image_prod_west
          requires: [approve_for_prod]
          context: hal-push
          hal-org-id: *hal_org_id
          from-secret-var: HAL_API_TOKEN
          from-region: us-west-2
          from-environment: beta
          from-registry-name: *ecr_registry
          secret-var: HAL_API_TOKEN
          region: us-west-2
          environment: prod
          registry-name: *ecr_registry
          app-id: *app_id
          app-name: *app_name
      - tag_image_latest_prod_west:
          requires: [promote_image_prod_west]
          context: hal-push
      - hal/deploy:
          name: deploy_prod_west
          requires: [promote_image_prod_west]
          context: hal-push
          deploy-setting-id: *deploy_setting_id_prod_west
          job-file: ".hal_deploy_id_prod_west"
      - hal/deploy:
          name: deploy_train
          requires: [approve_for_prod]
          context: hal-push
          deploy-setting-id: *deploy_config_train_east
          job-file: ".hal_deploy_id_train_east"
      - snyk/monitor:
          requires: [deploy_prod_east, deploy_prod_west, deploy_train]
          context: global.snyk
  prod-hotfix:
    jobs:
      - hal/checkout:
          filters:
            branches:
              only:
                - /^hotfix.*$/
      - dotnet/build:
          requires: [hal/checkout]
          <<: *dotnet_parameters
          <<: *dotnet_publish_parameters
      - dotnet/test:
          requires: [dotnet/build]
          <<: *dotnet_parameters
      - snyk/test:
          name: snyk-test
          requires: [dotnet/build]
          context: global.snyk
      - sonar/scan:
          requires: [dotnet/test]
          app-id: *app_id
      - approve_for_prod:
          type: approval
          requires: [dotnet/test]
      - docker/build:
          name: build_and_publish_image_prod
          requires: [approve_for_prod]
          context: hal-push
          environment: prod
          hal-org-id: *hal_org_id
          secret-var: HAL_API_TOKEN
          registry-name: *ecr_registry
          app-id: *app_id
          app-name: *app_name
          region: us-east-2
          dockerfile: LoanAccessAuthorizer/Dockerfile
      - tag_image_latest_prod_east:
          requires: [build_and_publish_image_prod]
          context: hal-push
      - docker/promote:
          name: copy_image_prod_west
          requires: [build_and_publish_image_prod]
          context: hal-push
          hal-org-id: *hal_org_id
          from-secret-var: HAL_API_TOKEN
          from-region: us-east-2
          from-environment: prod
          from-registry-name: *ecr_registry
          secret-var: HAL_API_TOKEN
          region: us-west-2
          environment: prod
          registry-name: *ecr_registry
          app-id: *app_id
          app-name: *app_name
      - tag_image_latest_prod_west:
          requires: [copy_image_prod_west]
          context: hal-push
      - hal/build:
          requires: [copy_image_prod_west]
          context: hal-push
          hal-appid: *hal_app_id
          from-deployment-setting: *deploy_setting_id_prod_east
      - hal/deploy:
          name: deploy_prod_east
          requires: [hal/build]
          context: hal-push
          deploy-setting-id: *deploy_setting_id_prod_east
          job-file: ".hal_deploy_id_prod_east"
      - hal/deploy:
          name: deploy_prod_west
          requires: [hal/build]
          context: hal-push
          deploy-setting-id: *deploy_setting_id_prod_west
          job-file: ".hal_deploy_id_prod_west"
      - docker/promote:
          name: promote_image_train
          requires: [build_and_publish_image_prod]
          context: hal-push
          hal-org-id: *hal_org_id
          from-secret-var: HAL_API_TOKEN
          from-region: us-east-2
          from-environment: prod
          from-registry-name: *ecr_registry
          secret-var: HAL_API_TOKEN
          region: us-east-2
          environment: uat
          registry-name: *ecr_registry
          app-id: *app_id
          app-name: *app_name
      - hal/deploy:
          name: deploy_train
          requires: [promote_image_train, hal/build]
          context: hal-push
          deploy-setting-id: *deploy_config_train_east
          job-file: ".hal_deploy_id_train_east"

#############################################################
# Jobs
#############################################################

jobs:
  tag_image_latest_nonprod_east:
    executor: linux
    working_directory: ~/project
    steps:
      - tag_docker_image_latest:
          aws_account_id: "************"
          region: us-east-2
          environment: dev
  tag_image_latest_nonprod_west:
    executor: linux
    working_directory: ~/project
    steps:
      - tag_docker_image_latest:
          aws_account_id: "************"
          region: us-west-2
          environment: beta
  tag_image_latest_prod_east:
    executor: linux
    working_directory: ~/project
    steps:
      - tag_docker_image_latest:
          aws_account_id: "************"
          region: us-east-2
          environment: prod
  tag_image_latest_prod_west:
    executor: linux
    working_directory: ~/project
    steps:
      - tag_docker_image_latest:
          aws_account_id: "************"
          region: us-west-2
          environment: prod
