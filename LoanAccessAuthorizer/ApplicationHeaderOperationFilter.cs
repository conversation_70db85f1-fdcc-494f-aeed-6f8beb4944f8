using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using LoanAccessAuthorizer.Common;

namespace LoanAccessAuthorizer;

public class ApplicationHeaderOperationFilter : IOperationFilter
{
    private readonly string[] headers = new string[] { Headers.RequestId, Headers.RequestSource};

    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        if (operation.Parameters == null)
        {
            operation.Parameters = new List<OpenApiParameter>();
        }

        foreach (var header in headers)
        {
            operation.Parameters.Add(new OpenApiParameter
            {
                In = ParameterLocation.Header,
                Schema = new OpenApiSchema
                {
                    Type = "string"
                },
                Name = header,
                Required = false
            });
        }
    }
}
