using System.Diagnostics.CodeAnalysis;
using Serilog;
using LoanAccessAuthorizer.Roles.LDAP;
using LoanAccessAuthorizer.Authorization.Rules;
using DecisionServices.Core;
using DecisionServices.Core.Cache;
using DecisionServices.Core.Cache.Redis;
using DecisionServices.Core.Extensions.AspNetCore.Middleware;
using DecisionServices.Core.Extensions.AspNetCore.Middleware.Context.ContextProviders;
using DecisionServices.Core.Extensions.Logging;
using LoanAccessAuthorizer.AmpJobRunner;
using LoanAccessAuthorizer.Authorization;
using LoanAccessAuthorizer.Common;
using LoanAccessAuthorizer.Domain;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.Authorization;
using LoanAccessAuthorizer.Dynamo;
using LoanAccessAuthorizer.RocketDocs;
using LoanAccessAuthorizer.Roles.LDAP.Provider;
using LoanAccessAuthorizer.OpenAmp.Configuration;
using LoanAccessAuthorizer.RocketLogicUnderwriting;
using LoanAccessAuthorizer.RocketLogicBanking;
using DecisionServices.Core.Extensions.AspNetCore.Middleware.Context;
using LoanAccessAuthorizer.TeamMemberDataService;
using LoanAccessAuthorizer.Common.AccessPopulations;
using LoanAccessAuthorizer.CreditQualifier;
using LoanAccessAuthorizer.Domain.TeamMemberData;
using LoanAccessAuthorizer.Exclusions;
using LoanAccessAuthorizer.IncomeQualifier.Models;
using LoanAccessAuthorizer.IncomeQualifier;
using LoanAccessAuthorizer.MODS;
using LoanAccessAuthorizer.PropertyInsuranceQualifier;
using LoanAccessAuthorizer.PropertyQualifier;
using LoanAccessAuthorizer.Configuration;
using LoanAccessAuthorizer.Authorization.Rules.TeamMember;
using Microsoft.AspNetCore.Mvc.Versioning;
using LoanAccessAuthorizer.Configuration.Swagger;
using Microsoft.Extensions.Options;
using Swashbuckle.AspNetCore.SwaggerGen;
using Microsoft.AspNetCore.Mvc.ApiExplorer;
using LoanAccessAuthorizer.Configuration.Versioning;
using Serilog.Sinks.SystemConsole.Themes;
using LoanAccessAuthorizer.RLTasks;
using DecisionServices.Core.Diagnostics.HealthChecks.Redis;
using LoanAccessAuthorizer.RocketLogicApi;
using LoanAccessAuthorizer.ParOrchestrator;
using System.Text.Json.Serialization;
using DecisionServices.Core.Authentication;
using LoanAccessAuthorizer.LicensingService;
using LoanAccessAuthorizer.TimeTrackingCheckService;
using DecisionServices.Core.HttpPolicy.Metrics;
using DecisionServices.Core.Metrics.AspNetCore.Inbound;
using DecisionServices.Core.Metrics.Configuration;
using LoanAccessAuthorizer.Domain.Configuration;

namespace LoanAccessAuthorizer;

[ExcludeFromCodeCoverage]
public class Startup
{
    public void ConfigureServices(IServiceCollection services,
        IConfiguration configuration,
        IWebHostEnvironment env)
    {
        services
            .AddControllers(config =>
            {
                config.Filters.Add(new MetricsActionFilter(Headers.RequestSource));
            })
            .AddJsonOptions(options =>
            {
                options.JsonSerializerOptions.Converters.Add(
                    new JsonStringEnumConverter(allowIntegerValues: false));
            });

        services.AddApiVersioning(options =>
        {
            options.ReportApiVersions = true;
            options.ApiVersionReader = new HeaderApiVersionReader(Headers.ApiVersion);
            options.AssumeDefaultVersionWhenUnspecified = true;
        });

        services.AddVersionedApiExplorer(options =>
        {
            options.GroupNameFormat = VersioningConstants.vMajor;
        });

        services.AddTransient<IConfigureOptions<SwaggerGenOptions>, ConfigureSwaggerOptions>();
        services.AddSwaggerGen(c =>
        {
            c.DescribeAllParametersInCamelCase();
            c.OperationFilter<ApplicationHeaderOperationFilter>();
        });

        services.AddCoreContextProviders(
            new CoreContextProviderConfiguration
            {
                RequestIdHeader = "x-request-id",
                RequestSourceHeader = Headers.RequestSource
            }
        );

        services.AddContextProvider(
            new RequestHeaderContextProvider("x-forwarded-for"),
            new ContextProviderConfiguration
            {
                AddToLogContext = AddToLogContext.Always,
                LogContextKey = "XForwardedFor"
            }
        );

        services.AddContextProvider(
            new RequestHeaderContextProvider("x-common-id"),
            new ContextProviderConfiguration
            {
                AddToLogContext = AddToLogContext.WhenNotNull,
                LogContextKey = "CommonId"
            }
        );

        services.AddContextProvider(
            new RequestHeaderContextProvider("x-amp-id"),
            new ContextProviderConfiguration
            {
                AddToLogContext = AddToLogContext.WhenNotNull,
                LogContextKey = "AmpUsername"
            }
        );

        services.AddContextProvider(
            new RequestHeaderContextProvider(Headers.ApiVersion),
            new ContextProviderConfiguration
            {
                AddToLogContext = AddToLogContext.Always,
                LogContextKey = "ApiVersion"
            }
        );

        services.AddCors(options =>
        {
            options.AddDefaultPolicy(builder =>
            {
                builder.AllowAnyHeader().AllowAnyMethod().AllowAnyOrigin();
            });
        });

        var loggingConfig = new CoreLoggingConfiguration
        {
            AppId = configuration.GetValue<string>("APP_ID"),
            Region = configuration.GetValue<string>("REGION")
        };
        if (env.IsDevelopment())
        {
            loggingConfig.ConfigureLogger = c => c.WriteTo.Console(theme: AnsiConsoleTheme.Code);
        }
        services.AddCoreServices(loggingConfig);

        var httpPolicyConfigs = configuration.GetSection("HttpPolicies");
        foreach (var child in httpPolicyConfigs.GetChildren())
        {
            services.Configure<HttpPoliciesConfig>(child.Key, child);
        }

        var ldapConfiguration = configuration
            .GetSection("LDAP")
            .Get<List<LdapProviderConfiguration>>();
        services.AddLdapServices(ldapConfiguration);

        var tmdsConfiguration = configuration
            .GetSection("TeamMemberDataService")
            .Get<AuthenticatedServiceConfig>();
        services.AddTeamMemberDataService(tmdsConfiguration, "TeamMemberDataService");

        var ttcsConfiguration = configuration
            .GetSection("TimeTrackingCheckService")
            .Get<AuthenticatedServiceConfig>();
        services.AddTimeTrackingCheckService(ttcsConfiguration, "TimeTrackingCheckService");

        services.AddRuleAccessVerifier(env.EnvironmentName);
        services.AddScoped<Authorizer>();
        services.AddScoped<BankerCheck>();
        services.AddScoped<IElevatedBankerCommonIdsProvider, ElevatedBankerCommonIdsProvider>();

        services.AddSingleton(Log.Logger);

        var healthCheckBuilder = services.AddHealthChecks();

        var cacheConfiguration = configuration
            .GetSection("cacheConfig")
            .Get<LoanAccessAuthorizerRedisConfiguration>();

        if (cacheConfiguration.Enabled)
        {
            cacheConfiguration.Region = configuration.GetValue<string>("Region") ?? "no-region";
            ThreadPool.SetMinThreads(cacheConfiguration.MinIoThreads, cacheConfiguration.MinWorkerThreads);
            services.Configure<GlobalCacheLockingConfiguration>(configuration.GetSection("CacheOptions:GlobalCacheLocking"));
            services.AddRedisCache(cacheConfiguration, env.EnvironmentName.ToLower());
            healthCheckBuilder.AddRedisHealthCheck();
        }
        else
        {
            services.AddDisabledCache();
        }

        services.AddSingleton(new EnvironmentInfo { EnvironmentName = env.EnvironmentName });
        services.AddSingleton<
            IApplicationAuthorizerDecisionStore,
            ApplicationAuthorizerKeyValueStore
        >();
        services.AddScoped<ITeamMemberPilotAccessService, TeamMemberPilotAccessService>();

        services.AddAuthorizationV2(configuration);

        var metricsConfiguration = configuration.GetSection("Metrics").Get<MetricsConfiguration>();
        metricsConfiguration.Tags?.Add(
            "region",
            configuration.GetValue<string>("Region") ?? "no-region"
        );
        metricsConfiguration.OpenTelemetryConfiguration.ApiKey = configuration.GetValue<string>("DynatraceApiKey");
        MetricsServiceConfiguration.ConfigureDefaultMetricsCollector(
            metricsConfiguration,
            env.EnvironmentName
        );

        var commonIdConfigurations = configuration.GetSection("CommonIdConfigurations");
        foreach (var commonIdConfiguration in commonIdConfigurations.GetChildren())
        {
            services.Configure<CommonIdConfiguration>(
                commonIdConfiguration.Key,
                commonIdConfiguration
            );
        }

        var dynamoOptions = configuration.GetAWSOptions("dynamoDB");
        var isDynamoEnabled = configuration.GetValue<bool>("dynamoDB:enabled");

        //needed to load service url when running in Docker locally
        if (!string.IsNullOrWhiteSpace(configuration["dynamoDB:ServiceUrl"]))
        {
            dynamoOptions.DefaultClientConfig.ServiceURL = configuration["dynamoDB:ServiceUrl"];
        }

        services.AddDynamo(dynamoOptions, configuration.GetValue<string>("dynamoDB:tableName"),
            isDynamoEnabled);
        services.AddExclusions(
            dynamoOptions,
            configuration.GetValue<string>("dynamoDB:exclusionsTableName"),
            isDynamoEnabled
        );

        var authHubTokenEndpoint = configuration["AuthHub:TokenEndpoint"];
        var authHubClientId = configuration["AuthHub:ClientId"];
        var authHubClientSecret = configuration["AuthHub:ClientSecret"];
        var ampUserName = configuration["AMPUserName"];

        var openAmpConfig = new OpenAmpConfiguration
        {
            TokenEndpoint = authHubTokenEndpoint,
            ClientId = authHubClientId,
            ClientSecret = authHubClientSecret,
            AppId = Constants.AppId
        };
        configuration.GetSection("OpenAmp").Bind(openAmpConfig);
        services.AddOpenAmp(openAmpConfig, "OpenAmp");

        var rlBankingServiceConfig = new AuthenticatedServiceConfig
        {
            TokenEndpoint = authHubTokenEndpoint,
            ClientId = authHubClientId,
            ClientSecret = authHubClientSecret
        };
        configuration.Bind("RLBankingService", rlBankingServiceConfig);
        services.AddRLBankingServices(rlBankingServiceConfig, "RLBankingService");

        var rlUnderwritingServiceConfig = new AuthenticatedServiceConfig
        {
            TokenEndpoint = authHubTokenEndpoint,
            ClientId = authHubClientId,
            ClientSecret = authHubClientSecret
        };
        configuration.Bind("RLUnderwritingService", rlUnderwritingServiceConfig);
        services.AddRLUnderwritingServices(rlUnderwritingServiceConfig, "RLUnderwritingService");

        var piqServiceConfig = new AuthenticatedServiceConfig
        {
            TokenEndpoint = authHubTokenEndpoint,
            ClientId = authHubClientId,
            ClientSecret = authHubClientSecret
        };
        configuration.Bind("PropertyInsuranceQualifierService", piqServiceConfig);
        services.AddPropertyInsuranceQualifierServices(piqServiceConfig, "PropertyInsuranceQualifierService");

        var incomeQualifierServiceConfig = new IncomeQualifierServiceConfig
        {
            TokenEndpoint = authHubTokenEndpoint,
            ClientId = authHubClientId,
            ClientSecret = authHubClientSecret,
            AmpUserName = ampUserName
        };
        configuration.Bind("IncomeQualifierService", incomeQualifierServiceConfig);
        services.AddIncomeQualifierServices(incomeQualifierServiceConfig, "IncomeQualifierService");

        var creditQualifierServiceConfig = new AuthenticatedServiceConfig
        {
            TokenEndpoint = authHubTokenEndpoint,
            ClientId = authHubClientId,
            ClientSecret = authHubClientSecret
        };
        configuration.Bind("CreditQualifierService", creditQualifierServiceConfig);
        services.AddCreditQualifierService(creditQualifierServiceConfig, "CreditQualifierService");

        var propertyQualifierServiceConfig = new AuthenticatedServiceConfig
        {
            TokenEndpoint = authHubTokenEndpoint,
            ClientId = authHubClientId,
            ClientSecret = authHubClientSecret
        };
        configuration.Bind("PropertyQualifierService", propertyQualifierServiceConfig);
        services.AddPropertyQualifierService(propertyQualifierServiceConfig, "PropertyQualifierService");

        var rlApiConfig = new AuthenticatedServiceConfig
        {
            TokenEndpoint = authHubTokenEndpoint,
            ClientId = authHubClientId,
            ClientSecret = authHubClientSecret,
        };

        configuration.Bind("RlApi", rlApiConfig);
        services.AddRlApiServices(rlApiConfig, "RlApi");

        var modsConfiguration = new AuthenticatedServiceConfig
        {
            TokenEndpoint = authHubTokenEndpoint,
            ClientId = authHubClientId,
            ClientSecret = authHubClientSecret
        };

        configuration.Bind("Mods", modsConfiguration);
        services.AddModsServices(modsConfiguration, "Mods");

        var jobRunnerConfig = new AuthenticatedServiceConfig
        {
            TokenEndpoint = authHubTokenEndpoint,
            ClientId = authHubClientId,
            ClientSecret = authHubClientSecret
        };

        configuration.Bind("AmpJobRunner", jobRunnerConfig);
        services.AddAmpJobRunner(jobRunnerConfig, "AmpJobRunner");

        var rlDocumentStorageConfig = new AuthenticatedServiceConfig
        {
            TokenEndpoint = authHubTokenEndpoint,
            ClientId = authHubClientId,
            ClientSecret = authHubClientSecret
        };
        configuration.Bind("RocketLogicDocumentStorage", rlDocumentStorageConfig);

        services.AddRocketDocs(rlDocumentStorageConfig, "RocketLogicDocumentStorage");

        var docEntityRelationshipServiceConfig = new AuthenticatedServiceConfig
        {
            TokenEndpoint = authHubTokenEndpoint,
            ClientId = authHubClientId,
            ClientSecret = authHubClientSecret
        };
        configuration.Bind("Ders", docEntityRelationshipServiceConfig);
        services.AddDocEntityReleationshipService(docEntityRelationshipServiceConfig);

        var rlTasksConfig = new AuthenticatedServiceConfig
        {
            TokenEndpoint = authHubTokenEndpoint,
            ClientId = authHubClientId,
            ClientSecret = authHubClientSecret
        };
        configuration.Bind("RLTasks", rlTasksConfig);
        services.AddRLTasks(rlTasksConfig, "RLTasks");

        var parOrchestratorConfig = new AuthenticatedServiceConfig
        {
            TokenEndpoint = authHubTokenEndpoint,
            ClientId = authHubClientId,
            ClientSecret = authHubClientSecret
        };

        configuration.Bind("ParOrchestrator", parOrchestratorConfig);
        services.AddParOrchestrator(parOrchestratorConfig, "ParOrchestrator");

        var accessPopulationConfigurations = configuration
            .GetSection("AccessPopulationConfig")
            .Get<IEnumerable<AccessPopulationConfiguration>>();
        services.AddAccessPopulations(accessPopulationConfigurations);

        var licensingServiceConfig = new AuthenticatedServiceConfig
        {
            TokenEndpoint = authHubTokenEndpoint,
            ClientId = authHubClientId,
            ClientSecret = authHubClientSecret
        };
        configuration.Bind("LicensingService", licensingServiceConfig);
        services.AddLicensingService(licensingServiceConfig, "LicensingService");

        var specialAccessLoanNumbers = configuration.GetSection("SpecialAccessLoanNumbers");
        services.Configure<SpecialAccessLoanNumbers>(specialAccessLoanNumbers);
    }

    public void Configure(
        IApplicationBuilder app,
        IWebHostEnvironment env,
        IApiVersionDescriptionProvider apiVersionDescriptionProvider
    )
    {

        // Eagerly load services to prevent CPU spike on initial request
        using (var scope = app.ApplicationServices.CreateScope())
        {
            scope.ServiceProvider.GetRequiredService<IAuthorizationOrchestrator>();
        }

        app.UseRouting();

        app.UseMiddleware<ContextLoggerMiddleware>()
            .UseRouteDataValueLoggingMiddleware(options =>
            {
                options.CapitalizeKeys = true;
                options.AllowedKeys.Add("loanIdentifier");
                options.AllowedKeys.Add("Controller");
                options.AllowedKeys.Add("Action");
                options.RenamedKeys.Add("loanIdentifier", "RequestLoanIdentifier");
            })
            .UseCoreMiddleware(
                new CoreMiddlewareConfiguration { UseRequestResponseLogMiddleware = true }
            );

        app.UseCors();

        app.UseEndpoints(endpoints =>
        {
            endpoints.MapControllers();
            endpoints.MapHealthChecks("/health");
        });

        app.UseSwagger();
        app.UseSwaggerUI(options =>
        {
            var endpoints = apiVersionDescriptionProvider.ApiVersionDescriptions
                .OrderByDescending(description => description.ApiVersion)
                .Select(
                    description =>
                        (description.GroupName, $"/swagger/{description.GroupName}/swagger.json")
                );
            foreach (var (name, endpoint) in endpoints)
            {
                options.SwaggerEndpoint(endpoint, name);
            }
        });
    }
}
