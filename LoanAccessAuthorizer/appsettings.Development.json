{"LDAP": [{"ServerAddress": "ldapquery.mi.corp.rockfin.com", "Priority": 1, "BindingUserName": "svc-laabind", "ProviderType": "<PERSON>d", "Parameters": {"MinConnectionCount": 1, "StaleConnectionTimeInSeconds": 300}}, {"ServerAddress": "QH1DC2.qcloud.rockfin.com", "Priority": 2, "BindingUserName": "svc-laabind", "ProviderType": "Basic"}], "RLUnderwritingService": {"BaseUrl": "https://api.test.rocket-logic-uw-np.foc.zone", "TokenEndpoint": "https://sso.test.authrock.com/oauth/token", "Audience": "urn:ql-api:rocket-logic-uw-api-206872:Test"}, "RLBankingService": {"BaseUrl": "https://test-rlb-orch.np.rocket-logic.foc.zone", "Audience": "urn:ql-api:rlb_orchestrator-206156_test-206156:Test"}, "IncomeQualifierService": {"BaseUrl": "https://api.income-data-orchestrator.test.income-np.foc.zone", "Audience": "urn:ql-api:income_data_orchestrator-202140:Test"}, "PropertyInsuranceQualifierService": {"BaseUrl": "https://insurance-orchestrator-test.processingservices-np.foc.zone", "Audience": "urn:ql-api:insurance.orchestrator-208699:Test"}, "AuthHub": {"TokenEndpoint": "https://sso.test.authrock.com/oauth/token"}, "dynamoDB": {"ServiceUrl": "http://localhost:4566", "tableName": "local-laa-dev-123456", "exclusionsTableName": "local-laa-dev-123456-exclusions", "enabled": true}, "OpenAmp": {"BaseUrl": "https://oagateway.test.openamp-np.foc.zone/rocketlogic/", "Audience": "urn:ql-api:oagateway-208933:Test", "IsArchiveSupported": false}, "TestFeatures": {"SimulateArchivedLoans": true, "SimulatedArchivedLoanNumbers": ["*********", "*********"]}, "RlApi": {"BaseUrl": "https://api.test.np.rocket-logic.foc.zone", "Audience": "urn:ql-api:rocketlogicapi-210205:Test"}, "Mods": {"BaseUrl": "https://api.mods.test.einstein-np.foc.zone/", "Audience": "urn:ql-api:mortgage_origination_data_service-206483:Test"}, "CreditQualifierService": {"BaseUrl": "https://api.credit-data-orchestrator.test.credit-np.foc.zone", "Audience": "urn:ql-api:credit-data-orchestrator-206069:Test"}, "PropertyQualifierService": {"BaseUrl": "https://api.property-qualifier-orchestrator.test.property-np.foc.zone", "Audience": "urn:ql-api:pq-orchestrator-206067:Test"}, "ConfigurationManager": {"BaseUrl": "https://api.test.configuration-manager.np.rocket-logic.foc.zone", "Audience": "urn:ql-api:configurationmanagerapi-211304:Test"}, "AmpJobRunner": {"BaseUrl": "https://api.rl-amp-job-rnr.test.np.rocket-logic.foc.zone", "Audience": "urn:ql-api:rl-amp-job-runner-211434:Test"}, "RocketLogicDocumentStorage": {"BaseUrl": "https://api.document-storage.test.origination-documents-np.foc.zone", "Audience": "urn:ql-api:document_storage-206592:Test"}, "Ders": {"BaseUrl": "https://api.doc-entity-rel.test.mortgageops.foc.zone", "Audience": "urn:ql-api:document_entity_relationship_service-210034:Test"}, "RLTasks": {"BaseUrl": "https://api.tasks.test.np.rocket-logic.foc.zone/", "Audience": "urn:ql-api:rocketlogictasksapi-211057:Test"}, "ParOrchestrator": {"BaseUrl": "https://test-par-orchestrator.np.rocket-logic.foc.zone/", "Audience": "urn:ql-api:par-orchestrator-214035:Test"}, "LicensingService": {"BaseUrl": "https://licensing-api.test.licensetheworld.foc.zone/", "Audience": "urn:ql-api:licensingservice-202059:Test"}, "Metrics": {"Enabled": false}, "cacheConfig": {"hostNames": ["localhost"], "port": "6379", "abortOnConnect": false, "Enabled": false, "OperationTimeoutInMs": 300}, "TimeTrackingCheckService": {"BaseUrl": "https://beta-check-service.time-np.foc.zone/", "TokenEndpoint": "https://sso.beta.authrock.com/oauth/token", "Audience": "urn:ql-api:time-tracking-check-service-beta-214377:Beta"}}