using ConfigurationManager.Sdk.Provider;
using MicrosoftConfigurationManager = Microsoft.Extensions.Configuration.ConfigurationManager;

namespace LoanAccessAuthorizer.Configuration;

public static class ServiceConfiguration
{
    public static IConfigurationBuilder AddConfigurationManagerOptions(this MicrosoftConfigurationManager configuration)
    {
        var configurationManagerSource = GetConfigurationManagerSource(configuration);

        return configuration.AddConfigurationManager(source =>
        {
            source.TokenEndpoint = configurationManagerSource.TokenEndpoint;
            source.Audience = configurationManagerSource.Audience;
            source.ApplicationId = configurationManagerSource.ApplicationId;
            source.ClientId = configurationManagerSource.ClientId;
            source.ClientSecret = configurationManagerSource.ClientSecret;
            source.BaseUrl = configurationManagerSource.BaseUrl;
            source.RefreshCron = configurationManagerSource.RefreshCron;
        });
    }

    private static ConfigurationManagerSource GetConfigurationManagerSource(MicrosoftConfigurationManager configuration)
    {
        var configurationManagerSource = new LaaConfigurationManagerSource
        {
            TokenEndpoint = configuration["AuthHub:TokenEndpoint"],
            ClientId = configuration["AuthHub:ClientId"],
            ClientSecret = configuration["AuthHub:ClientSecret"]
        };

        configuration.GetSection("ConfigurationManager")
            .Bind(configurationManagerSource);
        configurationManagerSource.ApplicationId =
            $"{Constants.AppId}{configurationManagerSource.ApplicationIdPostfix}";

        return configurationManagerSource;
    }
}
