{"cacheConfig": {"hostNames": ["redis.laa.np.decision-services"], "port": "6379", "abortOnConnect": false, "Enabled": true, "OperationTimeoutInMs": 300}, "dynamoDB": {"tableName": "laa-train-202723", "exclusionsTableName": "laa-train-202723-exclusions", "enabled": true}, "RLUnderwritingService": {"BaseUrl": "https://api.beta.rocket-logic-uw-np.foc.zone", "Audience": "urn:ql-api:rocket-logic-uw-api-206872:Beta"}, "RLBankingService": {"BaseUrl": "https://uat-rlb-orch.np.rocket-logic.foc.zone", "Audience": "urn:ql-api:rlb_orchestrator-206156_beta-206156:Beta"}, "Mods": {"BaseUrl": "https://api.mods.train.einstein-np.foc.zone/", "Audience": "urn:ql-api:mortgage_origination_data_service-206483:Train"}, "OpenAmp": {"BaseUrl": "https://oagateway.uat.openamp-np.foc.zone/rocketlogic/", "Audience": "urn:ql-api:oagateway-208933:Beta", "IsArchiveSupported": false}, "IncomeQualifierService": {"BaseUrl": "https://api.income-data-orchestrator.train.income-np.foc.zone", "Audience": "urn:ql-api:income_data_orchestrator-202140:Beta"}, "PropertyInsuranceQualifierService": {"BaseUrl": "https://insurance-orchestrator-beta.processingservices-np.foc.zone", "Audience": "urn:ql-api:insurance.orchestrator-208699:Beta"}, "RlApi": {"BaseUrl": "https://api.train.np.rocket-logic.foc.zone", "Audience": "urn:ql-api:rocketlogicapi-210205:Beta"}, "CreditQualifierService": {"BaseUrl": "https://api.credit-data-orchestrator.train.credit-np.foc.zone", "Audience": "urn:ql-api:credit-data-orchestrator-206069:Beta"}, "PropertyQualifierService": {"BaseUrl": "https://api.property-qualifier-orchestrator.beta.property-np.foc.zone", "Audience": "urn:ql-api:pq-orchestrator-206067:Beta"}, "ConfigurationManager": {"BaseUrl": "https://api.beta.configuration-manager.np.rocket-logic.foc.zone", "Audience": "urn:ql-api:configurationmanagerapi-211304:Beta", "ApplicationIdPostfix": "-train"}, "AmpJobRunner": {"BaseUrl": "https://api.rl-amp-job-rnr.train.np.rocket-logic.foc.zone", "Audience": "urn:ql-api:rl-amp-job-runner-211434:Beta"}, "RocketLogicDocumentStorage": {"BaseUrl": "https://api.document-storage.beta.origination-documents-np.foc.zone", "Audience": "urn:ql-api:document_storage-206592:Beta"}, "Ders": {"BaseUrl": "https://api.doc-entity-rel.beta.mortgageops.foc.zone", "Audience": "urn:ql-api:document_entity_relationship_service-210034:Beta"}, "RLTasks": {"BaseUrl": "https://api.tasks.beta.np.rocket-logic.foc.zone/", "Audience": "urn:ql-api:rocketlogictasksapi-211057:Beta"}, "ParOrchestrator": {"BaseUrl": "https://beta-par-orchestrator.np.rocket-logic.foc.zone/", "Audience": "urn:ql-api:par-orchestrator-214035:Beta"}, "LicensingService": {"BaseUrl": "https://licensing-api.beta.licensetheworld.foc.zone/", "Audience": "urn:ql-api:licensingservice-202059:Beta"}, "AuthHub": {"TokenEndpoint": "https://sso.beta.authrock.com/oauth/token"}, "TimeTrackingCheckService": {"BaseUrl": "https://beta-check-service.time-np.foc.zone/", "TokenEndpoint": "https://sso.beta.authrock.com/oauth/token", "Audience": "urn:ql-api:time-tracking-check-service-beta-214377:Beta"}}