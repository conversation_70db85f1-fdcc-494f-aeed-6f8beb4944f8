FROM docker.artifactory.foc.zone/rocket-logic/dotnet.glibc.dynatrace:1.2 as oneagent-builder
FROM mcr.microsoft.com/dotnet/aspnet:8.0

RUN apt-get update
RUN apt-get install curl -y

################################################################################
# CERTIFICATES
################################################################################
RUN curl -sL "https://tools.circleci.foc.zone/install-certs" | bash -

ENV DT_HOME="/opt/dynatrace/oneagent"

COPY --from=oneagent-builder $DT_HOME $DT_HOME

WORKDIR /app
COPY /dist .

ENTRYPOINT [ "dotnet", "LoanAccessAuthorizer.dll" ]
