using System.ComponentModel.DataAnnotations;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;

namespace LoanAccessAuthorizer.Models.Requests;

public class CreateManualExclusionRequest
{
    [Required]
    public string AppId { get; set; }
    [Required]
    public ExclusionReason? Reason { get; set; } // Making this nullable so that validation will fail if the property is not specified

    private string _comment = string.Empty;
    [StringLength(100)]
    public string Comment
    {
        get => _comment;
        set => _comment = string.IsNullOrWhiteSpace(value) ? string.Empty : value;
    }
}
