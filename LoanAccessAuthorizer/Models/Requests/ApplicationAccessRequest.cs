using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc;

namespace LoanAccessAuthorizer.Models.Requests;

public class ApplicationAccessRequest
{
    [FromRoute]
    public string LoanIdentifier { get; set; }
    [Required]
    [FromHeader(Name = "x-common-id")]
    public string CommonId { get; set; }
    [FromHeader(Name = "x-amp-id")]
    public string AmpUsername { get; set; }
    [Required]
    [FromBody]
    public IEnumerable<string> AppIds { get; set; }
}