using System.ComponentModel.DataAnnotations;

namespace LoanAccessAuthorizer.Models;

public class AccessResult : IValidatableObject
{
    public bool AccessDecision { get; set; }

    public IEnumerable<string> Features { get; set; }

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        var hasFeatures = Features?.Any() ?? false;
        if (!hasFeatures)
        {
            yield break;
        }

        if (!AccessDecision)
        {
            yield return new ValidationResult(
                "Features cannot be specified when AccessDecision is false.",
                [nameof(Features)]
            );
        }

        if (Features.Any(string.IsNullOrWhiteSpace))
        {
            yield return new ValidationResult(
                "Items in Features cannot be null or whitespace.",
                [nameof(Features)]
            );
        }
    }
}
