{"Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}}, "Metrics": {"AppId": "202723", "InfluxConfiguration": {"Enabled": false}, "OpenTelemetryConfiguration": {"Enabled": true}}, "MetricsEnabled": true, "LDAP": [{"ServerAddress": "ldapquery.mi.corp.rockfin.com", "Priority": 1, "BindingUserName": "svc-laabind", "ProviderType": "<PERSON>d", "Parameters": {"MinConnectionCount": 10, "StaleConnectionTimeInSeconds": 300}}, {"ServerAddress": "QH1DC2.qcloud.rockfin.com", "Priority": 2, "BindingUserName": "svc-laabind", "ProviderType": "<PERSON>d", "Parameters": {"MinConnectionCount": 0, "StaleConnectionTimeInSeconds": 300}}], "HttpPolicies": {"OpenAmp": {"NumberOfRetries": 3, "BackoffTimeoutInMilliseconds": 50, "IndividualRequestTimeoutInMilliseconds": 750}, "RLUnderwritingService": {"NumberOfRetries": 3, "BackOffTimeoutInMilliseconds": 50, "IndividualRequestTimeoutInMilliseconds": 1000}, "Mods": {"NumberOfRetries": 3, "BackOffTimeoutInMilliseconds": 50, "IndividualRequestTimeoutInMilliseconds": 750}, "RLBankingService": {"NumberOfRetries": 3, "BackOffTimeoutInMilliseconds": 50, "IndividualRequestTimeoutInMilliseconds": 3000}, "IncomeQualifierService": {"NumberOfRetries": 3, "BackOffTimeoutInMilliseconds": 50, "IndividualRequestTimeoutInMilliseconds": 750}, "CreditQualifierService": {"NumberOfRetries": 3, "BackOffTimeoutInMilliseconds": 50, "IndividualRequestTimeoutInMilliseconds": 750}, "PropertyQualifierService": {"NumberOfRetries": 3, "BackOffTimeoutInMilliseconds": 50, "IndividualRequestTimeoutInMilliseconds": 750}, "PropertyInsuranceQualifierService": {"NumberOfRetries": 3, "BackOffTimeoutInMilliseconds": 50, "IndividualRequestTimeoutInMilliseconds": 750}, "RlApi": {"NumberOfRetries": 3, "BackOffTimeoutInMilliseconds": 50, "IndividualRequestTimeoutInMilliseconds": 750}, "ParOrchestrator": {"NumberOfRetries": 3, "BackOffTimeoutInMilliseconds": 50, "IndividualRequestTimeoutInMilliseconds": 750}, "LicensingService": {"NumberOfRetries": 3, "BackOffTimeoutInMilliseconds": 50, "IndividualRequestTimeoutInMilliseconds": 750}, "TeamMemberDataService": {"NumberOfRetries": 3, "BackOffTimeoutInMilliseconds": 50, "IndividualRequestTimeoutInMilliseconds": 750}, "TimeTrackingCheckService": {"NumberOfRetries": 3, "BackOffTimeoutInMilliseconds": 50, "IndividualRequestTimeoutInMilliseconds": 5000}}, "OpenAmp": {"IsArchiveSupported": true}, "TeamMemberDataService": {"BaseUrl": "https://graphql-beta.tmds-np.foc.zone", "TokenEndpoint": "https://sso.beta.authrock.com/oauth/token", "Audience": "urn:ql-api:tmds-nonprod-graphql-api-206427:Beta"}, "ConfigurationManager": {"RefreshCron": "*/5 * * * *"}, "TimeTrackingCheckService": {"BaseUrl": "https://beta-check-service.time-np.foc.zone/", "TokenEndpoint": "https://sso.beta.authrock.com/oauth/token", "Audience": "urn:ql-api:time-tracking-check-service-beta-214377:Beta"}, "AMPUserName": "laaapi1", "APP_ID": "202723", "SpecialAccessLoanNumbers": {"LoanNumbers": []}, "dynamoDB": {"enabled": true}}