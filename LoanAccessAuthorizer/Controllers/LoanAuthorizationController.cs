using System.ComponentModel.DataAnnotations;
using LoanAccessAuthorizer.Authorization.Rules;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.Exceptions;
using LoanAccessAuthorizer.Common;
using LoanAccessAuthorizer.Configuration;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.Authorization;
using LoanAccessAuthorizer.Domain.Models.Response;
using LoanAccessAuthorizer.Exclusions;
using LoanAccessAuthorizer.Models.Requests;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.Options;

namespace LoanAccessAuthorizer.Controllers;

[Route("/")]
[ApiController]
[ApiVersion("1.0")]
[ApiVersion("2.0")]
[ApiVersion("3.0")]
[ApiVersion("4.0")]
public class LoanAuthorizationController : ControllerBase
{
    private readonly IAuthorizationOrchestrator _authorizationOrchestrator;
    private readonly IApplicationAuthorizerDecisionStore _applicationAuthorizerDecisionStore;
    private readonly ApplicationIdResolver _applicationIdResolver;
    private readonly IExclusionsRepository _exclusionsRepository;

    private readonly ISet<string> _specialAccessCommonIds = new HashSet<string>
    {
        "1000123",
        "2011078",
        "1001998",
        "1002899",
        "1002244",
        "1005885",
        "1000091"
    };

    private readonly SpecialAccessLoanNumbers _specialAccessLoanNumbers;

    public LoanAuthorizationController(
        IAuthorizationOrchestrator authorizationOrchestrator,
        IApplicationAuthorizerDecisionStore applicationAuthorizerDecisionStore,
        ApplicationIdResolver applicationIdResolver,
        IExclusionsRepository exclusionsRepository,
        IOptionsSnapshot<SpecialAccessLoanNumbers> specialAccessLoanNumbers
    )
    {
        _authorizationOrchestrator = authorizationOrchestrator;
        _applicationAuthorizerDecisionStore = applicationAuthorizerDecisionStore;
        _applicationIdResolver = applicationIdResolver;
        _exclusionsRepository = exclusionsRepository;
        _specialAccessLoanNumbers = specialAccessLoanNumbers.Value;
    }

    [HttpGet]
    [Route("loan/{loanIdentifier}/application/{applicationIdentifier}/access")]
    [MapToApiVersion("1.0")]
    public async Task<ActionResult<bool>> IsLoanAccessibleInApplication(
        string loanIdentifier,
        string applicationIdentifier
    )
    {
        var accessResult = await _authorizationOrchestrator.IsAuthorized(
            loanIdentifier,
            applicationIdentifier
        );
        return Ok(accessResult.AccessDecision);
    }

    [HttpGet]
    [Route("loan/{loanIdentifier}/application/{applicationIdentifier}/access")]
    [MapToApiVersion("2.0")]
    [MapToApiVersion("3.0")]
    [MapToApiVersion("4.0")]
    public async Task<ActionResult<Models.AccessResult>> IsLoanAccessibleInApplicationV2(
        string loanIdentifier,
        string applicationIdentifier,
        [FromHeader(Name = "x-common-id")] string commonId,
        [FromHeader(Name = "x-rock-human-id")] string rockHumanId
    )
    {
        var accessResult = await _authorizationOrchestrator.IsAuthorized(
            loanIdentifier,
            applicationIdentifier,
            commonId,
            rockHumanId
        );
        var result = new Models.AccessResult
        {
            AccessDecision = accessResult.AccessDecision,
            Features = accessResult.Features
        };
        return Ok(result);
    }

    [HttpPost]
    [Route("loan/{loanIdentifier}/application/access")]
    [MapToApiVersion("1.0")]
    [MapToApiVersion("2.0")]
    [MapToApiVersion("3.0")]
    // NOTE: We're not resolving application id's on this endpoint because it would be a breaking change as
    // the result's return with the casing on request and the orchestrator will resolve the identifier internally
    public async Task<ActionResult<IReadOnlyDictionary<string, bool>>> IsLoanAccessibleInApplications(
        string loanIdentifier,
        [FromBody] IEnumerable<string> appIds,
        [FromHeader(Name = "x-common-id")] string commonId,
        [FromHeader(Name = "x-rock-human-id")] string rockHumanId
    )
    {
        return Ok(
            await _authorizationOrchestrator.AreAuthorized(
                loanIdentifier,
                appIds,
                commonId,
                rockHumanId
            )
        );
    }

    [HttpPost]
    [Route("loan/{loanIdentifier}/application/access")]
    [MapToApiVersion("4.0")]
    // NOTE: We're not resolving application id's on this endpoint because it would be a breaking change as
    // the result's return with the casing on request and the orchestrator will resolve the identifier internally
    public async Task<ActionResult<IReadOnlyDictionary<string, Models.AccessResult>>> GetDetailedAccessResults(
        string loanIdentifier,
        [FromBody] IEnumerable<string> appIds,
        [FromHeader(Name = "x-common-id")] string commonId,
        [FromHeader(Name = "x-rock-human-id")] string rockHumanId
    )
    {
        var accessResults = (await _authorizationOrchestrator.AreAuthorizedAccessResults(
            loanIdentifier,
            appIds,
            commonId,
            rockHumanId
        )).ToDictionary(
            kv => kv.Key,
            kv => new Models.AccessResult
            {
                AccessDecision = kv.Value.AccessDecision,
                Features = kv.Value.Features
            }
        );
        return Ok(accessResults);
    }

    [HttpPost]
    [Route("loan/{loanIdentifier}/user-access")]
    [MapToApiVersion("1.0")]
    [MapToApiVersion("2.0")]
    // NOTE: We're not resolving application id's on this endpoint because it would be a breaking change as
    // the result's return with the casing on request and the orchestrator will resolve the identifier internally
    public async Task<
        ActionResult<IReadOnlyDictionary<string, ApplicationAuthorizationResponse>>
    > GetApplicationsAccessForLoanAndUser([FromQuery] ApplicationAccessRequest request)
    {
        if (HasSpecialAccessRequired(request.LoanIdentifier, request.CommonId))
        {
            return Ok(_authorizationOrchestrator.CreateNoAccessResponseForAppIds(request.AppIds));
        }

        var authorizationResponse =
            await _authorizationOrchestrator.GetAuthorizationResponseForApplications(
                request.LoanIdentifier,
                request.AppIds,
                request.CommonId,
                request.AmpUsername
            );

        return Ok(authorizationResponse.ApplicationAuth);
    }

    [HttpPost]
    [Route("loan/{loanIdentifier}/user-access")]
    [MapToApiVersion("3.0")]
    [MapToApiVersion("4.0")]
    // NOTE: We're not resolving application id's on this endpoint because it would be a breaking change as
    // the result's return with the casing on request and the orchestrator will resolve the identifier internally
    public async Task<ActionResult<AuthorizationResponse>> GetApplicationsAccessForLoanAndUserV3(
        [FromQuery] ApplicationAccessRequest request
    )
    {
        if (HasSpecialAccessRequired(request.LoanIdentifier, request.CommonId))
        {
            return new AuthorizationResponse(
                false,
                _authorizationOrchestrator.CreateNoAccessResponseForAppIds(request.AppIds),
                AuthorizationErrorCode.UserNotAllowedToAccessTeamMemberLoans
            );
        }

        return Ok(
            await _authorizationOrchestrator.GetAuthorizationResponseForApplications(
                request.LoanIdentifier,
                request.AppIds,
                request.CommonId,
                request.AmpUsername
            )
        );
    }

    [HttpDelete]
    [Route("loan/{loanIdentifier}/application/{applicationIdentifier}/access")]
    [Obsolete($"Replaced by the {nameof(RemoveApplicationAuthorizations)} method.")]
    public async Task<ActionResult> RemoveApplicationAuthorization(
        string loanIdentifier,
        string applicationIdentifier,
        [FromHeader(Name = Headers.RequestSource)] string sourceType,
        [FromQuery] bool skipCanClearCheck = false
    )
    {
        var resolvedApplicationId = _applicationIdResolver.Resolve(applicationIdentifier);
        if (!skipCanClearCheck)
        {
            var canClearDecision = await _authorizationOrchestrator.CanClearDecision(
                loanIdentifier,
                resolvedApplicationId
            );
            if (!canClearDecision)
            {
                throw new MethodNotAllowedException(
                    $"Can't Remove Decision for loan: {loanIdentifier}"
                );
            }
        }

        var removeDecisionTask = _applicationAuthorizerDecisionStore.RemoveDecision(
            loanIdentifier,
            resolvedApplicationId
        );

        var username = string.IsNullOrWhiteSpace(sourceType) ? resolvedApplicationId : sourceType;
        var removeExclusionsTask = _exclusionsRepository.DeleteExclusions(
            loanIdentifier,
            resolvedApplicationId,
            username
        );
        await Task.WhenAll(removeDecisionTask, removeExclusionsTask);

        return Ok();
    }

    [HttpDelete]
    [Route("loan/{loanIdentifier}/application/access")]
    [MapToApiVersion("1.0")]
    [MapToApiVersion("2.0")]
    [MapToApiVersion("3.0")]
    [MapToApiVersion("4.0")]
    public async Task<ActionResult> RemoveApplicationAuthorizations(
        string loanIdentifier,
        [FromQuery(Name = "ApplicationId")] IEnumerable<string> applicationIdentifiers,
        [FromHeader(Name = Headers.RequestSource)] string sourceType,
        [FromQuery] bool skipCanClearCheck = false
    )
    {
        if (applicationIdentifiers == null || !applicationIdentifiers.Any())
        {
            return Ok();
        }

        var applicationsToClearTask = applicationIdentifiers
            .Select(_applicationIdResolver.Resolve)
            .Select(async application =>
            {
                var canClearDecision =
                    skipCanClearCheck
                    || await _authorizationOrchestrator.CanClearDecision(
                        loanIdentifier,
                        application
                    );

                if (!canClearDecision)
                {
                    return (application, canClearDecision);
                }

                var removeDecisionTask = _applicationAuthorizerDecisionStore.RemoveDecision(
                    loanIdentifier,
                    application
                );

                var username = string.IsNullOrWhiteSpace(sourceType) ? application : sourceType;
                var removeExclusionsTask = _exclusionsRepository.DeleteExclusions(
                    loanIdentifier,
                    application,
                    username
                );
                await Task.WhenAll(removeDecisionTask, removeExclusionsTask);

                return (application, canClearDecision);
            });

        var applicationClearResult = await Task.WhenAll(applicationsToClearTask);

        return Ok(
            applicationClearResult.ToDictionary(
                result => result.application,
                result => result.canClearDecision
            )
        );
    }

    [HttpPut]
    [Route("loan/{loanIdentifier}/application/{applicationIdentifier}/access")]
    [MapToApiVersion("1.0")]
    [MapToApiVersion("2.0")]
    [MapToApiVersion("3.0")]
    [MapToApiVersion("4.0")]
    public async Task<ActionResult> OverrideApplicationAuthorization(
        string loanIdentifier,
        string applicationIdentifier,
        bool decision,
        [FromBody(EmptyBodyBehavior = EmptyBodyBehavior.Allow)] IEnumerable<string> features,
        [FromHeader(Name = Headers.RequestSource)] string sourceType
    )
    {
        var access = new Models.AccessResult { AccessDecision = decision, Features = features };
        var validationResult = access.Validate(null);
        if (validationResult.Any())
        {
            return BadRequest(validationResult);
        }

        await LoanAccessControllerHelper.OverrideApplicationAuthorization(
            loanIdentifier,
            applicationIdentifier,
            decision,
            features,
            sourceType,
            _applicationIdResolver,
            _applicationAuthorizerDecisionStore,
            _exclusionsRepository
        );
        return Ok();
    }

    [HttpPut]
    [Route("loan/{loanIdentifier}/application/access")]
    [MapToApiVersion("4.0")]
    public async Task<ActionResult> OverrideApplicationAuthorizations(
        string loanIdentifier,
        [Required, FromBody] IDictionary<string, Models.AccessResult> decisions,
        [FromHeader(Name = Headers.RequestSource)] string sourceType
    )
    {
        var tasks = decisions.Select(x => LoanAccessControllerHelper.OverrideApplicationAuthorization(
            loanIdentifier,
            x.Key,
            x.Value.AccessDecision,
            x.Value.Features,
            sourceType,
            _applicationIdResolver,
            _applicationAuthorizerDecisionStore,
            _exclusionsRepository
        ));
        await Task.WhenAll(tasks);
        return Ok();
    }

    private bool HasSpecialAccessRequired(string loanIdentifier, string commonId)
    {
        return _specialAccessLoanNumbers.LoanNumbers is not null
            && _specialAccessLoanNumbers.LoanNumbers.Contains(loanIdentifier)
            && !_specialAccessCommonIds.Contains(commonId);
    }
}
