using LoanAccessAuthorizer.Authorization.Rules;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;
using LoanAccessAuthorizer.Exclusions;

namespace LoanAccessAuthorizer.Controllers;

public static class LoanAccessControllerHelper
{
    public static async Task OverrideApplicationAuthorization(
        string loanIdentifier,
        string applicationIdentifier,
        bool decision,
        IEnumerable<string> features,
        string sourceType,
        ApplicationIdResolver applicationIdResolver,
        IApplicationAuthorizerDecisionStore applicationAuthorizerDecisionStore,
        IExclusionsRepository exclusionsRepository
    )
    {
        var resolvedApplicationId = applicationIdResolver.Resolve(applicationIdentifier);
        var storeDecisionTask = applicationAuthorizerDecisionStore.StoreDecision(
            loanIdentifier,
            resolvedApplicationId,
            decision
        );

        var username = string.IsNullOrWhiteSpace(sourceType) ? applicationIdentifier : sourceType;
        var updateExclusionsTask = UpdateExclusions(
            loanIdentifier,
            resolvedApplicationId,
            decision,
            features,
            username,
            exclusionsRepository
        );

        await Task.WhenAll(storeDecisionTask, updateExclusionsTask);
    }

    private static async Task UpdateExclusions(
        string loanIdentifier,
        string applicationIdentifier,
        bool decision,
        IEnumerable<string> features,
        string username,
        IExclusionsRepository exclusionsRepository
    )
    {
        if (decision)
        {
            await exclusionsRepository.DeleteExclusions(
                loanIdentifier,
                applicationIdentifier,
                username
            );
            await exclusionsRepository.PutExclusion(
                loanIdentifier,
                new Exclusions.Models.Exclusion
                {
                    AppId = applicationIdentifier,
                    Reason = null,
                    ExcludedAt = DateTime.Now,
                    Features = features,
                    User = username
                }
            );
            return;
        }

        // The Decision is false, and features only make sense if a decision is true. So, clear out the features.
        // Features are only stored on an exclusion with a null reason.
        await exclusionsRepository.DeleteExclusion(loanIdentifier, applicationIdentifier, null, username);

        // Make sure we have at least one non-null reason exclusion to mark the loan not in pilot.
        var exclusion = new Exclusions.Models.Exclusion
        {
            AppId = applicationIdentifier,
            Reason = ExclusionReason.NotInPilot,
            ExcludedAt = DateTime.Now,
            Comment = "Not in pilot",
            User = username
        };
        await exclusionsRepository.PutExclusion(loanIdentifier, exclusion);
    }
}
