using LoanAccessAuthorizer.Authorization.Rules;
using LoanAccessAuthorizer.Common;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.Authorization;
using LoanAccessAuthorizer.Domain.Models;
using LoanAccessAuthorizer.Domain.Models.Response;
using LoanAccessAuthorizer.Domain.TeamMemberData;
using LoanAccessAuthorizer.Exclusions;
using LoanAccessAuthorizer.Exclusions.Models;
using LoanAccessAuthorizer.LicensingService;
using LoanAccessAuthorizer.Models.Requests;
using Microsoft.AspNetCore.Mvc;
using Property.DataModels.Enums;

namespace LoanAccessAuthorizer.Controllers;
[Route("admin")]
[ApiController]
[ApiVersion("1.0")]
[ApiVersion("2.0")]
[ApiVersion("3.0")]
[ApiVersion("4.0")]
public class AdminController : ControllerBase
{
    private readonly IAuthorizationOrchestrator _authorizationOrchestrator;
    private readonly IApplicationAuthorizerDecisionStore _applicationAuthorizerDecisionStore;
    private readonly IExclusionsRepository _exclusionsRepository;
    private readonly ApplicationIdResolver _applicationIdResolver;
    private readonly ILeaderHierarchyProvider _leaderHierarchyProvider;
    private readonly LicensingServiceProvider _licensingServiceProvider;
    public AdminController(IAuthorizationOrchestrator authorizationOrchestrator,
        ApplicationIdResolver applicationIdResolver,
        IExclusionsRepository exclusionsRepository,
        IApplicationAuthorizerDecisionStore applicationAuthorizerDecisionStore,
        ILeaderHierarchyProvider leaderHierarchyProvider,
        LicensingServiceProvider licensingServiceProvider)
    {
        _authorizationOrchestrator = authorizationOrchestrator;
        _applicationIdResolver = applicationIdResolver;
        _exclusionsRepository = exclusionsRepository;
        _applicationAuthorizerDecisionStore = applicationAuthorizerDecisionStore;
        _leaderHierarchyProvider = leaderHierarchyProvider;
        _licensingServiceProvider = licensingServiceProvider;
    }

    [HttpGet]
    [Route("loan/{loanIdentifier}/application/access")]
    public async Task<ActionResult> GetAllAccess(
       string loanIdentifier,
       [FromHeader(Name = Headers.RequestSource)] string sourceType)
    {
        return Ok(await _authorizationOrchestrator.AreAuthorized(loanIdentifier, Authorization.Rules.ApplicationId.All));
    }

    [Obsolete($"use methods in {nameof(LoanAuthorizationController)} instead.")]
    [HttpPut]
    [Route("loan/{loanIdentifier}/application/access")]
    public async Task<ActionResult> OverrideApplicationAuthorization(
       string loanIdentifier,
       [FromBody] IEnumerable<string> applicationIdentifiers,
       [FromQuery] bool decision,
       [FromHeader(Name = Headers.RequestSource)] string sourceType)
    {
        var accessTasks = applicationIdentifiers
            .Select(applicationIdentifier =>
            OverrideAuth(loanIdentifier, decision, sourceType, applicationIdentifier));
        await Task.WhenAll(accessTasks);
        return Ok();
    }

    [HttpPut]
    [Route("override/bankerLicense")]
    [ProducesResponseType(typeof(ExclusionInformation), StatusCodes.Status200OK)]
    public async Task<ActionResult> OverrideBankerLicenseCache(
        [FromBody] BankerLicenseOverrideRequest bankerLicenseOverrides
    )
    {
        var results = await OverrideBankerLicense(bankerLicenseOverrides.CommonIds, bankerLicenseOverrides.State);

        if (results.Any(result => !result.Success))
        {
            return Ok(results.Where(result => !result.Success));
        }
        return Ok();
    }


    private async Task OverrideAuth(string loanIdentifier, bool decision, string sourceType, string applicationIdentifier)
    {
        try
        {
            await LoanAccessControllerHelper.OverrideApplicationAuthorization(
                    loanIdentifier, applicationIdentifier, decision, null, sourceType, _applicationIdResolver,
                    _applicationAuthorizerDecisionStore, _exclusionsRepository);
        }
        catch (ArgumentOutOfRangeException)
        {
            //Swallow the exception because we want to ignore any application Id that doesn't have a decider
        }
    }

    private async Task<IEnumerable<BankerLicenseOverrideResult>> OverrideBankerLicense(
        IEnumerable<string> commonIds,
        State state)
    {
        var tasks = commonIds.Select(async commonId =>
        {
            var nmlsId = (await _leaderHierarchyProvider.GetLeaderHierarchy(UserId.FromCommonId(commonId)))?.NmlsId;
            return await _licensingServiceProvider.AddOverrideState(commonId, nmlsId, state.ToString());
        });

        var results = await Task.WhenAll(tasks);

        return results;
    }
}
