using System;
using System.Threading.Tasks;
using DecisionServices.Core.Cache;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Controllers
{
    /// <summary>
    /// Test-only controller for simulating different scenarios in non-production environments
    /// This controller should be conditionally registered only in non-production environments
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class TestingController : ControllerBase
    {
        private readonly ICache _cache;
        private readonly IConfiguration _configuration;
        private readonly ILogger<TestingController> _logger;

        public TestingController(ICache cache, IConfiguration configuration, ILogger<TestingController> logger)
        {
            _cache = cache;
            _configuration = configuration;
            _logger = logger;
        }
        
        /// <summary>
        /// Simulates an archived loan by adding a cache entry
        /// </summary>
        /// <param name="loanNumber">The loan number to mark as archived</param>
        /// <param name="durationHours">How long (in hours) to keep this simulation active</param>
        [HttpPost("simulate-archived-loan/{loanNumber}")]
        public async Task<IActionResult> SimulateArchivedLoan(
            string loanNumber, 
            [FromQuery] int durationHours = 1)
        {
            // Safety check - don't allow in production
            if (IsProduction())
            {
                return BadRequest("This endpoint is not available in production environments");
            }

            // Use "IsLoanArchivedInAmp" as the identifier - matches IsLoanArchivedProvider's Identifier
            var cacheKey = new CacheKey(loanNumber, "IsLoanArchivedInAmp");
            // Mock application number - the IsLoanArchivedProvider checks if this is non-empty
            var mockArchivedAppNumber = $"SIMULATED-{loanNumber}-{DateTime.UtcNow:yyyyMMdd}";
            
            // Cache the simulated application number with expiration
            var duration = TimeSpan.FromHours(durationHours);
            var success = await _cache.Set(cacheKey, mockArchivedAppNumber, duration);
            
            if (success)
            {
                _logger.LogInformation(
                    "TESTING: Loan {LoanNumber} will be simulated as archived for the next {Hours} hours", 
                    loanNumber, durationHours);
                
                return Ok(new
                {
                    loanNumber,
                    status = "Simulated as archived",
                    expiresAt = DateTime.UtcNow.Add(duration),
                    message = $"Loan {loanNumber} will be treated as archived for {durationHours} hours"
                });
            }
            
            return StatusCode(500, "Failed to set cache entry");
        }
        
        /// <summary>
        /// Removes archive simulation for a loan
        /// </summary>
        [HttpDelete("simulate-archived-loan/{loanNumber}")]
        public async Task<IActionResult> RemoveArchivedLoanSimulation(string loanNumber)
        {
            // Safety check - don't allow in production
            if (IsProduction())
            {
                return BadRequest("This endpoint is not available in production environments");
            }
            
            var cacheKey = new CacheKey(loanNumber, "IsLoanArchivedInAmp");
            // Remove the value from cache
            await _cache.Delete(cacheKey);
            
            _logger.LogInformation("TESTING: Removed archived simulation for loan {LoanNumber}", loanNumber);
            
            return Ok(new
            {
                loanNumber,
                status = "Simulation removed",
                message = $"Archive simulation removed for loan {loanNumber}"
            });
        }
        
        /// <summary>
        /// Gets the current status of an archived loan simulation
        /// </summary>
        [HttpGet("simulate-archived-loan/{loanNumber}")]
        public async Task<IActionResult> GetArchivedLoanSimulationStatus(string loanNumber)
        {
            // Safety check - don't allow in production
            if (IsProduction())
            {
                return BadRequest("This endpoint is not available in production environments");
            }
            
            // Use "IsLoanArchivedInAmp" as the identifier - matches IsLoanArchivedProvider's Identifier
            var cacheKey = new CacheKey(loanNumber, "IsLoanArchivedInAmp");
            var cachedValue = await _cache.Get(cacheKey);
            
            var isSimulated = !string.IsNullOrEmpty(cachedValue);
            
            return Ok(new
            {
                loanNumber,
                isSimulatedAsArchived = isSimulated,
                simulationValue = cachedValue,
                message = isSimulated 
                    ? $"Loan {loanNumber} is currently simulated as archived" 
                    : $"Loan {loanNumber} is not simulated as archived"
            });
        }
        
        private bool IsProduction()
        {
            var environment = _configuration["ASPNETCORE_ENVIRONMENT"] ?? 
                              _configuration["Environment"] ?? 
                              "Development";
                              
            return environment.Equals("Production", StringComparison.OrdinalIgnoreCase);
        }
    }
}
