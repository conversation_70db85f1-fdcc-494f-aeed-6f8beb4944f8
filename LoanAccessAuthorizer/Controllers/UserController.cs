using System.ComponentModel.DataAnnotations;
using LoanAccessAuthorizer.Authorization.Rules.TeamMember;
using Microsoft.AspNetCore.Mvc;
using LoanAccessAuthorizer.Models.Responses.TeamMemberPilotStatus;

namespace LoanAccessAuthorizer.Controllers;

[Route("user/{commonId}")]
[ApiController]
[ApiVersion("1.0")]
[ApiVersion("2.0")]
[ApiVersion("3.0")]
[ApiVersion("4.0")]
public class UserController : Controller
{
    private readonly ITeamMemberPilotAccessService _teamMemberPilotAccessService;

    public UserController(ITeamMemberPilotAccessService teamMemberPilotAccessService)
    {
        _teamMemberPilotAccessService = teamMemberPilotAccessService;
    }

    [HttpGet("pilot")]
    public async Task<IEnumerable<TeamMemberPilotStatus>> GetPilotAccess([Required] [FromRoute] string commonId) =>
        (await _teamMemberPilotAccessService.GetTeamMemberPilotStatuses(commonId))?.Select(Map);

    [HttpGet("appid/{appId}/pilot")]
    public async Task<IEnumerable<TeamMemberPilotStatus>> GetPilotAccess([Required] [FromRoute] string commonId,
        [Required] [FromRoute] string appId) =>
        (await _teamMemberPilotAccessService.GetTeamMemberPilotStatus(commonId, appId))?.Select(Map);

    [HttpGet("leader/pilot")]
    public async Task<TeamMemberLeaderPilotStatus> GetLeaderPilotAccess([Required] [FromRoute] string commonId) =>
        Map(await _teamMemberPilotAccessService.GetTeamMemberLeaderPilotStatuses(commonId));

    [HttpGet("appid/{appId}/leader/pilot")]
    public async Task<TeamMemberLeaderPilotStatus> GetLeaderPilotAccess([Required] [FromRoute] string commonId,
        [Required] [FromRoute] string appId) =>
        Map(await _teamMemberPilotAccessService.GetTeamMemberLeaderPilotStatus(commonId, appId));

    private static TeamMemberPilotStatus Map(Authorization.Rules.TeamMember.Models.TeamMemberPilotStatus statuses) =>
        new()
        {
            ApplicationId = statuses.ApplicationId,
            IsInPilot = statuses.IsInPilot
        };

    private static TeamMemberLeaderPilotStatus
        Map(Authorization.Rules.TeamMember.Models.TeamMemberLeaderPilotStatus leaderPilotStatus) =>
        new()
        {
            Statuses = leaderPilotStatus.Statuses.Select(Map),
            LeaderHierarchy = leaderPilotStatus.LeaderHierarchy
        };
}
