using System.ComponentModel.DataAnnotations;
using LoanAccessAuthorizer.Authorization.Rules;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Exclusions;
using LoanAccessAuthorizer.Exclusions.Models;
using LoanAccessAuthorizer.Models.Requests;
using LoanAccessAuthorizer.Models.Responses;
using Microsoft.AspNetCore.Mvc;
using ExclusionReason = LoanAccessAuthorizer.Domain.PilotExclusions.Models.ExclusionReason;

namespace LoanAccessAuthorizer.Controllers;

[Route("/exclusion")]
[ApiController]
[ApiVersion("1.0")]
[ApiVersion("2.0")]
[ApiVersion("3.0")]
[ApiVersion("4.0")]
public class ExclusionsController : ControllerBase
{
    private readonly IApplicationAuthorizerDecisionStore _decisionsRepo;
    private readonly IExclusionsRepository _exclusionsRepo;
    private readonly ApplicationIdResolver _applicationIdResolver;

    public ExclusionsController(
        IApplicationAuthorizerDecisionStore decisionsRepo,
        IExclusionsRepository exclusionsRepo,
        ApplicationIdResolver applicationIdResolver
    )
    {
        _decisionsRepo = decisionsRepo;
        _exclusionsRepo = exclusionsRepo;
        _applicationIdResolver = applicationIdResolver;
    }

    [HttpGet]
    [Route("loan/{loanIdentifier}/application/{applicationId}/reason/{reason}")]
    [ProducesResponseType(typeof(ExclusionInformation), StatusCodes.Status200OK)]
    public async Task<ActionResult<ExclusionInformation>> GetExclusion(string loanIdentifier, string applicationId, string reason)
    {
        var resolvedApplicationId = _applicationIdResolver.Resolve(applicationId);
        var result = await _exclusionsRepo.GetExclusion(loanIdentifier, resolvedApplicationId, reason);
        return Ok(result);
    }

    [HttpGet]
    [Route("loan/{loanIdentifier}/application/{applicationId}/")]
    [ProducesResponseType(typeof(ExclusionInformation), StatusCodes.Status200OK)]
    public async Task<ActionResult<ExclusionInformation>> GetExclusions(string loanIdentifier, string applicationId)
    {
        var resolvedApplicationId = _applicationIdResolver.Resolve(applicationId);
        var result = await _exclusionsRepo.GetExclusionsForApplication(loanIdentifier, resolvedApplicationId);
        return Ok(result);
    }

    [HttpGet]
    [Route("loan/{loanIdentifier}")]
    [ProducesResponseType(typeof(ExclusionInformation), StatusCodes.Status200OK)]
    public async Task<ActionResult<ExclusionInformation>> GetExclusions(string loanIdentifier)
    {
        var result = await _exclusionsRepo.GetExclusions(loanIdentifier);
        return Ok(result);
    }

    [HttpPost]
    [Route("loan/{loanIdentifier}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult> PostExclusion(
        string loanIdentifier,
        [FromBody] CreateManualExclusionRequest request,
        [Required][FromHeader(Name = "x-common-id")] string commonId
    )
    {
        var resolvedApplicationId = _applicationIdResolver.Resolve(request.AppId);
        var exclusion = new Exclusion
        {
            AppId = resolvedApplicationId,
            Comment = request.Comment,
            ExcludedAt = DateTime.Now,
            // Reason is required - if it is null, validation would
            // prevent this method from being called.
            Reason = request.Reason.Value,
            User = commonId,
            Features = Enumerable.Empty<string>()
        };

        // The request's Reason is not null, so we are always changing the access decision to false, regardless of
        // what is already stored.
        var decisionTask = _decisionsRepo.StoreDecision(loanIdentifier, resolvedApplicationId, false);
        var exclusionTask = _exclusionsRepo.PutExclusion(loanIdentifier, exclusion);
        await Task.WhenAll(decisionTask, exclusionTask);
        return Ok();
    }

    [HttpDelete]
    [Route("loan/{loanIdentifier}/application/{applicationId}/reason/{reason?}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ApiExplorerSettings(IgnoreApi = true)]
    public async Task<ActionResult> DeleteExclusion(
        string loanIdentifier,
        string applicationId,
        ExclusionReason? reason,
        [Required][FromHeader(Name = "x-common-id")] string commonId
    )
    {
        var resolvedApplicationId = _applicationIdResolver.Resolve(applicationId);
        var existing = await _exclusionsRepo.GetExclusionsForApplication(loanIdentifier, resolvedApplicationId);
        var hadExclusions = existing.HasExclusions(resolvedApplicationId);

        await _exclusionsRepo.DeleteExclusion(loanIdentifier, resolvedApplicationId, reason, commonId);

        if (hadExclusions)
        {
            // At least one exclusion was previously persisted. So make sure the decision is persisted accordingly.
            // If there are no remaining exclusions, remove the decision.
            var updated = existing.Except(resolvedApplicationId, reason);
            var stillHasExclusions = updated.HasExclusions(resolvedApplicationId);
            var isExcluded = updated.IsExcluded(resolvedApplicationId);
            if (stillHasExclusions)
            {
                await _decisionsRepo.StoreDecision(loanIdentifier, resolvedApplicationId, !isExcluded);
            }
            else
            {
                await _decisionsRepo.RemoveDecision(loanIdentifier, resolvedApplicationId);
            }
        }

        return Ok();
    }

    [HttpGet]
    [Route("activity/loan/{loanIdentifier}")]
    [ProducesResponseType(typeof(LoanExclusionAuditHistory), StatusCodes.Status200OK)]
    public async Task<ActionResult<LoanExclusionAuditHistory>> GetLoanActivity(string loanIdentifier)
    {
        var result = await _exclusionsRepo.GetLoanActivity(loanIdentifier);
        return Ok(new LoanExclusionAuditHistory(loanIdentifier, result));
    }

    [HttpGet]
    [Route("activity/application/{applicationId}")]
    [ProducesResponseType(typeof(AppExclusionAuditHistory), StatusCodes.Status200OK)]
    public async Task<ActionResult<AppExclusionAuditHistory>> GetActivity(string applicationId)
    {
        var resolvedApplicationId = _applicationIdResolver.Resolve(applicationId);
        var result = await _exclusionsRepo.GetApplicationActivity(resolvedApplicationId);
        return Ok(new LoanExclusionAuditHistory(applicationId, result));
    }
}
