using DecisionServices.Core.StateProvider;
using LoanAccessAuthorizer.Domain.Exceptions;
using Microsoft.AspNetCore.Mvc;

namespace LoanAccessAuthorizer.Controllers;

[Route("/cache")]
[ApiController]
[ApiVersion("1.0")]
[ApiVersion("2.0")]
[ApiVersion("3.0")]
[ApiVersion("4.0")]
public class CacheController : ControllerBase
{
    private readonly ICacheableStateInvalidator _cacheableStateInvalidator;
    private readonly ILogger<CacheController> _logger;

    public CacheController(ICacheableStateInvalidator cacheableStateInvalidator, ILogger<CacheController> logger)
    {
        _cacheableStateInvalidator = cacheableStateInvalidator;
        _logger = logger;
    }

    [HttpGet]
    [Route("keywords")]
    public ActionResult<IEnumerable<string>> GetKeywordList()
    {
        return Ok(_cacheableStateInvalidator.Providers);
    }

    [HttpDelete]
    [Route("loan/{loanIdentifier}/keywords/{cacheKeyword}")]
    [Obsolete($"Replaced by the {nameof(DeleteCacheValues)} method.")]
    public async Task<ActionResult> DeleteCachedValue(string loanIdentifier, string cacheKeyword)
    {
        try
        {
            await _cacheableStateInvalidator.ClearState(loanIdentifier, cacheKeyword);
            return Ok();
        }
        catch (ArgumentOutOfRangeException ex)
        {
            throw new BadRequestException($"{cacheKeyword} not found: {loanIdentifier}", ex);
        }
        catch (Exception ex)
        {
            throw new UnknownException($"Unknown exception happened trying to clear {cacheKeyword}: {loanIdentifier}", ex);
        }
    }

    [HttpDelete]
    [ProducesResponseType(typeof(IDictionary<string, bool>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(IDictionary<string, bool>), StatusCodes.Status207MultiStatus)]
    [Route("loan/{loanIdentifier}/keywords")]
    public async Task<ActionResult> DeleteCacheValues(
        string loanIdentifier,
        [FromQuery(Name = "Key")] IEnumerable<string> cacheKeywords
    )
    {
        var tasks = GetCacheInvalidationTasks(loanIdentifier, cacheKeywords);
        var result = (await Task.WhenAll(tasks)).ToDictionary(r => r.keyword, r => r.Item2);

        return result.Values.Any(v => !v) ? StatusCode(StatusCodes.Status207MultiStatus, result) : Ok();
    }

    [HttpDelete]
    [ProducesResponseType(typeof(IDictionary<string, bool>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(IDictionary<string, bool>), StatusCodes.Status207MultiStatus)]
    [Route("user/{commonId}/keywords")]
    public async Task<ActionResult> DeleteUserAccessCacheValues(
        string commonId,
        [FromQuery(Name = "Key")] IEnumerable<string> cacheKeywords
    )
    {
        var tasks = GetCacheInvalidationTasks(commonId, cacheKeywords);
        var result = (await Task.WhenAll(tasks)).ToDictionary(r => r.keyword, r => r.Item2);

        return result.Values.Any(v => !v) ? StatusCode(StatusCodes.Status207MultiStatus, result) : Ok();
    }

    private IEnumerable<Task<(string keyword, bool)>> GetCacheInvalidationTasks(string keyValue, IEnumerable<string> cacheKeywords)
    {
        var tasks = cacheKeywords?.Select(async keyword =>
        {
            try {
                await _cacheableStateInvalidator.ClearState(keyValue, keyword);
                return (keyword, true);
            }
            catch (Exception ex){
                _logger.LogWarning(ex, "Failed to clear cache for {keyword} {keyValue}", keyword, keyValue);
                return (keyword, false);
            }
        }) ?? Enumerable.Empty<Task<(string keyword, bool)>>();
        return tasks;
    }
}
