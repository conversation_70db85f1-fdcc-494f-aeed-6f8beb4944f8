{"cacheConfig": {"hostNames": ["redis.laa.decision-services"], "port": "6379", "abortOnConnect": false, "Enabled": true, "OperationTimeoutInMs": 300}, "dynamoDB": {"tableName": "laa-prod-202723", "exclusionsTableName": "laa-prod-202723-exclusions", "enabled": true}, "Mods": {"BaseUrl": "https://api.mods.einstein.foc.zone/", "Audience": "urn:ql-api:mortgage_origination_data_service-206483:Prod"}, "RLUnderwritingService": {"BaseUrl": "https://api.rocket-logic-uw.foc.zone", "Audience": "urn:ql-api:rocket-logic-uw-api-206872:Prod"}, "RLBankingService": {"BaseUrl": "https://rlb-orch.rocket-logic.foc.zone", "Audience": "urn:ql-api:rlb_orchestrator-206156_prod-206156:Prod"}, "TeamMemberDataService": {"BaseUrl": "https://graphql.tmds.foc.zone", "TokenEndpoint": "https://sso.authrock.com/oauth/token", "Audience": "urn:ql-api:tmds-graphql-api-206427:Prod"}, "OpenAmp": {"BaseUrl": "https://oagateway.openamp.foc.zone/rocketlogic/", "Audience": "urn:ql-api:oagateway-208933:Prod"}, "IncomeQualifierService": {"BaseUrl": "https://api.income-data-orchestrator.income.foc.zone", "Audience": "urn:ql-api:income_data_orchestrator-202140:Prod"}, "CreditQualifierService": {"BaseUrl": "https://api.credit-data-orchestrator.credit.foc.zone", "Audience": "urn:ql-api:credit-data-orchestrator-206069:Prod"}, "PropertyQualifierService": {"BaseUrl": "https://api.property-qualifier-orchestrator.property.foc.zone", "Audience": "urn:ql-api:pq-orchestrator-206067:Prod"}, "PropertyInsuranceQualifierService": {"BaseUrl": "https://insurance-orchestrator.processingservices.foc.zone", "Audience": "urn:ql-api:insurance.orchestrator-208699:Prod"}, "RlApi": {"BaseUrl": "https://api.rocket-logic.foc.zone", "Audience": "urn:ql-api:rocketlogicapi-210205:Prod"}, "ConfigurationManager": {"BaseUrl": "https://api.configuration-manager.rocket-logic.foc.zone", "Audience": "urn:ql-api:configurationmanagerapi-211304:Prod", "RefreshCron": "*/1 * * * *"}, "AmpJobRunner": {"BaseUrl": "https://api.rl-amp-job-rnr.rocket-logic.foc.zone", "Audience": "urn:ql-api:rl-amp-job-runner-211434:Prod"}, "RocketLogicDocumentStorage": {"BaseUrl": "https://api.document-storage.origination-documents.foc.zone", "Audience": "urn:ql-api:document_storage-206592:Prod"}, "Ders": {"BaseUrl": "https://api.doc-entity-rel.mortgageops.foc.zone", "Audience": "urn:ql-api:document_entity_relationship_service-210034:Prod"}, "RLTasks": {"BaseUrl": "https://api.tasks.rocket-logic.foc.zone/", "Audience": "urn:ql-api:rocketlogictasksapi-211057:Prod"}, "ParOrchestrator": {"BaseUrl": "https://par-orchestrator.rocket-logic.foc.zone/", "Audience": "urn:ql-api:par-orchestrator-214035:Prod"}, "LicensingService": {"BaseUrl": "https://licensing-api.licensetheworld.foc.zone/", "Audience": "urn:ql-api:licensingservice-202059:Prod"}, "AuthHub": {"TokenEndpoint": "https://sso.authrock.com/oauth/token"}, "TimeTrackingCheckService": {"BaseUrl": "https://check-service.time.foc.zone/", "TokenEndpoint": "https://sso.authrock.com/oauth/token", "Audience": "urn:ql-api:time-tracking-check-service-214377:Prod"}}