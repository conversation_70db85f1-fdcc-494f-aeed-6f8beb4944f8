<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ProjectGuid>718fd487-0687-4c2d-8035-c6f1a41b3ab2</ProjectGuid>
    <UserSecretsId>75d96f9c-cf53-4c98-a7e6-7772cd94c2cb</UserSecretsId>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AWSSDK.core" Version="3.7.300.15" />
    <PackageReference Include="AWSSDK.Extensions.NETCore.Setup" Version="3.7.300" />
    <PackageReference Include="ConfigurationManager.Sdk" Version="3.3.0" />
    <PackageReference Include="DecisionServices.Core" Version="18.0.0" />
    <PackageReference Include="DecisionServices.Core.Cache" Version="18.0.0" />
    <PackageReference Include="DecisionServices.Core.Diagnostics.HealthChecks" Version="18.0.0" />
    <PackageReference Include="DecisionServices.Core.Metrics.AspNetCore" Version="18.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
    <PackageReference Include="Swashbuckle.AspNetCore.Filters" Version="8.0.2" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" Version="5.1.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer" Version="5.1.0" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />


  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\LoanAccessAuthorizer.AmpJobRunner\LoanAccessAuthorizer.AmpJobRunner.csproj" />
    <ProjectReference Include="..\LoanAccessAuthorizer.Authorization.Rules\LoanAccessAuthorizer.Authorization.Rules.csproj" />
    <ProjectReference Include="..\LoanAccessAuthorizer.Domain\LoanAccessAuthorizer.Domain.csproj" />
    <ProjectReference Include="..\LoanAccessAuthorizer.Dynamo\LoanAccessAuthorizer.Dynamo.csproj" />
    <ProjectReference Include="..\LoanAccessAuthorizer.Exclusions\LoanAccessAuthorizer.Exclusions.csproj" />
    <ProjectReference Include="..\LoanAccessAuthorizer.LicensingService\LoanAccessAuthorizer.LicensingService.csproj" />
    <ProjectReference Include="..\LoanAccessAuthorizer.OpenAmp\LoanAccessAuthorizer.OpenAmp.csproj" />
    <ProjectReference Include="..\LoanAccessAuthorizer.ParOrchestrator\LoanAccessAuthorizer.ParOrchestrator.csproj" />
    <ProjectReference Include="..\LoanAccessAuthorizer.RocketDocs\LoanAccessAuthorizer.RocketDocs.csproj" />
    <ProjectReference Include="..\LoanAccessAuthorizer.Roles.LDAP\LoanAccessAuthorizer.Roles.LDAP.csproj" />
    <ProjectReference Include="..\LoanAccessAuthorizer.Roles\LoanAccessAuthorizer.Roles.csproj" />
    <ProjectReference Include="..\LoanAccessAuthorizer.TeamMemberDataService\LoanAccessAuthorizer.TeamMemberDataService.csproj" />
    <ProjectReference Include="..\LoanAccessAuthorizer.TimeTrackingCheckService\LoanAccessAuthorizer.TimeTrackingCheckService.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="appsettings.Development.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Update="appsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>
