using System.Diagnostics.CodeAnalysis;
using System.Reflection;
using Microsoft.AspNetCore.Mvc.ApiExplorer;
using LoanAccessAuthorizer.Configuration;
using MicrosoftConfigurationManager = Microsoft.Extensions.Configuration.ConfigurationManager;

namespace LoanAccessAuthorizer;

[ExcludeFromCodeCoverage]
public class Program
{
    public static void Main(string[] args)
    {
        var builder = WebApplication.CreateBuilder(new WebApplicationOptions
        {
            Args = args,
            ContentRootPath = Path.GetDirectoryName(Assembly.GetEntryAssembly().Location)
        });

        Configure(builder.Configuration);

        var startup = new Startup();
        startup.ConfigureServices(builder.Services, builder.Configuration, builder.Environment);

        var app = builder.Build();

        startup.Configure(app, app.Environment,
            app.Services.GetRequiredService<IApiVersionDescriptionProvider>());

        app.Run();
    }

    private static void Configure(MicrosoftConfigurationManager configuration)
    {
        configuration.AddJsonFile("appsettings.local.json", optional: true);
        configuration.AddConfigurationManagerOptions();
    }
}
