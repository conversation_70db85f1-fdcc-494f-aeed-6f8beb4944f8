import { Injectable, Scope, Inject } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';

@Injectable({ scope: Scope.REQUEST })
export class LoanContextService {
  constructor(@Inject(REQUEST) private readonly request: Request) {}

  /**
   * Extract the current loan ID from the request context
   * This could come from:
   * - URL parameters (e.g., /loans/:loanId/...)
   * - Query parameters (e.g., ?loanId=...)
   * - Request headers
   * - JWT token claims
   */
  getCurrentLoanId(): string | null {
    // Method 1: From URL parameters
    const loanIdFromParams = this.request.params?.loanId;
    if (loanIdFromParams) {
      return loanIdFromParams;
    }

    // Method 2: From query parameters
    const loanIdFromQuery = this.request.query?.loanId as string;
    if (loanIdFromQuery) {
      return loanIdFromQuery;
    }

    // Method 3: From custom header
    const loanIdFromHeader = this.request.headers['x-loan-id'] as string;
    if (loanIdFromHeader) {
      return loanIdFromHeader;
    }

    // Method 4: From JWT token (if loan ID is embedded in the token)
    // This would require decoding the JWT and extracting the loan ID
    // const user = this.request.user; // Assuming JWT middleware sets this
    // if (user?.loanId) {
    //   return user.loanId;
    // }

    // Method 5: From request body (for POST requests)
    const loanIdFromBody = this.request.body?.loanId;
    if (loanIdFromBody) {
      return loanIdFromBody;
    }

    return null;
  }

  /**
   * Alternative method if loan ID is consistently passed in a specific way
   * Adjust this based on your application's routing and context patterns
   */
  extractLoanIdFromPath(): string | null {
    const path = this.request.path;
    
    // Example: Extract from paths like /api/loans/123456/messages
    const loanIdMatch = path.match(/\/loans\/([^\/]+)/);
    if (loanIdMatch && loanIdMatch[1]) {
      return loanIdMatch[1];
    }

    return null;
  }
}
