import http from "k6/http";
import { SharedArray } from "k6/data";

const loadTestEnvironment = __ENV.ENVIRONMENT;

let validEnvironment = false;
switch (loadTestEnvironment) {
  case "test":
    validEnvironment = true;
    break;
  case "beta":
    validEnvironment = true;
    break;
  case "local":
    validEnvironment = true;
    break;
  case null:
    loadTestEnvironment = "local";
    validEnvironment = true;
    break;
  default:
    validEnvironment = false;
}

if (validEnvironment === false) {
  throw new Error(
    "Not a valid load test environment, please set ENVIRONMENT to test, beta, local"
  );
}

const loadTestConfiguration = JSON.parse(
  open(`./load_test_configuration.${loadTestEnvironment}.json`)
);
const baseUrl = loadTestConfiguration.baseUrl;
const audience = loadTestConfiguration.audience;
const clientId = loadTestConfiguration.clientId;
const clientSecret = loadTestConfiguration.clientSecret;

const authUrl = `https://sso.${loadTestEnvironment}.authrock.com/oauth/token`;

// Options we can utilize to adjust how our k6 test will run
export const options = {
  stages: [
    { duration: "60s", target: 10 },
    { duration: "4m", target: 20 },
    { duration: "5m", target: 60 },
    { duration: "10m", target: 100 },
  ],
};

const getRandomApplicationId = () => {
  const randomIndex = Math.floor(
    Math.random() * loadTestConfiguration.accessDeciders.length
  );
  return loadTestConfiguration.accessDeciders[randomIndex];
};

// Load the test data from file
const data = new SharedArray("test data", () => {
  return loadTestConfiguration.loanIdentifiers;
});

var authToken = undefined;

// The actual testing function with the various requests we will be sending
export default async function () {
  const randomIndex = Math.floor(Math.random() * data.length);
  const loanIdentifier = data[randomIndex];

  if (authToken == undefined && loadTestEnvironment != "local") {
    const authTokenResponse = http.post(
      authUrl,
      {
        grant_type: "client_credentials",
        audience: audience,
        client_id: clientId,
        client_secret: clientSecret,
      },
      {
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      }
    );

    authToken = authTokenResponse.json().access_token;
    console.log(authToken);
  }

  const deciders = loadTestConfiguration.accessDeciders;
  const subsetSize = Math.floor(Math.random() * deciders.length) + 1;
  const shuffled = deciders.slice().sort(() => 0.5 - Math.random());
  const randomSubset = shuffled.slice(0, subsetSize);

  const postUrl = `${baseUrl}/loan/${loanIdentifier}/application/access`;
  http.post(postUrl, JSON.stringify(randomSubset), {
    headers: {
      Authorization: `${authToken}`,
      "Content-Type": "application/json",
      "x-api-version": "4.0",
    },
  });

  const applicationId = getRandomApplicationId();
  const url = `${baseUrl}/loan/${loanIdentifier}/application/${applicationId}/access`;
  http.get(url, {
    headers: {
      Authorization: `${authToken}`,
      "x-api-version": "4.0",
    },
  });
}
