# K6 Load Test

The following script provides a way of load testing LAA through the usage of k6.

More about k6 can be read at their [documentation](https://grafana.com/docs/k6/latest/)

In order to run the load test you'll need a non production configuration file that provides the following information. This file should be called `load_test_configuration.{environment}.json` and is hidden from version control as it will store sensitive information.

```json
{
  "baseUrl": "REPLACE_ME",
  "clientId": "REPLACE_ME",
  "clientSecret": "REPLACE_ME",
  "audience": "REPLACE_ME",
  "accessDeciders": ["REPLACE_ME"],
  "loanIdentifiers": ["REPLACE_ME"]
}
```

Once this file is setup it's as easy as executing the following k6 command `k6 run ./.k6/load_test.js -e ENVIRONMENT={environment_name}` from the root directory to start the test. In addition set the `K6_WEB_DASHBOARD` environment variable to true on your path in order to have a dynamic web interface for usage during the test execution.
