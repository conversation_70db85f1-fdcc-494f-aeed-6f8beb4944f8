﻿using System.Text.Json;
using System.Text.Json.Serialization;
using LoanAccessAuthorizer.Common.HttpRequestHandler;
using LoanAccessAuthorizer.CreditQualifier.Models;
using LoanAccessAuthorizer.CreditQualifier.Serialization;
using DecisionServices.Core.Metrics.Outbound;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.CreditQualifier;

public class CreditQualifierServiceClient : HttpRequestHandler
{
    public CreditQualifierServiceClient(HttpClient client, ILogger<CreditQualifierServiceClient> logger) : base(client, logger)
    {
    }

    [OutboundCall("api/loan/{loanIdentifier}/liabilities")]
    public Task<LiabilityCollection> GetLiabilities(string loanIdentifier)
    {
        var path = $"api/loan/{loanIdentifier}/liabilities";
        var request = new HttpRequestMessage(HttpMethod.Get, path);
        return HandleHttpRequest<LiabilityCollection>(request, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true,
            Converters = { new JsonStringEnumConverter(), new DiscriminatedLiabilityTypeConverter() }
        });
    }
}
