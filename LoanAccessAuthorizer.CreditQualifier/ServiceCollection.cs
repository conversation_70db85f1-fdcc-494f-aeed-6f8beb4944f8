using DecisionServices.Core;
using DecisionServices.Core.Authentication;
using DecisionServices.Core.Extensions.AspNetCore;
using DecisionServices.Core.Extensions.HttpClient;
using DecisionServices.Core.HttpPolicy.Metrics;
using DecisionServices.Core.HttpPolly;
using LoanAccessAuthorizer.Common;
using Microsoft.Extensions.DependencyInjection;

namespace LoanAccessAuthorizer.CreditQualifier;

public static class ServiceCollection
{
    private const string ServiceName = "credit-qualifier";
    private const string RetryPipelineKey = $"{ServiceName}-retries";
    private const string TimeoutPipelineKey = $"{ServiceName}-timeout";

    public static void AddCreditQualifierService(this IServiceCollection serviceCollection,
        AuthenticatedServiceConfig authenticatedServiceConfig, string httpPolicyConfigName)
    {
        serviceCollection.AddScoped<PolicyInfoLoggingHttpLifecycleHook<CreditQualifierServiceClient>>();
        serviceCollection.AddRetryResiliencePipeline(RetryPipelineKey, httpPolicyConfigName)
            .AddCircuitBreakerTimeoutResiliencePipeline(TimeoutPipelineKey, httpPolicyConfigName);

        serviceCollection.AddAuthenticatedService<CreditQualifierServiceClient>(authenticatedServiceConfig)
            .AddRequestContextMessageHandler(
            [
                (CoreContextKeys.RequestId, Headers.RequestId)
            ])
            .AddHttpMessageHandler<PolicyInfoLoggingHttpLifecycleHook<CreditQualifierServiceClient>>()
            .AddHttpPoliciesAndMetrics<CreditQualifierServiceClient>(ServiceName, RetryPipelineKey, TimeoutPipelineKey);
    }
}
