  <Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <RootNamespace>LoanAccessAuthorizer.CreditQualifier</RootNamespace>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\LoanAccessAuthorizer.Common\LoanAccessAuthorizer.Common.csproj" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="Credit.Data.Models" Version="8.6.0" />
      <PackageReference Include="DecisionServices.Core.Extensions.Authentication" Version="18.0.0" />
      <PackageReference Include="DecisionServices.Core.Extensions.HttpClient.Metrics" Version="18.0.0" />
      <PackageReference Include="DecisionServices.Core.HttpPolicy" Version="18.0.0" />
      <PackageReference Include="DecisionServices.Core.HttpPolicy.Metrics" Version="18.0.0" />
      <PackageReference Include="System.Drawing.Common" Version="8.0.7" />
      <PackageReference Include="System.Net.Http" Version="4.3.4" />
      <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
      <PackageReference Include="System.Net.Http.WinHttpHandler" Version="8.0.1" />
      <PackageReference Include="System.Security.Cryptography.Xml" Version="8.0.1" />
      <PackageReference Include="System.ServiceModel.Http" Version="8.0.0" />
      <PackageReference Include="System.Text.Json" Version="8.0.5" />
    </ItemGroup>

</Project>
