using Credit.Data.Models.Internal.Liabilities;
using PolymorphicDeserializer.Core;

namespace LoanAccessAuthorizer.CreditQualifier.Serialization;

public class LiabilityDiscriminatorOptions : DiscriminatorOptions
{
    public override IEnumerable<DiscriminatedType> GetDiscriminatedTypes() => DiscriminatedTypeFinder.Find<Liability>(DiscriminatorFieldName);
    public override Type BaseType => typeof(Liability);
    public override string DiscriminatorFieldName => "LiabilityType";
    public override bool SerializeDiscriminator => true;
}
