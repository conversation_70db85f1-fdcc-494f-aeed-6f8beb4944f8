import { Injectable } from '@nestjs/common';
import { combineLatest, Observable, of } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import { HttpErrorHandler } from 'src/http/http-error-handler';
import { ConsoleLoggerService } from 'src/logger/implementations/console-logger.service';
import { BannerMessage, MessageSeverity, MessageType } from 'src/models/message-center';
import { OauthClientService } from 'src/oauth/oauth-client/oauth-client.service';
import { ArchivedLoanService } from 'src/archived-loan/archived-loan.service';
import { LoanContextService } from 'src/loan-context/loan-context.service'; // Assuming this exists

@Injectable()
export class MessageCenterService {
  constructor(
    private oauthClient: OauthClientService,
    private logger: ConsoleLoggerService,
    private httpErrorHandler: HttpErrorHandler,
    private archivedLoanService: ArchivedLoanService,
    private loanContextService: LoanContextService, // Service to get current loan ID from context
  ) {
    this.logger.setContext(MessageCenterService.name);
  }

  public getBannerMessages(): Observable<BannerMessage[]> {
    // Get server banner messages and archived loan status in parallel
    return combineLatest([
      this.getServerBannerMessages(),
      this.getArchivedLoanBannerMessage(),
    ]).pipe(
      map(([serverMessages, archivedMessage]) => {
        const messages = [...serverMessages];
        
        // Add archived loan message if present
        if (archivedMessage) {
          messages.unshift(archivedMessage); // Add at the beginning for priority
        }
        
        return messages;
      }),
      catchError((err) => {
        this.logger.error('Failed to get combined banner messages', err);
        // Fallback to server messages only
        return this.getServerBannerMessages();
      }),
    );
  }

  private getServerBannerMessages(): Observable<BannerMessage[]> {
    return this.oauthClient
      .get<BannerMessage[]>(`/messages`, { params: { messageType: MessageType.BannerMessage } })
      .pipe(
        map((response) => response.data),
        catchError((err) =>
          this.httpErrorHandler.logAndContinue<BannerMessage[]>(
            err,
            'Failed to get server banner messages',
            [404],
            [],
          ),
        ),
      );
  }

  private getArchivedLoanBannerMessage(): Observable<BannerMessage | null> {
    // Get current loan ID from context (request headers, JWT, etc.)
    const loanId = this.loanContextService.getCurrentLoanId();
    
    if (!loanId) {
      return of(null);
    }

    return this.archivedLoanService.isLoanArchived(loanId).pipe(
      map((isArchived) => {
        if (!isArchived) {
          return null;
        }

        // Create archived loan banner message
        const archivedMessage: BannerMessage = {
          messageId: 'archived-loan',
          messageType: MessageType.BannerMessage,
          subject: 'Archived Loan',
          content: 'This loan is archived and cannot be edited. Any changes will not be saved.',
          messageSeverity: MessageSeverity.Warn,
          isActive: true,
          timestamp: new Date().toISOString(),
          source: 'rl-xp-bff-archived-loan-service',
          createdDate: new Date().toISOString(),
          updatedDate: new Date().toISOString(),
        };

        return archivedMessage;
      }),
      catchError((err) => {
        this.logger.error(`Failed to get archived loan banner message for loan ${loanId}`, err);
        return of(null);
      }),
    );
  }
}
