platform: 'linux'
image: 'iac'

env:
  dev-aws:
    ECS_SERVICE: laa-dev-202723 
  test-aws:
    ECS_SERVICE: laa-test-202723
  beta-aws:
    ECS_SERVICE: laa-beta-202723
  prod-aws:
    ECS_SERVICE: laa-prod-202723
  uat-aws:
    ECS_SERVICE: laa-train-202723

deploy:
  - |
       REGION=${HAL_CONTEXT}
       ECS_CLUSTER_NAME=decision-services-ecs-${REGION}
       IMAGE=${AWS_ACCOUNT}.dkr.ecr.${REGION}.amazonaws.com/laa:$HAL_COMMIT
       echo "Deploying ${IMAGE} to ${ECS_SERVICE} in ${ECS_CLUSTER_NAME}"
       ecs-deploy -v \
         --service-name $ECS_SERVICE \
         --cluster $ECS_CLUSTER_NAME \
         --region $REGION \
         --image $IMAGE \
         --enable-rollback \
         --timeout 600
