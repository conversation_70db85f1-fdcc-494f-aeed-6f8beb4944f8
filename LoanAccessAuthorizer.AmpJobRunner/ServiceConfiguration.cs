using DecisionServices.Core.HttpPolicy.Metrics;
using DecisionServices.Core.HttpPolly;
using DecisionServices.Core;
using LoanAccessAuthorizer.Common;
using Microsoft.Extensions.DependencyInjection;
using DecisionServices.Core.Authentication;
using DecisionServices.Core.Extensions.AspNetCore;
using DecisionServices.Core.Extensions.HttpClient;

namespace LoanAccessAuthorizer.AmpJobRunner;

public static class ServiceConfiguration
{
    private const string ServiceName = "amp-job-runner";
    private const string RetryPipelineKey = $"{ServiceName}-retries";
    private const string TimeoutPipelineKey = $"{ServiceName}-timeout";

    public static void AddAmpJobRunner(this IServiceCollection services,
        AuthenticatedServiceConfig config, string httpPolicyConfigName)
    {
        services.AddScoped<PolicyInfoLoggingHttpLifecycleHook<AmpJobRunnerServiceClient>>();
        services.AddRetryResiliencePipeline(RetryPipelineKey, httpPolicyConfigName)
            .AddCircuitBreakerTimeoutResiliencePipeline(TimeoutPipelineKey, httpPolicyConfigName);

        services.AddAuthenticatedService<AmpJobRunnerServiceClient>(config, client =>
        {
            client.DefaultRequestHeaders.Add(Headers.SourceType, "loan-access-authorizer");
        })
        .AddRequestContextMessageHandler(
        [
            (CoreContextKeys.RequestId, Headers.RequestId)
        ])
        .AddHttpMessageHandler<PolicyInfoLoggingHttpLifecycleHook<AmpJobRunnerServiceClient>>()
        .AddHttpPoliciesAndMetrics<AmpJobRunnerServiceClient>(ServiceName, RetryPipelineKey, TimeoutPipelineKey);
    }
}
