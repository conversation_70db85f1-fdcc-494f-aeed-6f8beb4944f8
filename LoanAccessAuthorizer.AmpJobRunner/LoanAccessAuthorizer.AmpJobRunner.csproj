<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AmpJobRunner.Models.Serialization.SystemTextJson" Version="3.4.0" />
    <PackageReference Include="DecisionServices.Core.Extensions.Authentication" Version="18.0.0" />
    <PackageReference Include="DecisionServices.Core.HttpPolicy.Metrics" Version="18.0.0" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\LoanAccessAuthorizer.Common\LoanAccessAuthorizer.Common.csproj" />
  </ItemGroup>

</Project>
