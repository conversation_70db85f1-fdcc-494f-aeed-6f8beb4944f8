#nullable enable

using System.Text.Json;
using AmpJobRunner.Models.Response;
using AmpJobRunner.Models.Serialization.SystemTextJson;
using LoanAccessAuthorizer.Common.HttpRequestHandler;
using DecisionServices.Core.Metrics.Outbound;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.AmpJobRunner;

public class AmpJobRunnerServiceClient : HttpRequestHandler
{
    private static readonly JsonSerializerOptions _serializerOptions = new(JsonSerializerDefaults.Web);

    static AmpJobRunnerServiceClient()
    {
        _serializerOptions.AddAmpJobRunnerSerializerOptions();
    }

    public AmpJobRunnerServiceClient(HttpClient client, ILogger<AmpJobRunnerServiceClient> logger)
        : base(client, logger)
    {
    }

    [OutboundCall("jobs/loan/{loanIdentifer}")]
    public virtual async Task<IEnumerable<JobDetails>> GetJobs(string loanIdentifer)
    {
        var path = $"jobs/loan/{loanIdentifer}";
        var request = new HttpRequestMessage(HttpMethod.Get, path);
        return await HandleHttpRequest<IEnumerable<JobDetails>>(request, _serializerOptions);
    }
}
