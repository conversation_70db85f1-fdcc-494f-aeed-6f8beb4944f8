_type: export
__export_format: 4
__export_date: 2023-09-07T14:13:44.110Z
__export_source: insomnia.desktop.app:v2023.5.7
resources:
  - _id: req_badd4551cfd74cb69d3dd250d1ea10f0
    parentId: fld_24cc9586888240db904b18eab11de61c
    modified: 1694051606372
    created: 1694051497688
    url: "{{ _.oauth_url }}"
    name: /oauth_url
    description: ""
    method: POST
    body:
      mimeType: application/json
      text: |-
        {
        	"grant_type":"client_credentials",
        	"audience":"{{ _.oauth_audience }}",
        	"client_id":"{{ _.oauth_clientid }}",
        	"client_secret":"{{ _.oauth_clientsecret }}"
        }
    parameters: []
    headers:
      - name: Content-Type
        value: application/json
      - name: User-Agent
        value: Insomnia/2023.5.7
    authentication: {}
    metaSortKey: -1694051497688
    isPrivate: false
    settingStoreCookies: true
    settingSendCookies: true
    settingDisableRenderRequestBody: false
    settingEncodeUrl: true
    settingRebuildPath: true
    settingFollowRedirects: global
    _type: request
  - _id: fld_24cc9586888240db904b18eab11de61c
    parentId: wrk_413cd17d44b8449e89de05106561f3bd
    modified: 1694091581021
    created: 1694049992022
    name: Authentication
    description: ""
    environment: {}
    environmentPropertyOrder: null
    metaSortKey: -1694091577671
    _type: request_group
  - _id: wrk_413cd17d44b8449e89de05106561f3bd
    parentId: null
    modified: 1694091567238
    created: 1694091558494
    name: LAA
    description: ""
    scope: collection
    _type: workspace
  - _id: req_6280833e06b044dea2fa27a285da154b
    parentId: fld_60b0af028c9a41a980116c8a87e91b73
    modified: 1694093872702
    created: 1694092289308
    url: "{{ _.base_url }}/cache/keywords"
    name: /cache/keywords
    description: ""
    method: GET
    body: {}
    parameters: []
    headers:
      - name: Authorization
        value: Bearer {{ _.oauth_token }}
      - name: x-forwarded-for
        value: "{{ _.x_forwarded_for }}"
      - name: x-request-id
        value: "{{ _.x_request_id }}"
      - name: x-source
        value: "{{ _.x_sourcetype }}"
    authentication:
      type: bearer
      token: "{{ _.oauth_token }}"
    metaSortKey: -1694092295339
    isPrivate: false
    settingStoreCookies: true
    settingSendCookies: true
    settingDisableRenderRequestBody: false
    settingEncodeUrl: true
    settingRebuildPath: true
    settingFollowRedirects: global
    _type: request
  - _id: fld_60b0af028c9a41a980116c8a87e91b73
    parentId: wrk_413cd17d44b8449e89de05106561f3bd
    modified: 1694095937975
    created: 1694091577571
    name: Endpoints
    description: ""
    environment:
      x_amp_username: ""
      x_common_id: ""
      loanIdentifier: ""
      applicationId: ""
      reason: ""
      cacheKeyword: ""
    environmentPropertyOrder:
      "&":
        - x_amp_username
        - x_common_id
        - loanIdentifier
        - applicationId
        - reason
        - cacheKeyword
    metaSortKey: -1694091577571
    _type: request_group
  - _id: req_a266e3be6c1649209ee61deea642ca8b
    parentId: fld_60b0af028c9a41a980116c8a87e91b73
    modified: 1694093922072
    created: 1694092289331
    url: "{{ _.base_url }}/exclusion/activity/application/{{ _.applicationId }}"
    name: /exclusion/activity/application/{applicationId}
    description: ""
    method: GET
    body: {}
    parameters: []
    headers:
      - name: Authorization
        value: Bearer {{ _.oauth_token }}
      - name: x-forwarded-for
        value: "{{ _.x_forwarded_for }}"
      - name: x-request-id
        value: "{{ _.x_request_id }}"
      - name: x-source
        value: "{{ _.x_sourcetype }}"
    authentication: {}
    metaSortKey: -1694092295239
    isPrivate: false
    settingStoreCookies: true
    settingSendCookies: true
    settingDisableRenderRequestBody: false
    settingEncodeUrl: true
    settingRebuildPath: true
    settingFollowRedirects: global
    _type: request
  - _id: req_9f15a8da52a042eda2043c04747819a1
    parentId: fld_60b0af028c9a41a980116c8a87e91b73
    modified: 1694093923433
    created: 1694092289328
    url: "{{ _.base_url }}/exclusion/activity/loan/{{ _.loanIdentifier }}"
    name: /exclusion/activity/loan/{loanIdentifier}
    description: ""
    method: GET
    body: {}
    parameters: []
    headers:
      - name: Authorization
        value: Bearer {{ _.oauth_token }}
      - name: x-forwarded-for
        value: "{{ _.x_forwarded_for }}"
      - name: x-request-id
        value: "{{ _.x_request_id }}"
      - name: x-source
        value: "{{ _.x_sourcetype }}"
    authentication: {}
    metaSortKey: -1694092295214
    isPrivate: false
    settingStoreCookies: true
    settingSendCookies: true
    settingDisableRenderRequestBody: false
    settingEncodeUrl: true
    settingRebuildPath: true
    settingFollowRedirects: global
    _type: request
  - _id: req_c49b336d55e146a39f1ab42ddd5990bd
    parentId: fld_60b0af028c9a41a980116c8a87e91b73
    modified: 1694093924636
    created: 1694092289322
    url: "{{ _.base_url }}/exclusion/loan/{{ _.loanIdentifier }}"
    name: /exclusion/loan/{loanIdentifier}
    description: ""
    method: GET
    body: {}
    parameters: []
    headers:
      - name: Authorization
        value: Bearer {{ _.oauth_token }}
      - name: x-forwarded-for
        value: "{{ _.x_forwarded_for }}"
      - name: x-request-id
        value: "{{ _.x_request_id }}"
      - name: x-source
        value: "{{ _.x_sourcetype }}"
    authentication: {}
    metaSortKey: -1694092295189
    isPrivate: false
    settingStoreCookies: true
    settingSendCookies: true
    settingDisableRenderRequestBody: false
    settingEncodeUrl: true
    settingRebuildPath: true
    settingFollowRedirects: global
    _type: request
  - _id: req_43693da361e34f15b67891f68b9670d4
    parentId: fld_60b0af028c9a41a980116c8a87e91b73
    modified: 1694093926055
    created: 1694092289320
    url: "{{ _.base_url }}/exclusion/loan/{{ _.loanIdentifier }}/application/{{
      _.applicationId }}"
    name: /exclusion/loan/{loanIdentifier}/application/{applicationId}
    description: ""
    method: GET
    body: {}
    parameters: []
    headers:
      - name: Authorization
        value: Bearer {{ _.oauth_token }}
      - name: x-forwarded-for
        value: "{{ _.x_forwarded_for }}"
      - name: x-request-id
        value: "{{ _.x_request_id }}"
      - name: x-source
        value: "{{ _.x_sourcetype }}"
    authentication: {}
    metaSortKey: -1694092295164
    isPrivate: false
    settingStoreCookies: true
    settingSendCookies: true
    settingDisableRenderRequestBody: false
    settingEncodeUrl: true
    settingRebuildPath: true
    settingFollowRedirects: global
    _type: request
  - _id: req_ac592dbf94ec4874af58fdbba5bf6f1a
    parentId: fld_60b0af028c9a41a980116c8a87e91b73
    modified: 1694093927542
    created: 1694092289317
    url: "{{ _.base_url }}/exclusion/loan/{{ _.loanIdentifier }}/application/{{
      _.applicationId }}/reason/{{ _.reason }}"
    name: /exclusion/loan/{loanIdentifier}/application/{applicationId}/reason/{reason}
    description: ""
    method: GET
    body: {}
    parameters: []
    headers:
      - name: Authorization
        value: Bearer {{ _.oauth_token }}
      - name: x-forwarded-for
        value: "{{ _.x_forwarded_for }}"
      - name: x-request-id
        value: "{{ _.x_request_id }}"
      - name: x-source
        value: "{{ _.x_sourcetype }}"
    authentication: {}
    metaSortKey: -1694092295139
    isPrivate: false
    settingStoreCookies: true
    settingSendCookies: true
    settingDisableRenderRequestBody: false
    settingEncodeUrl: true
    settingRebuildPath: true
    settingFollowRedirects: global
    _type: request
  - _id: req_72ff8991669c4330a181e14b96e0acb3
    parentId: fld_60b0af028c9a41a980116c8a87e91b73
    modified: 1694094921498
    created: 1694092289352
    url: "{{ _.base_url }}/user/{{ _.x_common_id }}/pilot"
    name: /user/{commonId}/pilot
    description: ""
    method: GET
    body: {}
    parameters: []
    headers:
      - name: Authorization
        value: Bearer {{ _.oauth_token }}
      - name: x-forwarded-for
        value: "{{ _.x_forwarded_for }}"
      - name: x-request-id
        value: "{{ _.x_request_id }}"
      - name: x-source
        value: "{{ _.x_sourcetype }}"
    authentication: {}
    metaSortKey: -1694092295120.25
    isPrivate: false
    settingStoreCookies: true
    settingSendCookies: true
    settingDisableRenderRequestBody: false
    settingEncodeUrl: true
    settingRebuildPath: true
    settingFollowRedirects: global
    _type: request
  - _id: req_616c98a688694850bee92412591c9fa4
    parentId: fld_60b0af028c9a41a980116c8a87e91b73
    modified: 1694094958780
    created: 1694092289334
    url: "{{ _.base_url }}/loan/{{ _.loanIdentifier }}/application/{{
      _.applicationId }}/access"
    name: /loan/{loanIdentifier}/application/{applicationIdentifier}/access
    description: ""
    method: GET
    body: {}
    parameters: []
    headers:
      - name: Authorization
        value: Bearer {{ _.oauth_token }}
      - name: x-forwarded-for
        value: "{{ _.x_forwarded_for }}"
      - name: x-request-id
        value: "{{ _.x_request_id }}"
      - name: x-source
        value: "{{ _.x_sourcetype }}"
    authentication: {}
    metaSortKey: -1694092295101.5
    isPrivate: false
    settingStoreCookies: true
    settingSendCookies: true
    settingDisableRenderRequestBody: false
    settingEncodeUrl: true
    settingRebuildPath: true
    settingFollowRedirects: global
    _type: request
  - _id: req_f73d018e732647e5b76469fdce4a6427
    parentId: fld_60b0af028c9a41a980116c8a87e91b73
    modified: 1694095626961
    created: 1694092289315
    url: "{{ _.base_url }}/cache/loan/{{ _.loanIdentifier }}/keywords"
    name: /cache/loan/{loanId}/keywords
    description: ""
    method: DELETE
    body: {}
    parameters:
      - name: key
        disabled: false
        value: LoanDetails
        id: pair_46c732d57acc47e481b455f6d975adf5
      - id: pair_23748facfe4c463bb9a7ce9e6577bba3
        name: key
        value: AmpLoanDetails
        description: ""
    headers:
      - name: Authorization
        value: Bearer {{ _.oauth_token }}
      - name: x-forwarded-for
        value: "{{ _.x_forwarded_for }}"
      - name: x-request-id
        value: "{{ _.x_request_id }}"
      - name: x-source
        value: "{{ _.x_sourcetype }}"
    authentication: {}
    metaSortKey: -1694092295093.6875
    isPrivate: false
    settingStoreCookies: true
    settingSendCookies: true
    settingDisableRenderRequestBody: false
    settingEncodeUrl: true
    settingRebuildPath: true
    settingFollowRedirects: global
    _type: request
  - _id: req_d6588a4012974ed3a44c0e96e8b356eb
    parentId: fld_60b0af028c9a41a980116c8a87e91b73
    modified: 1694095495512
    created: 1694092289312
    url: "{{ _.base_url }}/cache/loan/{{ _.loanIdentifier }}/keywords/{{
      _.cacheKeyword }}"
    name: /cache/loan/{loanId}/keywords/{cacheKeyword}
    description: ""
    method: DELETE
    body: {}
    parameters: []
    headers:
      - name: Authorization
        value: Bearer {{ _.oauth_token }}
      - name: x-forwarded-for
        value: "{{ _.x_forwarded_for }}"
      - name: x-request-id
        value: "{{ _.x_request_id }}"
      - name: x-source
        value: "{{ _.x_sourcetype }}"
    authentication: {}
    metaSortKey: -1694092295085.875
    isPrivate: false
    settingStoreCookies: true
    settingSendCookies: true
    settingDisableRenderRequestBody: false
    settingEncodeUrl: true
    settingRebuildPath: true
    settingFollowRedirects: global
    _type: request
  - _id: req_aa87c54bac934c3ba27e5f9269962a12
    parentId: fld_60b0af028c9a41a980116c8a87e91b73
    modified: 1694094067972
    created: 1694094015498
    url: "{{ _.base_url }}/exclusion/loan/{{ _.loanIdentifier }}/application/{{
      _.applicationId }}/reason/{{ _.reason }}"
    name: /exclusion/loan/{loanIdentifier}/application/{applicationId}/reason/{reason}
    description: ""
    method: DELETE
    body: {}
    parameters: []
    headers:
      - name: Authorization
        value: Bearer {{ _.oauth_token }}
      - name: x-forwarded-for
        value: "{{ _.x_forwarded_for }}"
      - name: x-request-id
        value: "{{ _.x_request_id }}"
      - name: x-source
        value: "{{ _.x_sourcetype }}"
      - name: x-common-id
        value: "{{ _.x_common_id }}"
    authentication: {}
    metaSortKey: -1694092295070.25
    isPrivate: false
    settingStoreCookies: true
    settingSendCookies: true
    settingDisableRenderRequestBody: false
    settingEncodeUrl: true
    settingRebuildPath: true
    settingFollowRedirects: global
    _type: request
  - _id: req_e6560591c5824696bd844a004704140c
    parentId: fld_60b0af028c9a41a980116c8a87e91b73
    modified: 1694095774582
    created: 1694092289344
    url: "{{ _.base_url }}/loan/{{ _.loanIdentifier }}/application/access"
    name: /loan/{loanIdentifier}/application/access
    description: ""
    method: DELETE
    body: {}
    parameters:
      - name: applicationId
        disabled: false
        value: reo
        id: pair_0e2350d676464cd2abd1f0df376ee273
      - name: skipCanClearCheck
        disabled: false
        value: "true"
        id: pair_66f391a33b674680808ba871bd0c77fc
      - id: pair_7d02eb57858d4f29a129a153d1c24bd7
        name: applicationId
        value: AQ
        description: ""
    headers:
      - name: Authorization
        value: Bearer {{ _.oauth_token }}
      - name: x-forwarded-for
        value: "{{ _.x_forwarded_for }}"
      - name: x-request-id
        value: "{{ _.x_request_id }}"
      - name: x-source
        value: "{{ _.x_sourcetype }}"
      - name: x-sourcetype
        value: "{{ _.x_sourcetype }}"
    authentication: {}
    metaSortKey: -1694092295026.5
    isPrivate: false
    settingStoreCookies: true
    settingSendCookies: true
    settingDisableRenderRequestBody: false
    settingEncodeUrl: true
    settingRebuildPath: true
    settingFollowRedirects: global
    _type: request
  - _id: req_07a72cf0029d49c490fe9d1802f74681
    parentId: fld_60b0af028c9a41a980116c8a87e91b73
    modified: 1694095705532
    created: 1694092289336
    url: "{{ _.base_url }}/loan/{{ _.loanIdentifier }}/application/{{
      _.applicationId }}/access"
    name: /loan/{loanIdentifier}/application/{applicationIdentifier}/access
    description: ""
    method: DELETE
    body: {}
    parameters:
      - name: skipCanClearCheck
        disabled: false
        value: "true"
        id: pair_fdbd56e6650f46f88113cac0c81bab27
    headers:
      - name: Authorization
        value: Bearer {{ _.oauth_token }}
      - name: x-forwarded-for
        value: "{{ _.x_forwarded_for }}"
      - name: x-request-id
        value: "{{ _.x_request_id }}"
      - name: x-source
        value: "{{ _.x_sourcetype }}"
      - name: x-sourcetype
        value: "{{ _.x_sourcetype }}"
    authentication: {}
    metaSortKey: -1694092295014
    isPrivate: false
    settingStoreCookies: true
    settingSendCookies: true
    settingDisableRenderRequestBody: false
    settingEncodeUrl: true
    settingRebuildPath: true
    settingFollowRedirects: global
    _type: request
  - _id: req_71cb75b9a7804042b3a7828128f3d210
    parentId: fld_60b0af028c9a41a980116c8a87e91b73
    modified: 1694095005566
    created: 1694092289325
    url: "{{ _.base_url }}/exclusion/loan/{{ _.loanIdentifier }}"
    name: /exclusion/loan/{loanIdentifier}
    description: ""
    method: POST
    body:
      mimeType: application/json
      text: |-
        {
          "appId": "reo",
          "reason": "NotInPilot",
          "comment": "Testing Insomnia Collection"
        }
    parameters: []
    headers:
      - name: Content-Type
        value: application/json
      - name: Authorization
        value: Bearer {{ _.oauth_token }}
      - name: x-forwarded-for
        value: "{{ _.x_forwarded_for }}"
      - name: x-request-id
        value: "{{ _.x_request_id }}"
      - name: x-source
        value: "{{ _.x_sourcetype }}"
      - name: x-common-id
        value: "{{ _.x_common_id }}"
    authentication: {}
    metaSortKey: -1694092294982.75
    isPrivate: false
    settingStoreCookies: true
    settingSendCookies: true
    settingDisableRenderRequestBody: false
    settingEncodeUrl: true
    settingRebuildPath: true
    settingFollowRedirects: global
    _type: request
  - _id: req_6d966fc0817846009c0dcdb02ee0548b
    parentId: fld_60b0af028c9a41a980116c8a87e91b73
    modified: 1694095042014
    created: 1694092289341
    url: "{{ _.base_url }}/loan/{{ _.loanIdentifier }}/application/access"
    name: /loan/{loanIdentifier}/application/access
    description: ""
    method: POST
    body:
      mimeType: application/json
      text: |-
        [
          "AQ", "reo"
        ]
    parameters: []
    headers:
      - name: Content-Type
        value: application/json
      - name: Authorization
        value: Bearer {{ _.oauth_token }}
      - name: x-forwarded-for
        value: "{{ _.x_forwarded_for }}"
      - name: x-request-id
        value: "{{ _.x_request_id }}"
      - name: x-source
        value: "{{ _.x_sourcetype }}"
    authentication: {}
    metaSortKey: -1694092294979.625
    isPrivate: false
    settingStoreCookies: true
    settingSendCookies: true
    settingDisableRenderRequestBody: false
    settingEncodeUrl: true
    settingRebuildPath: true
    settingFollowRedirects: global
    _type: request
  - _id: req_fd049928c3904e79ab688b4b5a42ff80
    parentId: fld_60b0af028c9a41a980116c8a87e91b73
    modified: 1694095058362
    created: 1694092289348
    url: "{{ _.base_url }}/loan/{{ _.loanIdentifier }}/user-access"
    name: /loan/{loanIdentifier}/user-access
    description: ""
    method: POST
    body:
      mimeType: application/json
      text: |-
        [
          "AQ", "reo"
        ]
    parameters: []
    headers:
      - name: Content-Type
        value: application/json
      - name: Authorization
        value: Bearer {{ _.oauth_token }}
      - name: x-forwarded-for
        value: "{{ _.x_forwarded_for }}"
      - name: x-request-id
        value: "{{ _.x_request_id }}"
      - name: x-source
        value: "{{ _.x_sourcetype }}"
      - name: x-common-id
        value: "{{ _.x_common_id }}"
      - name: x-amp-id
        value: "{{ _.x_amp_username }}"
    authentication: {}
    metaSortKey: -1694092294976.5
    isPrivate: false
    settingStoreCookies: true
    settingSendCookies: true
    settingDisableRenderRequestBody: false
    settingEncodeUrl: true
    settingRebuildPath: true
    settingFollowRedirects: global
    _type: request
  - _id: req_5f2ce284ae3c4f3b8a37a8a7665b965e
    parentId: fld_60b0af028c9a41a980116c8a87e91b73
    modified: 1694095258880
    created: 1694092289339
    url: "{{ _.base_url }}/loan/{{ _.loanIdentifier }}/application/{{
      _.applicationId }}/access"
    name: /loan/{loanIdentifier}/application/{applicationIdentifier}/access
    description: ""
    method: PUT
    body: {}
    parameters:
      - name: decision
        disabled: false
        value: "false"
        id: pair_2661abb84dde4f77af3a5f27f872a700
      - id: pair_abb02c548e124cb0b1e1fc7cc6a3c8e3
        name: ""
        value: ""
        description: ""
    headers:
      - name: Authorization
        value: Bearer {{ _.oauth_token }}
      - name: x-forwarded-for
        value: "{{ _.x_forwarded_for }}"
      - name: x-request-id
        value: "{{ _.x_request_id }}"
      - name: x-source
        value: "{{ _.x_sourcetype }}"
      - name: x-sourcetype
        value: "{{ _.x_sourcetype }}"
    authentication: {}
    metaSortKey: -1694092294889
    isPrivate: false
    settingStoreCookies: true
    settingSendCookies: true
    settingDisableRenderRequestBody: false
    settingEncodeUrl: true
    settingRebuildPath: true
    settingFollowRedirects: global
    _type: request
  - _id: env_6389d29a7f0b4fc293ee2d443d9fee1b
    parentId: wrk_413cd17d44b8449e89de05106561f3bd
    modified: 1694093797937
    created: 1694049936777
    name: Base Environment
    data:
      oauth_token: "{% response 'body', 'req_badd4551cfd74cb69d3dd250d1ea10f0',
        'b64::JC5hY2Nlc3NfdG9rZW4=::46b', 'always', 60 %}"
      x_forwarded_for: "{% os 'hostname', '' %}"
      x_sourcetype: insomnia
      x_request_id: "{% uuid 'v4' %}"
    dataPropertyOrder:
      "&":
        - oauth_token
        - x_forwarded_for
        - x_sourcetype
        - x_request_id
    color: null
    isPrivate: false
    metaSortKey: 1694049936777
    _type: environment
  - _id: jar_b462e356df6a408890de31e3a3d7f54d
    parentId: wrk_413cd17d44b8449e89de05106561f3bd
    modified: 1694051623593
    created: 1694049936780
    name: Default Jar
    cookies:
      - key: did
        value: s%3Av0%3A57809c50-4d21-11ee-bbdd-1fdbdfae900f.Jqm%2BiAvq6TG%2F0pSqh1AiC%2FZ48AKg%2F39hxMtbMlyboGQ
        expires: 2024-09-06T07:53:29.000Z
        maxAge: 31557600
        domain: sso.test.authrock.com
        path: /
        secure: true
        httpOnly: true
        hostOnly: true
        creation: 2023-09-07T01:53:29.863Z
        lastAccessed: 2023-09-07T01:53:29.863Z
        sameSite: none
        id: "33681850583720463"
      - key: did_compat
        value: s%3Av0%3A57809c50-4d21-11ee-bbdd-1fdbdfae900f.Jqm%2BiAvq6TG%2F0pSqh1AiC%2FZ48AKg%2F39hxMtbMlyboGQ
        expires: 2024-09-06T07:53:29.000Z
        maxAge: 31557600
        domain: sso.test.authrock.com
        path: /
        secure: true
        httpOnly: true
        hostOnly: true
        creation: 2023-09-07T01:53:29.863Z
        lastAccessed: 2023-09-07T01:53:29.863Z
        id: "7802905960659987"
      - key: did
        value: s%3Av0%3A5af26a80-4d21-11ee-bbdd-1fdbdfae900f.fISX5UrtSsbGlzZ0AlDpK8ohyr8HFvnqMoDcQRxqSJk
        expires: 2024-09-06T07:53:35.000Z
        maxAge: 31557600
        domain: sso.beta.authrock.com
        path: /
        secure: true
        httpOnly: true
        hostOnly: true
        creation: 2023-09-07T01:53:35.655Z
        lastAccessed: 2023-09-07T01:53:35.655Z
        sameSite: none
        id: "7961616935966407"
      - key: did_compat
        value: s%3Av0%3A5af26a80-4d21-11ee-bbdd-1fdbdfae900f.fISX5UrtSsbGlzZ0AlDpK8ohyr8HFvnqMoDcQRxqSJk
        expires: 2024-09-06T07:53:35.000Z
        maxAge: 31557600
        domain: sso.beta.authrock.com
        path: /
        secure: true
        httpOnly: true
        hostOnly: true
        creation: 2023-09-07T01:53:35.655Z
        lastAccessed: 2023-09-07T01:53:35.655Z
        id: "06545092017266119"
      - key: did
        value: s%3Av0%3A5fb08ed0-4d21-11ee-92c3-89cddbc4e742.OaXkmjCPKejTmwR3l0NfWeeymk6YcGlkr0Owb4BwXRc
        expires: 2024-09-06T07:53:42.000Z
        maxAge: 31557600
        domain: sso.authrock.com
        path: /
        secure: true
        httpOnly: true
        hostOnly: true
        creation: 2023-09-07T01:53:43.593Z
        lastAccessed: 2023-09-07T01:53:43.593Z
        sameSite: none
        id: "16443943280497475"
      - key: did_compat
        value: s%3Av0%3A5fb08ed0-4d21-11ee-92c3-89cddbc4e742.OaXkmjCPKejTmwR3l0NfWeeymk6YcGlkr0Owb4BwXRc
        expires: 2024-09-06T07:53:42.000Z
        maxAge: 31557600
        domain: sso.authrock.com
        path: /
        secure: true
        httpOnly: true
        hostOnly: true
        creation: 2023-09-07T01:53:43.593Z
        lastAccessed: 2023-09-07T01:53:43.593Z
        id: "14789892687530326"
    _type: cookie_jar
  - _id: env_0edbb70b429d4a58bc324c189783eccf
    parentId: env_6389d29a7f0b4fc293ee2d443d9fee1b
    modified: 1694091782679
    created: 1694050141435
    name: Local Template
    data:
      oauth_url: https://sso.test.authrock.com/oauth/token
      oauth_audience: urn:ql-api:loan_access_authorizer-202723:Test
      oauth_clientid: ""
      oauth_clientsecret: ""
      base_url: http://localhost:8883
    dataPropertyOrder:
      "&":
        - oauth_url
        - oauth_audience
        - oauth_clientid
        - oauth_clientsecret
        - base_url
    color: null
    isPrivate: false
    metaSortKey: 1694050141435
    _type: environment
  - _id: env_8bdba36a51724181a5838efebf9fa27a
    parentId: env_6389d29a7f0b4fc293ee2d443d9fee1b
    modified: 1694091702851
    created: 1694050142499
    name: Test Template
    data:
      oauth_url: https://sso.test.authrock.com/oauth/token
      oauth_audience: urn:ql-api:loan_access_authorizer-202723:Test
      oauth_clientid: ""
      oauth_clientsecret: ""
      base_url: https://api.loan-access-authorizer.test.mortgageops.foc.zone
    dataPropertyOrder:
      "&":
        - oauth_url
        - oauth_audience
        - oauth_clientid
        - oauth_clientsecret
        - base_url
    color: null
    isPrivate: false
    metaSortKey: 1694050142499
    _type: environment
  - _id: env_11e9f805e6334581bd7486d269da60d9
    parentId: env_6389d29a7f0b4fc293ee2d443d9fee1b
    modified: 1694091819662
    created: 1694050143506
    name: Beta Template
    data:
      oauth_url: https://sso.beta.authrock.com/oauth/token
      oauth_audience: urn:ql-api:loan_access_authorizer-202723:Beta
      oauth_clientid: ""
      oauth_clientsecret: ""
      base_url: https://api.loan-access-authorizer.beta.mortgageops.foc.zone
    dataPropertyOrder:
      "&":
        - oauth_url
        - oauth_audience
        - oauth_clientid
        - oauth_clientsecret
        - base_url
    color: null
    isPrivate: false
    metaSortKey: 1694050143506
    _type: environment
  - _id: env_a8f55ca69d28406baf9c01769c14ab29
    parentId: env_6389d29a7f0b4fc293ee2d443d9fee1b
    modified: 1694091824894
    created: 1694050144497
    name: Train Template
    data:
      oauth_url: https://sso.beta.authrock.com/oauth/token
      oauth_audience: urn:ql-api:loan_access_authorizer-202723:Beta
      oauth_clientid: ""
      oauth_clientsecret: ""
      base_url: https://api.loan-access-authorizer-train.beta.mortgageops.foc.zone
    dataPropertyOrder:
      "&":
        - oauth_url
        - oauth_audience
        - oauth_clientid
        - oauth_clientsecret
        - base_url
    color: null
    isPrivate: false
    metaSortKey: 1694050144497
    _type: environment
  - _id: env_44a6ca3bd1fe409790de7cc0bdfaba5d
    parentId: env_6389d29a7f0b4fc293ee2d443d9fee1b
    modified: 1694091862882
    created: 1694050145435
    name: Prod Template
    data:
      oauth_url: https://sso.authrock.com/oauth/token
      oauth_audience: urn:ql-api:loan_access_authorizer-202723:Prod
      oauth_clientid: ""
      oauth_clientsecret: ""
      base_url: https://api.loan-access-authorizer.mortgageops.foc.zone
    dataPropertyOrder:
      "&":
        - oauth_url
        - oauth_audience
        - oauth_clientid
        - oauth_clientsecret
        - base_url
    color: null
    isPrivate: false
    metaSortKey: 1694050145435
    _type: environment
