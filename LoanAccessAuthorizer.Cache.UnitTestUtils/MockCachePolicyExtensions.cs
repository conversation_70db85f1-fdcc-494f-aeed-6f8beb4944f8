using DecisionServices.Core.Cache;
using Moq;

namespace LoanAccessAuthorizer.Cache.UnitTestUtils;

public static class MockCachePolicyExtensions
{
    public static void SetupFetchValue<T>(this Mock<CachePolicy<T>> mockCachePolicy, Cache<PERSON><PERSON> key)
    {
        mockCachePolicy.Setup(cache => cache.Execute(
            It.IsAny<Func<Task<T>>>(),
            key,
            It.IsAny<Func<T, bool>>(),
            null))
            .Returns(async (Func<Task<T>> fetchValue, CacheKey _, Func<T, bool> _, dynamic _) =>
            {
                return await fetchValue();
            });
    }
}
