using DecisionServices.Core.Cache;
using Moq;

namespace LoanAccessAuthorizer.Cache.UnitTestUtils;

public static class MockRepositoryExtensions
{
    public static Mock<CachePolicyFactory> CreateCachePolicyFactory(
        this MockRepository mockRepository)
    {
        var mockServiceProvider = mockRepository.Create<IServiceProvider>(MockBehavior.Loose);
        return mockRepository.Create<CachePolicyFactory>(mockServiceProvider.Object);
    }
}
